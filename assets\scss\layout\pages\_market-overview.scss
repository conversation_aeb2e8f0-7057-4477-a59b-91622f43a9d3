@use '../../utils' as *;

/*----------------------------------------*/
/*  Trends panel styles
/*----------------------------------------*/
.trends-panel-card {
    padding: 18px;
    background: #0A1729;
    border-radius: 30px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;

    @media #{$xxs} {
        padding: 16px;
    }

    &::before {
        position: absolute;
        content: "";
        width: 101px;
        height: 102px;
        right: -30px;
        right: -30px;
        bottom: -28px;
        background-color: #1F4A55;
        filter: blur(70px);
    }

    .heading-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #142032;
        border-radius: 12px;
        padding: 10px 10px;
        height: 50px;
        margin-bottom: 16px;

        .heading-left {
            display: flex;
            gap: 6px;

            img {
                width: 25px;
                height: 25px;
            }
        }
    }

    .market-trends-panel {
        .td-table {
            min-width: 350px;
        }
    }
}

.timeframe-select {
    background-color: #27313d;
    color: white;
    padding: 3px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    appearance: none;
    outline: none;
    border: 1px solid rgba($white, $alpha: 0.1);
    border-radius: 6px;

    &:hover {
        background-color: #374b70;
    }
}

.market-overview-filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-search-bar {
    position: relative;
    width: 237px;

    input {
        border: none;
        color: var(--td-white);
        width: 100%;
        background: #142032;
        border-radius: 8px;
        padding: 5px 16px 5px 34px;
        height: 40px;
        font-size: 14px;
        padding-left: 36px;

        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }

    .icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 15px;
        display: inline-flex
    }
}

.market-overview-tab {
    &.td-tab {
        .nav-tabs {
            gap: 12px;

            .nav-link {
                height: 36px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 18px 18px;
                border-radius: 30px;
                font-size: 18px;
                font-weight: 700;
                color: rgba($white, $alpha: 0.6);
                background-color: transparent !important;
                position: relative;
                z-index: 1;

                &::before {
                    position: absolute;
                    content: "";
                    height: 100%;
                    width: 100%;
                    top: 0;
                    left: 0;
                    background-color: rgba($black, $alpha: 0.5);
                    border-radius: 30px;
                    z-index: -1;
                }

                &.active {
                    &::before {
                        background-color: rgba($black, $alpha: 0);
                    }
                }
            }
        }
    }
}