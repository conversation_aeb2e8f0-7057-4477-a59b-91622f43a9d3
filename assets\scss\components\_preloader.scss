@use '../utils' as *;

/*----------------------------------------*/
/* Preloader styles
/*----------------------------------------*/

.preloader {
    background: var(--td-white);
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 9999999;
    top: 0;
    inset-inline-start: 0;
    @include gridbox();
    place-items: center;

    @include dark-theme {
        background: var(--td-heading);
    }

    .spinner {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        border: 9px solid var(--td-primary);
        animation: spinner-bulqg1 0.8s infinite linear alternate,
            spinner-oaa3wk 1.6s infinite linear;
    }

    @keyframes spinner-bulqg1 {
        0% {
            clip-path: polygon(50% 50%, 0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
        }

        12.5% {
            clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
        }

        25% {
            clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%);
        }

        50% {
            clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
        }

        62.5% {
            clip-path: polygon(50% 50%, 100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
        }

        75% {
            clip-path: polygon(50% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%);
        }

        100% {
            clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%);
        }
    }

    @keyframes spinner-oaa3wk {
        0% {
            transform: scaleY(1) rotate(0deg);
        }

        49.99% {
            transform: scaleY(1) rotate(135deg);
        }

        50% {
            transform: scaleY(-1) rotate(0deg);
        }

        100% {
            transform: scaleY(-1) rotate(-135deg);
        }
    }
}



#td-loadingDiv {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    z-index: 999;
    background-color: rgb(29, 29, 29);
    display: none;
}

.td-loading-wrapper {
    width: 360px;
    height: 62px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    overflow: hidden;
    margin-top: -5px;
}

.td-loading {
    height: 8px;
    border-radius: 10px;
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    position: relative;
}

.td-loading-overlay {
    width: 0;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgb(137, 86, 255) 0%, rgb(70, 98, 242) 100%);
    transition: width 3s;
    border-radius: 10px;
}
