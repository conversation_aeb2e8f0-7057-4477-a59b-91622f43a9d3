@use "../utils" as *;

/*----------------------------------------*/
/* Buttons styles
/*----------------------------------------*/
.btn-wrap {
    @include flexbox();
    align-items: center;
    gap: 15px 15px;
    flex-wrap: wrap;
}

.td-btn {
    padding: 0 26px;
    background: var(--td-primary);
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    @include border-radius(8px);
    @include inline-flex();
    align-items: center;
    justify-content: center;
    height: 50px;
    color: var(--td-heading);
    font-size: 16px;
    font-weight: 600;
    gap: 8px;

    .btn-icon {
        display: flex;
        align-items: center;

        i {
            transition: .3s;
        }
    }

    &:focus {
        color: var(--td-heading);
    }

    &:hover {
        background-color: #509415;
        color: var(--td-heading);
        transform: translate3d(0, -2px, 0);
    }

    // outline btn
    &.btn-primary-outline {
        border: 2px solid rgba(166, 239, 103, 0.4);
        background-color: transparent;
        color: var(--td-white);

        &:hover {
            background-color: var(--td-heading);
            color: var(--td-white);
        }
    }

    &.outline-white-btn {
        background: var(--td-white);
        border: 1px solid #DBDBDB;
        color: var(--td-heading);
        gap: 8px;

        span {
            svg {
                width: 20px;
                height: 20px;
            }
        }

        &:hover {
            background-color: var(--td-heading);
            border-color: var(--td-heading);
            color: var(--td-white);
        }
    }

    &.outline-danger-btn {
        border: 1px solid var(--td-danger);
        background-color: transparent;
        color: var(--td-danger);
        font-weight: 500;
    }

    &.outline-black-btn {
        border: 1px solid rgba($heading, $alpha: 0.6);
        color: rgba($heading, $alpha: 0.6);
        background-color: transparent;
    }

    &.outline-black-fill-btn {
        background: #010c1a;
        border: 1px solid rgba(255, 255, 255, 0.16);
        color: var(--td-white);
    }

    &.outline-auth-btn {
        border: 1px solid #212a3d;
        color: rgba($white, $alpha: 0.6);
        background-color: transparent;

        &:hover {
            background-color: var(--td-secondary);
            color: var(--td-white);
            border-color: var(--td-secondary);
        }
    }

    &.btn-outline-dark-gunmetal {
        background-color: #142032;
        color: var(--td-primary);
    }

    &.btn-outline-white-10 {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: transparent;
        color: var(--td-text-primary);
    }

    // Default btns
    &.btn-primary {
        background: var(--td-primary-alt);
        color: var(--td-white);

        span {
            svg * {
                width: 16px;
                height: 16px;
                stroke: var(--td-white);
            }
        }

        &:hover {
            background: var(--td-primary);
        }
    }

    &.danger-btn {
        background: var(--td-danger);
        color: var(--td-white);

        span {
            svg * {
                stroke: var(--td-white);
            }
        }
    }

    &.btn-white {
        background-color: var(--td-white);
        color: var(--td-heading);

        &:hover {
            background-color: var(--td-primary);
            color: var(--td-white);
        }
    }

    &.btn-gray {
        background: var(--td-alice-blue);
        border: 1px solid rgba(171, 178, 225, 0.3);
        @include border-radius(8px);
        color: var(--td-heading);

        .td-btn.btn-gray {
            background: rgba(255, 255, 255, 0.2);
            @include border-radius(8px);
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--td-white);
        }
    }

    &.btn-dark {
        background: #010c1a;
        border: 1px solid rgba(255, 255, 255, 0.16);
        color: var(--td-white);
    }

    &.btn-secondary {
        background: linear-gradient(180deg, #73A8F8 0%, #3B57E7 100%);
        ;
        color: var(--td-white);

        &:hover {
            background: linear-gradient(180deg, #73A8F8 0%, #3B57E7 100%);
            ;
            color: var(--td-white);
        }
    }

    // Has underline
    &.has-underline {
        background-color: transparent;
        height: auto;
        padding: 0;

        .btn-text {
            color: var(--td-heading);

            &::after {
                position: absolute;
                content: "";
                inset-inline-start: auto;
                bottom: -2px;
                background: currentColor;
                width: 0;
                height: 2px;
                transition: 0.3s;
                inset-inline-end: 0;
            }
        }

        .btn-icon {
            width: 22px;
            height: 22px;
            background-color: var(--td-primary);
            @include inline-flex();
            align-items: center;
            justify-content: center;
            border-radius: 50%;

            svg * {
                stroke: var(--td-white);
            }
        }

        &:hover {
            .btn-text {
                &::after {
                    width: 100%;
                    inset-inline-start: 0;
                    inset-inline-end: auto;
                }
            }
        }
    }

    // Size Variation
    &.btn-xs {
        padding: 0 16px;
        height: 30px;
        font-size: 12px;
        gap: 4px;

        .btn-icon {
            i {
                width: 14px;
                height: 14px;
            }
        }
    }

    &.btn-h-40 {
        height: 40px;
        padding: 0 15px;
        font-weight: 500;
        font-size: 14px;
        gap: 4px;
    }

    &.btn-h-36 {
        height: 36px;
        padding: 0 15px;
        font-weight: 500;
        font-size: 14px;
        gap: 4px;
    }

    &.btn-sm {
        padding: 0 22px;
        height: 44px;
        gap: 6px;
        font-size: 14px;

        svg {
            width: 16px;
            height: 16px;
        }

        @media #{$xs,$sm} {
            padding: 0 12px;
            height: 36px;
            gap: 6px;
            font-size: 14px;
            gap: 2px;
        }
    }

    &.btn-md {
        padding: 0 25px;
        font-size: var(--font-size-b3);
        height: 50px;

        @media #{$sm} {
            padding: 0 15px;
        }
    }

    &.btn-lg {
        height: 70px;
        padding: 0 35px;
        font-size: 18px;

        @media #{$sm} {
            padding: 0 17px;
        }
    }

    &.btn-xl {
        padding: 0 45px;
        font-size: 20px;
        height: 75px;

        @media #{$sm} {
            padding: 0 20px;
            font-size: 16px;
            height: 55px;
        }
    }

    &.btn-xxl {
        padding: 0 60px;
        font-size: 22px;
        height: 100px;
        line-height: 100px;

        @media #{$sm} {
            padding: 0 20px;
            font-size: 18px;
            height: 70px;
        }
    }
}

// underline btn
.td-underline-btn {
    font-weight: 500;
    position: relative;
    color: var(--td-primary);
    font-size: 14px;

    &::after {
        content: "";
        position: absolute;
        height: 1px;
        transition: .3s;
        left: auto;
        bottom: -2px;
        background: var(--td-primary);
        width: 0;
        right: 0;
    }

    &:hover {
        color: var(--td-primary);

        &::after {
            width: 100%;
            left: 0;
            right: auto;
        }
    }
}

// chat button
.chat-button {
    background: #1d2939;
    border-radius: 6px;
    padding: 0px 6px 0px 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    i {
        width: 14px;
        height: 14px;
    }
}