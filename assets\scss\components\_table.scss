@use '../utils' as *;

/*----------------------------------------*/
/*  Table styles
/*----------------------------------------*/

// table container
.table-container {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.06);
    padding: 16px 0px 16px;
    border-radius: 24px;

    &.has-eerie-black {
        background-color: var(--td-eerie-black);
    }

    .table-heading {
        margin-bottom: 20px;
        padding: 0 16px;

        .title {
            font-size: 20px;
        }
    }
}

// table description
.table-description {
    display: flex;
    align-items: center;
    gap: rem(10);

    .icon {
        width: 44px;
        height: 44px;
        background: rgba(3, 166, 109, 0.1);
        border-radius: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 0 0 auto;

        span {
            display: inline-flex;
            align-items: center;
        }
    }

    .contents {
        .title {
            font-size: rem(14);
        }

        .date {
            font-size: rem(12);
        }
    }

    &.is-primary-10 {
        .icon {
            background: rgba($primary, $alpha: 0.10);
            color: var(--td-danger);
        }
    }

    &.is-danger-10 {
        .icon {
            background: rgba($danger, $alpha: 0.10);
            color: var(--td-danger);
        }
    }
}

// Table currency
.table-currency-info {
    display: flex;
    align-items: center;
    gap: rem(10);

    .thumb {
        width: 20px;
    }

    .name {
        font-size: 14px;
    }
}

// common table
.td-table {
    width: 100%;
    border-collapse: separate; // Allows border-radius to work
    border-spacing: 0; // Removes spacing between table cells
    font-size: 14px;

    &.recent-order-table {
        min-width: 900px;
    }

    &.recent-table {
        min-width: 980px;
    }

    thead {
        background: rgba(255, 255, 255, 0.08);
        @include border-radius(8px);
        overflow: hidden; // Ensures border-radius applies in Firefox

        tr {
            th {
                border-inline-start: 0;
                border-inline-end: 0;
                padding: 12px 16px;
                text-align: left;
                font-weight: 600;
            }
        }
    }

    th {
        text-align: left;
        padding: 14px 16px;
        font-weight: 600;
        color: #999999;
    }

    td {
        text-align: left;
        padding: 13px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.06);
        font-weight: 500;
    }

    tr {
        &:last-child {
            td {
                border-bottom: none;
            }
        }
    }

    tbody {
        tr {
            &:hover {
                // background: rgba(255, 255, 255, 0.08);
            }
        }
    }

    .no-data-found {
        text-align: center;
        padding: rem(60) rem(40);
        margin: 0 auto;

        img {
            width: 50px;
            margin-bottom: 5px;
        }

        span {
            font-size: 16px;
            letter-spacing: 0.03em;
            font-weight: 700;
            display: block;
        }
    }

    &.table-currency {
        td {
            border-bottom: 0;
            padding: 10px 10px;
            &:last-child {
                text-align: end;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }
            &:first-child {
                text-align: end;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }
        }

        tr {
            &:hover {
                background: #142032;
            }
        }
    }
}

// Filter bar
.filter-bar {
    padding: 0 16px 16px;
    display: grid;
    gap: 16px;
    align-items: center;
    grid-template-columns: repeat(auto-fit, minmax(236px, 1fr));

    .filter-bar-search {
        position: relative;

        .input-box {
            background-color: transparent;
            padding: 0px 16px 0px 16px;
            border: 1px solid rgba($white, $alpha: 0.1);
            height: 44px;
            padding-left: 40px;
            color: #999;
            font-weight: 700;
            font-size: 14px;

            @include td-placeholder {
                color: #999;
                font-weight: 700;
                font-size: 14px;
            }
        }

        .search-icon {
            position: absolute;
            top: 50%;
            left: 16px;
            transform: translateY(-50%);
        }
    }

    .td-form-group {
        &.has-right-icon .input-icon i {
            width: 18px;
            height: 18px;
        }

        .input-field {
            .select2-container--default .select2-selection--single {
                height: 44px;
                line-height: 44px;
            }

            input {
                background-color: transparent;
                padding: 0px 16px 0px 16px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                height: 44px;
                color: #999;
                font-weight: 700;
                font-size: 14px;
            }
        }
    }
}

// Review box
.review-box {
    margin-top: 20px;

    .review-btn {
        border: none;
        color: #fff;
        background: rgba(255, 255, 255, 0.04);
        border-radius: 12px;
        border-style: solid;
        border-color: transparent;
        border-width: 0px 0px 1px 0px;
        padding: 7px 20px 7px;
    }

    .review-table {
        width: 100%;

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            font-size: rem(14);
            padding: rem(12) 0;
        }

        th {
            color: #999;

        }

        td {
            text-align: end;
        }
    }

    .amount {
        color: var(--td-green);
    }

    .charge {
        color: var(--td-danger);
    }

    .method,
    .rate {
        color: var(--td-white);
    }

    .email {
        color: #6780ff;
        font-weight: 700;

        a {
            text-decoration: underline;

            &:hover {
                text-decoration: none;
            }
        }
    }

    .total-box {
        margin-top: 8px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 18px;
        display: flex;
        align-content: center;
        justify-content: space-between;

        .total-text {
            font-size: 14px;
            display: block;
            font-weight: 700;
            margin-bottom: rem(4);
        }
    }

    .buttons {
        display: flex;
        align-items: center;
        justify-content: end;
        gap: rem(16);
        margin-top: rem(40);
    }
}

// bank info
.table-bank-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .thumb {
        width: 40px;
        height: 40px;
    }

    .contents {
        .designation {
            font-size: 14px;
            margin-bottom: 5px;
            display: block;
        }

        .title {
            font-size: 16px;
        }
    }
}

// Table actions
.table-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .icon {
        background: var(--td-primary);
        ;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        color: var(--td-white);

        &.is-green {
            background: var(--td-green)
        }
        &.is-outline {
            border: 1px solid #999;
            background-color: transparent;
            color: #999;
        }
    }

    .text {
        background: var(--td-primary);
        border-radius: 8px;
        padding: 7px 16px 7px 16px;
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: center;
        position: relative;
        font-size: 14px;
        font-weight: 700;
        color: var(--td-white);
        line-height: 1;

        &.is-danger {
            background: #eb4e5c;
        }
        &.is-primary {
            color: var(--td-heading);
            background: var(--td-primary)
        }
    }
}