@use '../../utils' as *;

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/

// order panel
.trade-order-panel {
    position: relative;
    z-index: 1;
    border-radius: 8px;
    padding: 8px;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(8px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}

// order type tab
.order-type-tab {
    .nav-tabs {
        display: flex;
        column-gap: 12px;
        background: #091628;
        border-radius: 4px;
        padding: 10px;

        .nav-link {
            font-size: 14px;
            line-height: 16px;
            font-weight: 700;
            background-color: rgba(0, 0, 0, 0);
            color: #999;
            display: flex;
            align-items: center;

            &.active {
                border-bottom: 1px solid var(--td-primary);
                padding-bottom: 2px;
                color: var(--td-white);
            }
        }
    }
}

// order tabs 
.order-tabs {
    .nav-tabs {
        display: flex;
        column-gap: 12px;
        margin-top: 2px;
        padding: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid hsla(0, 0%, 100%, .1);
        border-radius: 0;

        .nav-link {
            font-size: 14px;
            line-height: 16px;
            font-weight: 700;
            background-color: rgba(0, 0, 0, 0);
            color: #999;
            display: flex;
            align-items: center;

            &.active {
                border-bottom: 1px solid var(--td-primary);
                padding-bottom: 2px;
                color: var(--td-white);
            }
        }
    }
}

.order-form {
    display: flex;
    gap: 20px;

    @media #{$xl} {
        gap: 16px;
    }

    @media #{$xs} {
        flex-direction: column;
    }

    .divider-line {
        border: 1px solid rgba($white, $alpha: 0.1);
    }
}

.order-box {
    flex: 1;
}

.order-info {
    margin-bottom: 16px;

    p {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba($white, $alpha: 0.6);
        font-size: 12px;
        font-weight: 700;

        &:not(:last-child) {

            margin-bottom: 10px;
        }

        span {
            color: var(--td-white);
        }
    }
}

.percentage-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 25px;
    margin-top: 20px;

    &.has-danger {
        button {
            &.active {
                background: #eb4e5c;
            }
        }
    }

    button {
        background: rgba(255, 255, 255, 0.04);
        color: var(--td-white);
        border-radius: 4px;
        padding: 0 16px;
        display: flex;
        gap: 10px;
        align-items: center;
        justify-content: center;
        height: 24px;
        position: relative;
        font-size: 12px;
        font-weight: 700;
        min-width: 74px;
    }

    button.active {
        background: #10b981;
    }
}

.buy-btn {
    background: #03a66d;
    border-radius: 6px;
    padding: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    align-self: stretch;
    flex-shrink: 0;
    height: 44px;
    width: 100%;
    color: var(--td-white);
}

.sell-btn {
    background: #eb4e5c;
    border-radius: 6px;
    padding: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    align-self: stretch;
    height: 44px;
    color: var(--td-white);
}