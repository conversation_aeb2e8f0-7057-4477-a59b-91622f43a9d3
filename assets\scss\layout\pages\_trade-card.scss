@use '../../utils' as *;

/*----------------------------------------*/
/*  Trade card styles
/*----------------------------------------*/

.trade-card {
    position: relative;
    z-index: 1;
    background: #071220;
    border-radius: 8px;
    padding-bottom: 8px;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(8px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}

.trade-card-heading {
    padding: 8px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 41px;
    display: flex;
    align-items: center;
}