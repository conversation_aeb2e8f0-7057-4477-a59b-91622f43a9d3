@use '../../utils' as *;

/*----------------------------------------*/
/*  Trading platform styles
/*----------------------------------------*/

// trading workspace tab 
.trading-workspace-tab {
    .nav-tabs {
        display: inline-flex;
        column-gap: 12px;
        row-gap: 12px;
        padding: 10px;
        border-bottom: 1px solid hsla(0, 0%, 100%, .1);
        border-radius: 0;

        .nav-link {
            font-size: 14px;
            line-height: 16px;
            font-weight: 700;
            background-color: transparent;
            color: #999;
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 1px solid transparent;

            &.active {
                border-color: var(--td-primary);
                color: var(--td-white);
            }
        }
    }
}

.trading-platform {
    position: relative;
    padding: 12px 24px 16px;
    background: #071220;
    border-radius: 8px;
    z-index: 1;

    @media #{$xs} {
        padding: 12px 12px 12px;
    }

    .workspace-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 12px 20px;
    }

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(10px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }

    .trades-table {
        width: 100%;
        border-collapse: collapse;

        thead {
            .table-sorting-arrow {
                position: relative;
                top: 3px;

                i {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        th {
            font-weight: 700;
            text-align: left;
            padding: 4px 6px;
            border-bottom: 1px solid hsla(0, 0%, 100%, .1);
            font-size: 12px;
        }

        td {
            padding: 6px 6px;
            border-bottom: 1px solid hsla(0, 0%, 100%, .1);
            font-size: 12px;
            font-weight: 500;

            &:first-child {
                border-left: 1px solid rgba($white, $alpha: 0.1);
            }

            &:last-child {
                border-right: 1px solid rgba($white, $alpha: 0.1);
            }

        }

        .trade-id {
            color: var(--td-white);
        }

        .profit-negative {
            color: var(--td-danger);
        }

        .close-button {
            background: none;
            color: var(--td-white);
            cursor: pointer;
            padding: 4px;
            font-size: 20px;
            width: 28px;
            height: 28px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center
        }

        .value-zero {
            position: relative;

            .btn-inner {
                display: flex;
                align-items: center;
                column-gap: 6px;

                .text {
                    color: var(--td-white);
                }

                button {
                    width: 16px;
                    height: 16px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 2px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .security-order-container {
                width: 280px;
                background-color: #091628;
                border-radius: 8px;
                padding: 10px 10px;
                color: var(--td-white);
                box-shadow: 0px 2px 10px rgba(0, 0, 0, .5);
                position: absolute;
                bottom: 100%;
                display: none;

                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    line-height: 1;
                }

                .header .close-btn {
                    cursor: pointer;
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }

                .content {
                    margin-top: 15px;
                }

                .input-row {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    gap: 6px 6px;
                }

                .input-row button {
                    background-color: #3d404e;
                    border: none;
                    color: var(--td-white);
                    font-size: 18px;
                    padding: 5px;
                    cursor: pointer;
                    border-radius: 4px;
                    width: 30px;
                    height: 30px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }

                .input-value {
                    background-color: #252734;
                    color: var(--td-white);
                    border: none;
                    text-align: center;
                    width: 130px;
                    padding: 8px;
                    border-radius: 4px;
                    flex-grow: 1;
                    height: 30px;
                    font-size: 0.75rem;
                }

                .label {
                    font-size: 12px;
                    color: #a0a3b1;
                    margin-bottom: 10px;
                    display: block;
                }

                .percent-row {
                    display: flex;
                    justify-content: space-between;
                    font-size: 12px;
                    color: #c0c4d3;
                    padding: 10px 0;
                    border-top: 1px solid #3d404e;
                    margin-top: 10px;
                }

                .footer {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 10px;
                }

                .back-btn {
                    background-color: #3d404e;
                    border-radius: 4px;
                    padding: 0 18px;
                    font-size: 12px;
                    height: 32px;

                    &:focus {
                        color: var(--td-white);
                    }
                }

                .save-btn {
                    background-color: var(--td-success);
                    color: var(--td-white);
                    border-radius: 4px;
                    padding: 0 18px;
                    font-size: 12px;
                    height: 32px;

                    &:focus {
                        color: var(--td-white);
                    }
                }
            }
        }
    }
}

.expand-switch {
    display: flex;
    align-items: center;
    column-gap: 6px;

    .text {
        font-size: 12px;
        font-weight: 600;
    }

    .icon {
        display: flex;
        align-items: center;

        i {
            width: 18px;
            height: 18px;
        }
    }
}
// user-not-sign

.user-not-sign {
    display: grid;
    place-items: center;
    padding: 70px 0;
}