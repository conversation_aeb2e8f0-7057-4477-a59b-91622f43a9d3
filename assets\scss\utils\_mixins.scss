@use "sass:selector";

@mixin td-root($child) {
    @at-root #{selector.unify(&, $child)} {
        @content;
    }
}

// Rtl
@mixin rtl {
    [dir=rtl] & {
        @content;
    }
}

// Mood switch light
@mixin dark-theme {
    .dark-theme & {
        @content;
    }
}

// Bg color
@mixin bg-color($value, $opacity) {
    background-color: rgba($color: $value, $alpha: $opacity);
}

// Placeholder input
@mixin td-placeholder {
    &::-webkit-input-placeholder {
        /* Chrome/Opera/Safari */
        @content;
    }

    &::-moz-placeholder {
        /* Firefox 19+ */
        @content;
    }

    &:-moz-placeholder {
        /* Firefox 4-18 */
        @content;
    }

    &:-ms-input-placeholder {
        /* IE 10+  Edge*/
        @content;
    }

    &::placeholder {
        /* MODERN BROWSER */
        @content;
    }
}

// Gradient 
@mixin td-gradient($value, $type : "linear") {
    @if $type ==linear {
        background-image: -webkit-linear-gradient($value);
        background-image: -moz-linear-gradient($value);
        background-image: -ms-linear-gradient($value);
        background-image: -o-linear-gradient($value);
        background-image: linear-gradient($value);
    }

    @else {
        background-image: -webkit-radial-gradient($value);
        background-image: -moz-radial-gradient($value);
        background-image: -ms-radial-gradient($value);
        background-image: -o-radial-gradient($value);
        background-image: radial-gradient($value);
    }
}

// Animate 
@mixin animation($property) {
    -webkit-animation: $property;
    -moz-animation: $property;
    -ms-animation: $property;
    -o-animation: $property;
    animation: $property;
}

// Filter 
@mixin filter($value) {
    -webkit-filter: $value;
    filter: $value;
}

// Appearance for select
@mixin appearance($value) {
    -webkit-appearance: $value;
    -moz-appearance: $value;
    -ms-appearance: $value;
    -o-appearance: $value;
    appearance: $value;
}

// keyframes 
@mixin keyframes($name) {
    @-webkit-keyframes #{$name} {
        @content;
    }

    @-moz-keyframes #{$name} {
        @content;
    }

    @-ms-keyframes #{$name} {
        @content;
    }

    @keyframes #{$name} {
        @content;
    }
}

//background 
@mixin background {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

// Transition specific
@mixin td-transition($property: all, $time: .3s, $func : ease-out, $delay : 0s) {
    -webkit-transition: $property $time $delay $func;
    -moz-transition: $property $time $delay $func;
    -ms-transition: $property $time $delay $func;
    -o-transition: $property $time $delay $func;
    transition: $property $time $delay $func;
}

// Transition multiple
@mixin td-transition-mul($property) {
    -webkit-transition: $property;
    -moz-transition: $property;
    -ms-transition: $property;
    -o-transition: $property;
    transition: $property ;
}

// Transform
@mixin transform($transforms) {
    -webkit-transform: $transforms;
    -moz-transform: $transforms;
    -ms-transform: $transforms;
    -o-transform: $transforms;
    transform: $transforms;
}

@mixin animation-name($name: fadeInUp, $fill : both) {
    animation-name: $name;
    animation-fill-mode: $fill;
}

@mixin animation-control($time: .3s, $duration : 1s) {
    animation-delay: $time;
    animation-duration: $duration;
}

// Sentence case
@mixin sentence-case() {
    text-transform: lowercase;

    &:first-letter {
        text-transform: uppercase;
    }
}

// Flexbox display
@mixin flexbox() {
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

@mixin inline-flex() {
    display: -webkit-inline-box;
    display: -moz-inline-box;
    display: -ms-inline-flexbox;
    display: -webkit-inline-flex;
    display: inline-flexbox;
    display: inline-flex;
}

// Grid display
@mixin gridbox() {
    display: -ms-grid;
    display: -moz-grid;
    display: grid;
}

@mixin gap($value) {
    gap: $value;
    grid-gap: $value;
}

//Border
@mixin border-radius($man) {
    -webkit-border-radius: $man;
    -moz-border-radius: $man;
    -o-border-radius: $man;
    -ms-border-radius: $man;
    border-radius: $man;
}

// Box shadows
@mixin box-shadow($shadow) {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    -ms-box-shadow: $shadow;
    -o-box-shadow: $shadow;
    box-shadow: $shadow;
}

// backdrop-filter
@mixin backdrop-filter($shadow) {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    -ms-box-shadow: $shadow;
    -o-box-shadow: $shadow;
    box-shadow: $shadow;
}

// backdrop-filter blur
@mixin backdrop-filter($blur) {
    backdrop-filter: blur($blur);
    -webkit-backdrop-filter: blur($blur);
}