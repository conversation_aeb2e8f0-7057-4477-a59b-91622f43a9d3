@use '../../utils' as *;

/*----------------------------------------*/
/* Trading View styles
/*----------------------------------------*/
.trading-system-status-section {
    position: relative;

    &::before {
        position: absolute;
        content: "";
        height: 100%;
        width: 248px;
        top: 0;
        left: 0;
        background: linear-gradient(270deg, #010C1A 29.4%, rgba(1, 12, 26, 0) 94.11%);
        transform: matrix(-1, 0, 0, 1, 0, 0);
        z-index: 9;

        @media #{$xs,$sm,$md} {
            width: 190px;
        }
    }

    &::after {
        position: absolute;
        content: "";
        height: 100%;
        width: 248px;
        top: 0;
        right: 0;
        background: linear-gradient(270deg, #010C1A 29.4%, rgba(1, 12, 26, 0) 94.11%);
        z-index: 9;

        @media #{$xs,$sm,$md} {
            width: 190px;
        }
    }
}

.trading-status-sliders {
    .trading-status-card {
        display: inline-flex;
        align-items: self-start;
        gap: 12px;
        background: #0A1729;
        border: 1px solid rgba(255, 255, 255, 0.16);
        box-shadow: 0px 4px 11px rgba(166, 239, 103, 0.1);
        border-radius: 16px;
        padding: 12px 16px;

        .icon {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            flex: 0 0 auto;
            display: inline-flex;
            align-items: center;
        }

        .contents {
            .title {
                font-size: 16px;
            }

            .description {
                font-size: 14px;
                display: block;
                margin-top: 2px;

                .down {
                    color: #EB4E5C;
                }
            }
        }
    }

    .swiper-wrapper {
        transition-timing-function: linear;
    }

    .swiper-slide {
        width: max-content !important;
        /* Important for 'slidesPerView: 'auto'' */
    }

    &.style-two {
        .trading-status-card {
            background: #0F0F0F;
            border: 1px solid rgba(255, 255, 255, 0.16);
            border-radius: 12px;
            gap: 6px;
            align-items: center;

            .contents {
                display: flex;
                gap: 6px;

                .description {
                    margin-top: 0;
                }
            }
        }
    }
}