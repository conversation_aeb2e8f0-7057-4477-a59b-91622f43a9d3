<!doctype html>
<html class="no-js" lang="zxx">

<head>
   <meta charset="utf-8">
   <meta http-equiv="x-ua-compatible" content="ie=edge">
   <title>P2P Order User || PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange</title>
   <meta name="description" content="">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <!-- Place favicon.ico in the root directory -->
   <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
   <!-- CSS here -->
   <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
   <link rel="stylesheet" href="../assets/css/flag-icon.css">
   <link rel="stylesheet" href="../assets/css/select2.css">
   <link rel="stylesheet" href="../assets/css/daterangepicker.css">
   <link rel="stylesheet" href="../assets/css/simplebar.css">
   <link rel="stylesheet" href="../assets/css/styles.css">
   <link rel="stylesheet" href="../assets/css/satoshi.css">
</head>

<body class="dark-theme">

   <!--[if lte IE 9]>
   <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
   <![endif]-->

   <!-- Pre loader start -->
   <!-- Pre loader start -->
   <div id="td-loadingDiv">
      <div class="td-loading-wrapper">
         <svg width="184" height="30" viewBox="0 0 184 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
               d="M0 8.73976C0 8.21184 0.263844 7.71884 0.703105 7.426L9.38732 1.63653C10.4366 0.936994 11.8421 1.68919 11.8421 2.95029V21.2602C11.8421 21.7882 11.5783 22.2812 11.139 22.574L2.45479 28.3635C1.40549 29.063 0 28.3108 0 27.0497V8.73976Z"
               fill="#3B57E7" />
            <path
               d="M8.68408 8.73976C8.68408 8.21184 8.94793 7.71884 9.38719 7.426L18.0714 1.63653C19.1207 0.936994 20.5262 1.68919 20.5262 2.95029V21.2602C20.5262 21.7882 20.2623 22.2812 19.8231 22.574L11.1389 28.3635C10.0896 29.063 8.68408 28.3108 8.68408 27.0497V8.73976Z"
               fill="#CCFF70" />
            <path
               d="M176.954 22.3422C172.858 22.3422 169.989 19.3611 169.989 15.0984C169.989 10.7799 172.803 7.79883 176.843 7.79883C180.966 7.79883 183.585 10.5571 183.585 14.8476V15.8785L173.221 15.9064C173.471 18.3303 174.753 19.5561 177.01 19.5561C178.876 19.5561 180.102 18.8318 180.492 17.5223H183.641C183.056 20.5313 180.548 22.3422 176.954 22.3422ZM176.87 10.5849C174.864 10.5849 173.639 11.6715 173.304 13.7332H180.214C180.214 11.8387 178.904 10.5849 176.87 10.5849Z"
               fill="#A6EF67" />
            <path
               d="M152.903 14.7915C152.903 10.7795 155.522 7.77051 159.45 7.77051C161.512 7.77051 163.128 8.6342 163.936 10.1108L164.131 8.21628H167.279V21.2831C167.279 25.8801 164.521 28.7498 160.063 28.7498C156.107 28.7498 153.404 26.4931 152.986 22.8154H156.385C156.608 24.5985 157.974 25.6572 160.063 25.6572C162.403 25.6572 163.908 24.1806 163.908 21.896V19.6114C163.044 20.893 161.345 21.701 159.367 21.701C155.466 21.701 152.903 18.7756 152.903 14.7915ZM156.33 14.7079C156.33 17.0203 157.806 18.7477 160.035 18.7477C162.376 18.7477 163.824 17.1039 163.824 14.7079C163.824 12.3676 162.403 10.7516 160.035 10.7516C157.778 10.7516 156.33 12.4511 156.33 14.7079Z"
               fill="#A6EF67" />
            <path
               d="M142.515 22.3422C139.589 22.3422 137.806 20.6427 137.806 18.0517C137.806 15.5163 139.645 13.9282 142.905 13.6775L147.028 13.371V13.0645C147.028 11.1979 145.914 10.4456 144.186 10.4456C142.18 10.4456 141.066 11.2814 141.066 12.7302H138.168C138.168 9.74909 140.62 7.79883 144.353 7.79883C148.059 7.79883 150.344 9.80481 150.344 13.6218V21.98H147.362L147.112 19.9462C146.527 21.3671 144.66 22.3422 142.515 22.3422ZM143.629 19.779C145.719 19.779 147.056 18.5253 147.056 16.4079V15.6835L144.186 15.9064C142.069 16.1014 141.261 16.7979 141.261 17.9123C141.261 19.1661 142.097 19.779 143.629 19.779Z"
               fill="#A6EF67" />
            <path
               d="M119.934 21.9801L115.477 8.2168H119.015L120.826 14.1233C121.132 15.2099 121.411 16.4079 121.662 17.6895C121.912 16.3522 122.163 15.4885 122.609 14.1233L124.504 8.2168H127.958L129.797 14.1233C129.964 14.6805 130.521 16.7422 130.689 17.6617C130.912 16.6308 131.357 14.987 131.608 14.1233L133.447 8.2168H137.041L132.277 21.9801H129.101L127.206 16.0179C126.649 14.2069 126.342 12.8974 126.231 12.2009C126.092 12.8417 125.841 13.8168 125.144 16.0736L123.25 21.9801H119.934Z"
               fill="#A6EF67" />
            <path
               d="M112.11 21.9797H108.711V11.0582H106.064V8.21636H108.711V3.92578H112.11V8.21636H114.785V11.0582H112.11V21.9797Z"
               fill="white" />
            <path
               d="M98.2143 22.3422C94.1187 22.3422 91.249 19.3611 91.249 15.0984C91.249 10.7799 94.063 7.79883 98.1028 7.79883C102.226 7.79883 104.845 10.5571 104.845 14.8476V15.8785L94.4809 15.9064C94.7316 18.3303 96.0132 19.5561 98.27 19.5561C100.137 19.5561 101.363 18.8318 101.753 17.5223H104.901C104.316 20.5313 101.808 22.3422 98.2143 22.3422ZM98.1307 10.5849C96.1247 10.5849 94.8988 11.6715 94.5645 13.7332H101.474C101.474 11.8387 100.165 10.5849 98.1307 10.5849Z"
               fill="white" />
            <path
               d="M81.1182 21.9795H77.7471V1.25098H81.1182V13.9834L86.4675 8.21621H90.7303L85.4924 13.677L90.6188 21.9795H86.7183L83.1242 16.1844L81.1182 18.274V21.9795Z"
               fill="white" />
            <path
               d="M61.5649 15.0705C61.5649 10.8078 64.3789 7.79883 68.4466 7.79883C72.2078 7.79883 74.771 9.8884 75.1332 13.2038H71.7342C71.3441 11.6436 70.2018 10.8357 68.5859 10.8357C66.4127 10.8357 64.964 12.4795 64.964 15.0705C64.964 17.6616 66.3013 19.2775 68.4744 19.2775C70.174 19.2775 71.372 18.4417 71.7342 16.9372H75.1611C74.7432 20.1412 72.0685 22.3422 68.4744 22.3422C64.2953 22.3422 61.5649 19.4447 61.5649 15.0705Z"
               fill="white" />
            <path
               d="M44.9683 15.07C44.9683 10.7794 48.0608 7.82617 52.3235 7.82617C56.5863 7.82617 59.6788 10.7794 59.6788 15.07C59.6788 19.3606 56.5863 22.3139 52.3235 22.3139C48.0608 22.3139 44.9683 19.3606 44.9683 15.07ZM48.3673 15.07C48.3673 17.5775 49.9832 19.277 52.3235 19.277C54.6639 19.277 56.2798 17.5775 56.2798 15.07C56.2798 12.5625 54.6639 10.863 52.3235 10.863C49.9832 10.863 48.3673 12.5625 48.3673 15.07Z"
               fill="white" />
            <path
               d="M28.5264 28.3602V8.21674H31.6747L31.8975 10.3063C32.7334 8.71824 34.5443 7.79883 36.6339 7.79883C40.5066 7.79883 43.0698 10.6128 43.0698 14.9312C43.0698 19.2218 40.7295 22.3422 36.6339 22.3422C34.5722 22.3422 32.7891 21.5343 31.9254 20.1412V28.3602H28.5264ZM31.9533 15.0984C31.9533 17.578 33.4856 19.2775 35.8259 19.2775C38.222 19.2775 39.6429 17.5502 39.6429 15.0984C39.6429 12.6466 38.222 10.8914 35.8259 10.8914C33.4856 10.8914 31.9533 12.6188 31.9533 15.0984Z"
               fill="white" />
         </svg>

         <div class="td-loading">
            <div class="td-loading-overlay"></div>
         </div>
      </div>
   </div>
   <!-- Pre loader start -->

   <!-- Cryptocurrency buy Modal -->
   <div class="modal cryptocurrency-modal fade" id="cryptocurrencyBuyModal" tabindex="-1" aria-labelledby="cryptocurrencyBuyModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
         <!-- modal close btn -->
         <button type="button" class="modal-btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="solar--close-circle-linear"></i></button>
         <div class="cryptocurrency-purchase-modal">
            <div class="currency-contents">
               <div class="table-advertiser is-online">
                  <span class="avatar">M</span>
                  <div>
                     <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"></path>
                        <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"></path>
                        <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"></path>
                        </svg>
                        </span></p>
                     <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                     <p class="status">Online <span class="devider">|</span> 15min</p>
                  </div>
               </div>
               <div class="seller-info-wrapper">
                  <div class="seller-info">
                     <span class="status">Available</span>
                     <h4 class="status-title">44.22 USDT</h4>
                  </div>
                  <div class="seller-info">
                     <span class="status">Payment Time Limite</span>
                     <h4 class="status-title">20 min</h4>
                  </div>
                  <div class="seller-info">
                     <span class="status">Avg. Release Time</span>
                     <h4 class="status-title">2h.24 minute</h4>
                  </div>
               </div>
               <div class="dvertisers-terms-contents">
                  <div class="heading">
                     <h5>Advertisers’ Terms ( Please read carefully)</h5>
                  </div>
                  <div class="info">
                     <ul>
                        <li>What are the terms used in banking?</li>
                        <li>What are the 8 words related to bank?</li>
                        <li>The elements of the marketing mix in services are 7, namely: product, price, place, people, promotion, physical evidence and process. Banks are service institutions.</li>
                        <li>Advertisers’ Terms ( Please read carefully)</li>
                     </ul>
                  </div>
               </div>
            </div>
            <div class="modal-right-contents">
               <div class="currency-forms">
                  <div class="forms-grid">
                     <div class="price"><span>Price</span> 125.4 USDT</div>
                     <div class="input-box">
                        <div class="contents">
                           <label>You Pay</label>
                           <span>$100</span>
                        </div>
                        <div class="icon">
                           <img src="../assets/images/currency-icon/btc.png" alt="Currency Icon">
                           <span>USDT</span>
                        </div>
                     </div>
                     <div class="input-box">
                        <div class="contents">
                           <label>You Receive</label>
                           <span>100</span>
                        </div>
                        <div class="icon">
                           <img src="../assets/images/currency-icon//sol.png" alt="Currency Icon">
                           <span>USDT</span>
                        </div>
                     </div>
                     <div class="payment-method">
                        <div class="td-form-group">
                           <label class="input-label">Payment Method</label>
                           <div class="input-field">
                              <select  class="defaultselect2">
                                 <option value="paypal">PayPal</option>  
                                 <option value="stripe">Stripe</option>  
                                 <option value="braintree">Braintree</option>  
                                 <option value="square">Square</option>  
                                 <option value="payoneer">Payoneer</option>  
                                 <option value="worldpay">WorldPay</option>  
                                 <option value="adyen">Adyen</option>  
                              </select> 
                           </div>
                           <p class="feedback-invalid">This field is required</p>
                        </div>
                     </div>
                     <div class="processing-fee">Processing Fee: 1.35 USDT</div>
                  </div>
                  <div class="buttons">
                     <button type="button" data-bs-dismiss="modal" aria-label="Close" class="payment-btn cancel w-100">Cancel</button>
                     <button type="button" class="payment-btn buy w-100">Buy USDT</button>
                  </div>
               </div>
            </div>
         </div>
      </div>
      </div>
   </div>

   <!-- Cryptocurrency sell Modal -->
   <div class="modal cryptocurrency-modal is-sell fade" id="cryptocurrencySellModal" tabindex="-1" aria-labelledby="cryptocurrencySellModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
         <!-- modal close btn -->
         <button type="button" class="modal-btn-close" data-bs-dismiss="modal" aria-label="Close"><i class="solar--close-circle-linear"></i></button>
         <div class="cryptocurrency-purchase-modal">
            <div class="currency-contents">
               <div class="table-advertiser is-online">
                  <span class="avatar">M</span>
                  <div>
                     <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"></path>
                        <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"></path>
                        <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"></path>
                        </svg>
                        </span></p>
                     <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                     <p class="status">Online <span class="devider">|</span> 15min</p>
                  </div>
               </div>
               <div class="seller-info-wrapper">
                  <div class="seller-info">
                     <span class="status">Available</span>
                     <h4 class="status-title">44.22 USDT</h4>
                  </div>
                  <div class="seller-info">
                     <span class="status">Payment Time Limite</span>
                     <h4 class="status-title">20 min</h4>
                  </div>
                  <div class="seller-info">
                     <span class="status">Avg. Release Time</span>
                     <h4 class="status-title">2h.24 minute</h4>
                  </div>
               </div>
               <div class="dvertisers-terms-contents">
                  <div class="heading">
                     <h5>Advertisers’ Terms ( Please read carefully)</h5>
                  </div>
                  <div class="info">
                     <ul>
                        <li>What are the terms used in banking?</li>
                        <li>What are the 8 words related to bank?</li>
                        <li>The elements of the marketing mix in services are 7, namely: product, price, place, people, promotion, physical evidence and process. Banks are service institutions.</li>
                        <li>Advertisers’ Terms ( Please read carefully)</li>
                     </ul>
                  </div>
               </div>
            </div>
            <div class="modal-right-contents">
               <div class="currency-forms">
                  <div class="forms-grid">
                     <div class="price"><span>Price</span> 125.4 USDT</div>
                     <div class="input-box">
                        <div class="contents">
                           <label>You Pay</label>
                           <span>$100</span>
                           <div class="balance">
                              Balance:44.22 USDT
                           </div>
                        </div>
                        <div class="icon">
                           <img src="../assets/images/currency-icon/btc.png" alt="Currency Icon">
                           <span>USDT</span>
                        </div>
                     </div>
                     <div class="input-box">
                        <div class="contents">
                           <label>You Receive</label>
                           <span>100</span>
                        </div>
                        <div class="icon">
                           <img src="../assets/images/currency-icon//sol.png" alt="Currency Icon">
                           <span>USDT</span>
                        </div>
                     </div>
                     <div class="payment-method">
                        <div class="td-form-group">
                           <label class="input-label">Payment Method</label>
                           <div class="input-field">
                              <select  class="defaultselect2">
                                 <option value="Bikash">Bikash</option>  
                                 <option value="Bank Transfer">Bank Transfer</option>  
                                 <option value="Meghna Bank LTD">Meghna Bank LTD</option>  
                                 <option value="Asia Bank">Asia Bank</option>  
                                 <option value="Dutch-Bangla Bank">Dutch-Bangla Bank</option>  
                                 <option value="worldpay">WorldPay</option>  
                                 <option value="adyen">Adyen</option>  
                              </select> 
                           </div>
                           <p class="feedback-invalid">This field is required</p>
                        </div>
                     </div>
                     <div class="processing-fee">Processing Fee: 1.35 USDT</div>
                  </div>
                  <div class="buttons">
                     <button type="button" data-bs-dismiss="modal" aria-label="Close" class="payment-btn cancel w-100">Cancel</button>
                     <button type="button" class="payment-btn sell w-100">Buy USDT</button>
                  </div>
               </div>
            </div>
         </div>
      </div>
      </div>
   </div>

   <!-- Page wrapper start-->
   <div class="page-wrapper null compact-wrapper">

      <!-- Page header start-->
      <div class="app-page-header">
         <div class="app-dashboard-header">
            <div class="left-content">
               <div class="content-inner">
                  <button class="toggle-sidebar d-xl-none">
                     <span class="bar-icon">
                        <svg width="7" height="10" viewBox="0 0 7 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M5.25977 8.53022L1.73977 5.00022L5.25977 1.47021" fill="white"/>
                           <path d="M5.25977 8.53022L1.73977 5.00021L5.25977 1.47021" stroke="#171717" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>                                                                         
                     </span>
                  </button>
                  <div class="header-menu d-none d-xl-inline-flex">
                     <div class="td-main-menu">
                        <nav>
                           <ul>
                              <li>
                                 <a href="#">Buy Crypto</a>
                                 <ul class="dp-menu">
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-01.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Spot Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-02.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Margin Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-03.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Trading Bot</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-04.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Convert</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-05.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>API Services</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-06.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Pre-Market trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-07.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>GemSPACE</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                 </ul>
                              </li>
                              <li class="menu-has-children">
                                 <a href="#">Markets</a>
                                 <ul class="dp-menu">
                                    <!-- menu item box list -->
                                    <li class="menu-has-children">
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-01.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Spot Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                       <ul class="menu-sidebar">
                                          <span class="menu-tabs-wrapper">
                                             <div class="menu-tabs td-tab">
                                                <button class="scroll-left d-none">&#10094;</button>
                                                <ul class="nav nav-tabs" id="myTab" role="tablist">
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link active" id="header-menu-usdt-tab" data-bs-toggle="tab" data-bs-target="#header-menu-usdt-tab-pane" type="button" role="tab" aria-controls="header-menu-usdt-tab-pane" aria-selected="true">USDT</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-usdc-tab" data-bs-toggle="tab" data-bs-target="#header-menu-usdc-tab-pane" type="button" role="tab" aria-controls="header-menu-usdc-tab-pane" aria-selected="false">USDC</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-btc-tab" data-bs-toggle="tab" data-bs-target="#header-menu-btc-tab-pane" type="button" role="tab" aria-controls="header-menu-btc-tab-pane" aria-selected="false">BTC</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-fiat-tab" data-bs-toggle="tab" data-bs-target="#header-menu-fiat-tab-pane" type="button" role="tab" aria-controls="header-menu-fiat-tab-pane" aria-selected="false">FIAT</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-xrp-tab" data-bs-toggle="tab" data-bs-target="#header-menu-xrp-tab-pane" type="button" role="tab" aria-controls="header-menu-xrp-tab-pane" aria-selected="false">XRP</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-doge-tab" data-bs-toggle="tab" data-bs-target="#header-menu-doge-tab-pane" type="button" role="tab" aria-controls="header-menu-doge-tab-pane" aria-selected="false">DOGE</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-shiba-tab" data-bs-toggle="tab" data-bs-target="#header-menu-shiba-tab-pane" type="button" role="tab" aria-controls="header-menu-shiba-tab-pane" aria-selected="false">SHIBA</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-ada-tab" data-bs-toggle="tab" data-bs-target="#header-menu-ada-tab-pane" type="button" role="tab" aria-controls="header-menu-ada-tab-pane" aria-selected="false">ADA</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-sol-tab" data-bs-toggle="tab" data-bs-target="#header-menu-sol-tab-pane" type="button" role="tab" aria-controls="header-menu-sol-tab-pane" aria-selected="false">SOL</button>
                                                   </li>
                                                </ul>
                                                <button class="scroll-right">&#10095;</button>
                                             </div>
                                          </span>
                                          <span class="tab-content" id="myTabContent">
                                             <div class="tab-pane fade show active" id="header-menu-usdt-tab-pane" role="tabpanel" aria-labelledby="header-menu-usdt-tab" tabindex="0">
                                                <div class="box">
                                                   <div class="search-box">
                                                      <div class="search-icon">
                                                         <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M12.25 12.2501L9.71658 9.7167M9.71658 9.7167C10.1499 9.28335 10.4937 8.76889 10.7282 8.20269C10.9627 7.63649 11.0835 7.02964 11.0835 6.41679C11.0835 5.80394 10.9627 5.19709 10.7282 4.63088C10.4937 4.06468 10.1499 3.55022 9.71658 3.11687C9.28323 2.68352 8.76877 2.33977 8.20257 2.10524C7.63637 1.87071 7.02952 1.75 6.41666 1.75C5.80381 1.75 5.19696 1.87071 4.63076 2.10524C4.06456 2.33977 3.5501 2.68352 3.11675 3.11687C2.24156 3.99206 1.74988 5.17908 1.74988 6.41679C1.74988 7.6545 2.24156 8.84151 3.11675 9.7167C3.99194 10.5919 5.17896 11.0836 6.41666 11.0836C7.65437 11.0836 8.84139 10.5919 9.71658 9.7167Z" stroke="#CCCCCC" stroke-linecap="round" stroke-linejoin="round"></path>
                                                         </svg>
                                                      </div>
                                                      <input type="text" placeholder="Search">
                                                   </div>
                                                   <ul class="pair-list">
                                                      <li class="active">
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/btc.png" alt="BTC">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">BTC/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/eth.png" alt="ETH">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">ETH/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/sol.png" alt="ETH">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">SOL/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/xmr.png" alt="XMR">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">XMR/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/erp.png" alt="ERP">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">ERP/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/met.png" alt="MET">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">MET/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/gsc.png" alt="GSC">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">GSC/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/bnb.png" alt="BNB">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">BNB/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li> 
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/pot.png" alt="POT">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">POT/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li> 
                                                   </ul>
                                                </div>
                                             </div>
                                             <div class="tab-pane fade" id="header-menu-usdc-tab-pane" role="tabpanel" aria-labelledby="header-menu-usdc-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-btc-tab-pane" role="tabpanel" aria-labelledby="header-menu-btc-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-fiat-tab-pane" role="tabpanel" aria-labelledby="header-menu-fiat-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-xrp-tab-pane" role="tabpanel" aria-labelledby="header-menu-xrp-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-doge-tab-pane" role="tabpanel" aria-labelledby="header-menu-doge-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-shiba-tab-pane" role="tabpanel" aria-labelledby="header-menu-shiba-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-ada-tab-pane" role="tabpanel" aria-labelledby="header-menu-ada-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-sol-tab-pane" role="tabpanel" aria-labelledby="header-menu-sol-tab" tabindex="0">...</div>
                                          </span>
                                      </ul>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-02.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Margin Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-03.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Trading Bot</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-04.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Convert</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-05.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>API Services</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-06.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Pre-Market trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-07.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>GemSPACE</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                 </ul>
                              </li>
                              <li>
                                 <a href="#">Trade</a>
                              </li>
                              <li>
                                 <a href="#">Derivatives</a>
                              </li>
                              <li>
                                 <a href="#">Earn</a>
                              </li>
                              <li>
                                 <a href="#">Institutional</a>
                              </li>
                           </ul>
                        </nav>
                     </div>
                  </div>
               </div> 
            </div>
            <div class="right-content">
               <div class="header-right">
                  <div class="header-quick-actions d-flex align-items-center">
                     <div class="header-btns-wrap d-none d-sm-inline-flex">
                        <a class="td-btn btn-sm" href="../auth/sign-up.html">
                           <span class="btn-icon"><i class="solar--download-minimalistic-linear"></i></span>
                           <span class="btn-text">Deposit</span>
                        </a>
                     </div>
                     <div class="currency-switcher d-none d-sm-block">
                        <div class="defaults-select">
                           <select class="defaultselect2">
                              <option value="USD" data-icon="flag-icon flag-icon-us" selected>Dollar (USD)</option>  
                              <option value="EUR" data-icon="flag-icon flag-icon-de">Euro (EUR)</option>  
                              <option value="CNY" data-icon="flag-icon flag-icon-cn">Chinese (CNY)</option>  
                              <option value="JPY" data-icon="flag-icon flag-icon-jp">Japanese (JPY)</option>  
                              <option value="GBP" data-icon="flag-icon flag-icon-gb">British (GBP)</option>  
                              <option value="INR" data-icon="flag-icon flag-icon-in">Indian (INR)</option>  
                              <option value="AUD" data-icon="flag-icon flag-icon-au">Australian (AUD)</option>  
                              <option value="CAD" data-icon="flag-icon flag-icon-ca">Canadian (CAD)</option>  
                           </select>
                        </div>
                     </div>
                     <div class="others-actions">
                        <div class="theme-switcher">
                           <!-- Theme toggle button for switching between light and dark mode -->
                           <button id="theme-toggle" class="action-icon theme-switcher" aria-label="Toggle Theme">
                              <!-- Light mode icon (visible in dark mode) -->
                              <span class="light-mode" aria-hidden="true">
                                 <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M16.5 11.8831C15.5149 12.3286 14.4214 12.5766 13.2701 12.5766C8.93647 12.5766 5.42337 9.06352 5.42337 4.7299C5.42337 3.57851 5.67135 2.48505 6.11683 1.5C3.39432 2.73122 1.5 5.47102 1.5 8.65327C1.5 12.9869 5.0131 16.5 9.34672 16.5C12.529 16.5 15.2688 14.6056 16.5 11.8831Z" stroke="#080808" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                                 </svg>
                              </span>
                              <!-- Dark mode icon (visible in light mode) -->
                              <span class="dark-mode" aria-hidden="true">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M12 3V4.8M12 19.2V21M4.8 12H3M6.88271 6.88271L5.60991 5.60991M17.1173 6.88271L18.3901 5.60991M6.88271 17.121L5.60991 18.3938M17.1173 17.121L18.3901 18.3938M21 12H19.2M16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12Z" stroke="white" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                                 </svg>
                              </span>
                          </button>
                        </div>
                        <div class="language-dropdown">
                           <div class="language-box language-nav">
                              <div class="translate_wrapper">
                                 <div class="current_lang">
                                    <div class="quick-action-item">
                                       <button type="button" class="action-icon">
                                          <i class="solar--global-line-duotone"></i>    
                                       </button>
                                    </div>
                                 </div>
                                 <div class="more_lang">
                                    <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English</span></div>
                                    <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                                    <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                                    <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- Notification dropdown -->
                        <div class="notification-panel-box">
                           <div class="quick-action-item">
                              <button type="button" class="action-icon notification-btn">
                                 <i class="solar--bell-line-duotone"></i>      
                              </button>
                           </div>
                           <!-- Notification content here -->
                           <div class="notification-panel">
                              <div class="notification-header">
                                 <h3>Notifications</h3>
                                 <button type="button" class="notification-btn-close notifications-dropdown-close">
                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path fill-rule="evenodd" clip-rule="evenodd" d="M4.1674 15.1556C3.84198 15.4811 3.84203 16.0086 4.1675 16.3341C4.49297 16.6595 5.0206 16.6595 5.34601 16.334L10.0004 11.6788L14.6551 16.3336C14.9805 16.659 15.5082 16.659 15.8336 16.3336C16.159 16.0081 16.159 15.4805 15.8336 15.1551L11.1788 10.5002L15.8333 5.8449C16.1586 5.51944 16.1586 4.9918 15.8331 4.66639C15.5077 4.34098 14.98 4.34103 14.6546 4.66649L10.0002 9.32172L5.34544 4.66689C5.02001 4.34146 4.49237 4.34146 4.16693 4.66689C3.84149 4.99234 3.84149 5.51997 4.16693 5.84541L8.82187 10.5003L4.1674 15.1556Z" fill="white"/>
                                    </svg>                                    
                                 </button>
                              </div>
                              <div class="notifications-inner">
                                 <div class="notifications-top">
                                    <button type="button">View All</button>
                                    <button type="button">Clear All</button>
                                 </div>
                                 <div class="notifications-lists">
                                    <a href="profile-notification.html">
                                       <div class="notification-list">
                                          <div class="inner-item">
                                             <div class="icon">
                                                <img src="../assets/images/notifications/01.png" alt="Notifications Icon">
                                             </div>
                                             <div class="contents">
                                                <h5 class="title">Account password change</h5>
                                                <p class="message">Today's the day. Your culinary adventure is almost there.</p>
                                             </div>
                                          </div>
                                          <p class="time">Today</p>
                                       </div>
                                    </a>
                                    <a href="profile-notification.html">
                                       <div class="notification-list">
                                          <div class="inner-item">
                                             <div class="icon">
                                                <img src="../assets/images/notifications/02.png" alt="Notifications Icon">
                                             </div>
                                             <div class="contents">
                                                <h5 class="title">Solve the security issue</h5>
                                                <p class="message">Last chance to add a little extra to your Tuesday delivery.</p>
                                             </div>
                                          </div>
                                          <p class="time">Today</p>
                                       </div>
                                    </a>
                                    <a href="profile-notification.html">
                                       <div class="notification-list">
                                          <div class="inner-item">
                                             <div class="icon">
                                                <img src="../assets/images/notifications/03.png" alt="Notifications Icon">
                                             </div>
                                             <div class="contents">
                                                <h5 class="title">Download android app</h5>
                                                <p class="message">Added Gourmet Cheese to your kit. Next week just got tastier!</p>
                                             </div>
                                          </div>
                                          <p class="time">Today</p>
                                       </div>
                                    </a>
                                    <a href="profile-notification.html">
                                       <div class="notification-list">
                                          <div class="inner-item">
                                             <div class="icon">
                                                <img src="../assets/images/notifications/04.png" alt="Notifications Icon">
                                             </div>
                                             <div class="contents">
                                                <h5 class="title">Bitcoin price is high now</h5>
                                                <p class="message">New menu items are in! What will you try next?</p>
                                             </div>
                                          </div>
                                          <p class="time">4 days ago</p>
                                       </div>
                                    </a>
                                    <a href="profile-notification.html">
                                       <div class="notification-list">
                                          <div class="inner-item">
                                             <div class="icon">
                                                <img src="../assets/images/notifications/05.png" alt="Notifications Icon">
                                             </div>
                                             <div class="contents">
                                                <h5 class="title">Poment completed</h5>
                                                <p class="message">Tell us how the Veggie Platter did on the flavor front!</p>
                                             </div>
                                          </div>
                                          <p class="time">1 week ago</p>
                                       </div>
                                    </a>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <!-- User Profile -->
                        <div class="user-profile-drop">
                           <div class="quick-action-item">
                              <button type="button" class="action-icon">
                                 <i class="solar--user-bold-duotone"></i>    
                              </button>
                           </div>
                           <div class="dropdown-menu">
                              <div class="profile-header">
                                 <div class="avatar">SH</div>
                                 <div class="user-info">
                                    <h5 class="title">sh******@gmail.com</h5>
                                    <div class="uid"><span class="uid-text">UID: <span id="uid-text">236145905</span> </span>
                                       <i class="solar--copy-outline copy-btn" onclick="copyUID(this)"></i>
                                       <span class="tooltip">Copied!</span>
                                    </div>
                                    <span class="td-badge unverified"><i class="solar--danger-triangle-outline"></i> Unverified</span>
                                 </div>
                             </div>
                              <div class="profie-info-list">
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--widget-outline"></i></span>
                                    <span class="text">Dashboard</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--sale-bold-duotone"></i></span>
                                    <span class="text">Dashboard</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--cart-4-bold-duotone"></i></span>
                                    <span class="text">Orders</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--user-bold-duotone"></i></span>
                                    <span class="text">Account</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--users-group-two-rounded-bold-duotone"></i></span> 
                                    <span class="text">Referral</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--cup-bold-duotone"></i></span>
                                    <span class="text">Rewards Hub</span>
                                 </a>
                                 <a class="profie-info-item" href="profile-notification.html">
                                    <span class="icon"><i class="solar--settings-bold-duotone"></i></span>
                                    <span class="text">Settings</span>
                                 </a>
                                 <a class="profie-info-item profile-log-out" href="profile-notification.html">
                                    <span class="icon"><i class="solar--recive-square-bold-duotone"></i></span>
                                    <span class="text">Log Out</span>
                                 </a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <!-- Page header end -->

      <!-- Page body start-->
      <div class="app-page-body-wrapper">

         <!-- Page sidebar start-->
         <div class="app-sidebar-wrapper">
            <!-- app sidebar -->
            <div class="app-sidebar" id="sidebar">
               <div class="main-sidebar-header">
                  <a href="user-dashborad.html" class="sidebar-logo">
                     <img class="main-logo" src="../assets/images/logo/logo.svg" alt="logo">
                     <img class="small-logo" src="../assets/images/logo/logo-small.svg" alt="logo">
                  </a>
                  <button class="toggle-sidebar">
                     <span class="bar-icon">
                        <svg width="7" height="10" viewBox="0 0 7 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M5.25977 8.53022L1.73977 5.00022L5.25977 1.47021" fill="white"/>
                           <path d="M5.25977 8.53022L1.73977 5.00021L5.25977 1.47021" stroke="#171717" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                     </span>
                  </button>
               </div>
               <div class="main-sidebar" id="simple-bar">
                  <nav class="main-menu-container nav nav-pills flex-column sub-open">
                     <ul class="main-menu">
                        <!-- slide -->
                        <li class="slide">
                           <a href="user-dashborad.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--widget-bold-duotone"></i>                             
                              </div>
                              <span class="sidebar-menu-label">Dashboard</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="my-order.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--cart-4-bold-duotone"></i>                            
                              </div>
                              <span class="sidebar-menu-label">Orders</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="deposit.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--card-recive-bold-duotone"></i>                          
                              </div>
                              <span class="sidebar-menu-label">Deposit</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="transfer.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--card-send-bold-duotone"></i>                                                          
                              </div>
                              <span class="sidebar-menu-label">Transfer</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="withdraw.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--cash-out-bold-duotone"></i>                                                      
                              </div>
                              <span class="sidebar-menu-label">Withdraw</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="referral-program.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--users-group-two-rounded-bold-duotone"></i>                      
                              </div>
                              <span class="sidebar-menu-label">Referral Program</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="history.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--clock-circle-bold-duotone"></i>                
                              </div>
                              <span class="sidebar-menu-label">History</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="wallets.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--wallet-money-bold-duotone"></i>                                           
                              </div>
                              <span class="sidebar-menu-label">Wallets</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide active">
                           <a href="P2P-order-user.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--rocket-2-bold-duotone"></i>          
                              </div>
                              <span class="sidebar-menu-label">P2P Orders</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="staking.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--structure-bold-duotone"></i>                      
                              </div>
                              <span class="sidebar-menu-label">Staking</span>
                           </a>
                        </li>
                        <!-- slide -->
                        <li class="slide">
                           <a href="my-rewards.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--cup-bold-duotone"></i>    
                              </div>
                              <span class="sidebar-menu-label">My Rewards</span>
                           </a>
                        </li>
                        <li class="slide">
                           <a href="profile-settings.html" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <i class="solar--settings-bold-duotone"></i>                       
                              </div>
                              <span class="sidebar-menu-label">Settings</span>
                           </a>
                        </li>
                        <!-- has need multiLevel menu default none -->
                        <li class="sidebar-menu-category d-none"><span class="category-name">MultiLevel</span></li>
                        <li class="slide has-sub d-none">
                           <a href="javascript:void(0);" class="sidebar-menu-item">
                              <div class="side-menu-icon">
                                 <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.07151 0.999245V15.1414M8.07151 15.1414L15.1426 8.07031M8.07151 15.1414L1.00044 8.07031" stroke="#565659" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                 </svg>
                              </div>
                              <span class="sidebar-menu-label">MultiLevel Menu</span>
                              <i class="fa-regular fa-angle-down side-menu-angle"></i>
                           </a>
                           <ul class="sidebar-menu child1">
                              <li class="slide">
                                 <a href="javascript:void(0);" class="sidebar-menu-item">Level-1.0</a>
                              </li>
                              <li class="slide has-sub">
                                 <a href="javascript:void(0);" class="sidebar-menu-item">Level-2.0
                                 <i class="fa-regular fa-angle-down side-menu-angle"></i></a>
                                 <ul class="sidebar-menu child2">
                                    <li class="slide">
                                       <a href="javascript:void(0);" class="sidebar-menu-item">Level-2.1</a>
                                    </li>
                                    <li class="slide has-sub">
                                       <a href="javascript:void(0);" class="sidebar-menu-item">Level-2.2
                                       <i class="fa-regular fa-angle-down side-menu-angle"></i></a>
                                       <ul class="sidebar-menu child3">
                                          <li class="slide">
                                             <a href="javascript:void(0);" class="sidebar-menu-item">Level-3.0</a>
                                          </li>
                                          <li class="slide">
                                             <a href="leaves-employee.html" class="sidebar-menu-item">Level-3.0</a>
                                          </li>
                                       </ul>
                                    </li>
                                 </ul>
                              </li>
                           </ul>
                        </li>
                     </ul>
                  </nav>
               </div>
            </div>
         </div>
         <!-- Page sidebar end-->

         <!-- App page body contents start -->
         <div class="app-page-body">
            <div class="row g-20">
               <div class="col-xxl-12">
                  <!-- Pages heading -->
                  <div class="pages-heading mb-35">
                     <div class="title-inner">
                        <h3 class="title">Post Normal Ad</h3>
                     </div>
                     <div class="page-links">
                        <ul>
                           <li>
                              <a href="#">P2P Help Center</a>
                           </li>
                           <li class="active">
                              <a href="P2P-order-user.html">My Orders</a>
                           </li>
                           <li class="menu-has-children">
                              <a href="#">More</a>
                              <ul class="dp-menu">
                                 <li>
                                    <a href="payment-method.html">Payment Method</a>
                                 </li>
                                 <li>
                                    <a href="post-normal-ad.html">Post new Ad</a>
                                 </li>
                                 <li>
                                    <a href="#">My ads</a>
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </div>
                  </div>

                  <!-- p2p order area start -->
                  <div class="p2p-order-area">
                     <div class="p2p-order-header">
                        <div class="top-contents">
                           <!-- buy sell box tab -->
                           <div class="buy-sell-box-tab td-tab">
                              <ul class="nav nav-tabs" id="myTab" role="tablist">
                                 <li class="nav-item" role="presentation">
                                   <button class="nav-link active" id="order-buy-tab" data-bs-toggle="tab" data-bs-target="#order-buy-tab-pane" type="button" role="tab" aria-controls="order-buy-tab-pane" aria-selected="true">Buy</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                   <button class="nav-link" id="order-sell-tab" data-bs-toggle="tab" data-bs-target="#order-sell-tab-pane" type="button" role="tab" aria-controls="order-sell-tab-pane" aria-selected="false">Sell</button>
                                 </li>
                               </ul>
                           </div>
                           <!-- p2p currency options tab -->
                           <div class="p2p-currency-options-tab td-tab">
                              <ul class="nav nav-tabs" id="myTab" role="tablist">
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link active" id="p2p-order-USDT-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-USDT-tab-pane" type="button" role="tab" aria-controls="p2p-order-USDT-tab-pane" aria-selected="true">USDT</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-BTC-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-BTC-tab-pane" type="button" role="tab" aria-controls="p2p-order-BTC-tab-pane" aria-selected="false">BTC</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-USDC-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-USDC-tab-pane" type="button" role="tab" aria-controls="p2p-order-USDC-tab-pane" aria-selected="false">USDC</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-FDUSD-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-FDUSD-tab-pane" type="button" role="tab" aria-controls="p2p-order-FDUSD-tab-pane" aria-selected="false">FDUSD</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-ETH-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-ETH-tab-pane" type="button" role="tab" aria-controls="p2p-order-ETH-tab-pane" aria-selected="false">ETH</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-ADA-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-ADA-tab-pane" type="button" role="tab" aria-controls="p2p-order-ADA-tab-pane" aria-selected="false">ADA</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-SHIB-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-SHIB-tab-pane" type="button" role="tab" aria-controls="p2p-order-SHIB-tab-pane" aria-selected="false">SHIB</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-DOGE-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-DOGE-tab-pane" type="button" role="tab" aria-controls="p2p-order-DOGE-tab-pane" aria-selected="false">DOGE</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-TRX-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-TRX-tab-pane" type="button" role="tab" aria-controls="p2p-order-TRX-tab-pane" aria-selected="false">TRX</button>
                                 </li>
                                 <li class="nav-item" role="presentation">
                                 <button class="nav-link" id="p2p-order-TRUPM-tab" data-bs-toggle="tab" data-bs-target="#p2p-order-TRUPM-tab-pane" type="button" role="tab" aria-controls="p2p-order-TRUPM-tab-pane" aria-selected="false">TRUPM</button>
                                 </li>
                              </ul>
                           </div>
                        </div>
                        <!-- p2p order filters -->
                        <div class="p2p-order-filters">
                           <!-- Currency Filter -->
                           <div class="filter-item">
                              <div class="td-form-group has-multiple">
                                 <div class="input-field-inner">
                                    <div class="input-field">
                                       <input type="text" class="form-control" placeholder="Amount">
                                    </div>
                                    <div class="input-field">
                                       <select class="select2Icons">
                                          <option value="BTC" data-image="../assets/images/currency-icon/btc.png">Bitcoin (BTC)</option>  
                                          <option value="ETC" data-image="../assets/images/currency-icon/eth.png">Ethereum Classic (ETC)</option>  
                                          <option value="SOL" data-image="../assets/images/currency-icon/sol.png">Solana (SOL)</option>  
                                          <option value="XMR" data-image="../assets/images/currency-icon/xmr.png">Monero (XMR)</option>  
                                          <option value="BNB" data-image="../assets/images/currency-icon/bnb.png">Binance Coin (BNB)</option>  
                                       </select>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <!-- Filter type Search -->
                           <div class="filter-item">
                              <div class="multiple-select fix">
                                 <select id="payment-methods" class="form-select" multiple="multiple">
                                    <option value="all">All Payments</option>
                                    <option value="paypal">PayPal</option>
                                    <option value="bank_transfer">Bank Transfer</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="meghna_bank">Meghna Bank LTD</option>
                                    <option value="dutch_bangla">Dutch-Bangla Bank</option>
                                    <option value="bikash1">Bikash</option>
                                    <option value="bikash2">Bikash</option>
                                 </select>
                              </div>
                           </div>
                           <!-- Order Type Filter -->
                           <div class="filter-item">
                              <div class="filter-option">
                                 <div class="fiter-option-icon">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path fill-rule="evenodd" clip-rule="evenodd" d="M3.71475 1.6875C3.72646 1.6875 3.73821 1.6875 3.75 1.6875L14.2853 1.6875C14.7855 1.68748 15.2131 1.68746 15.5548 1.73057C15.9173 1.77629 16.2676 1.87897 16.5554 2.14893C16.8482 2.42366 16.9645 2.76642 17.0156 3.12304C17.0626 3.45063 17.0625 3.85798 17.0625 4.32269L17.0625 4.90509C17.0625 5.27147 17.0625 5.58821 17.0352 5.85233C17.0057 6.13655 16.9414 6.4032 16.7869 6.6592C16.6336 6.91328 16.4271 7.0977 16.1891 7.26303C15.9649 7.41877 15.6784 7.58002 15.3415 7.76966L13.1347 9.01196C12.6323 9.29474 12.4574 9.39657 12.3406 9.49795C12.0724 9.73078 11.9189 9.98911 11.8467 10.3128C11.8158 10.4511 11.8125 10.6254 11.8125 11.1547L11.8125 13.2037C11.8125 13.8797 11.8126 14.4535 11.743 14.8947C11.6691 15.3638 11.4974 15.8137 11.0473 16.0952C10.6076 16.3702 10.1231 16.345 9.65235 16.2332C9.19894 16.1255 8.6402 15.9071 7.96977 15.6449L7.90461 15.6195C7.59057 15.4967 7.31558 15.3892 7.09788 15.2768C6.86392 15.156 6.64665 15.0057 6.48042 14.7718C6.31231 14.5352 6.24491 14.2815 6.21486 14.0222C6.18746 13.7858 6.18748 13.502 6.1875 13.1855L6.1875 11.1547C6.1875 10.6254 6.18416 10.4512 6.15331 10.3128C6.08115 9.98912 5.92757 9.73078 5.65938 9.49795C5.5426 9.39657 5.36769 9.29474 4.86535 9.01196L2.65853 7.76967C2.32162 7.58002 2.03515 7.41877 1.81094 7.26303C1.57293 7.0977 1.36643 6.91328 1.2131 6.6592C1.05861 6.4032 0.99428 6.13655 0.964836 5.85233C0.937473 5.58821 0.937487 5.27147 0.937503 4.90509L0.937504 4.361C0.937504 4.34818 0.937503 4.33541 0.937502 4.32268C0.937467 3.85797 0.937436 3.45063 0.98438 3.12304C1.03548 2.76642 1.15182 2.42366 1.44463 2.14893C1.73237 1.87897 2.08271 1.77629 2.44518 1.73057C2.78694 1.68746 3.21455 1.68748 3.71475 1.6875ZM2.58597 2.84672C2.33573 2.87829 2.25615 2.93018 2.21438 2.96937C2.17769 3.00379 2.12928 3.06439 2.098 3.28262C2.06386 3.52091 2.0625 3.84651 2.0625 4.361V4.87836C2.0625 5.27902 2.0632 5.53708 2.08385 5.73641C2.10308 5.92204 2.13609 6.0113 2.1763 6.07793C2.21768 6.14649 2.28638 6.2235 2.45275 6.33907C2.62804 6.46083 2.86752 6.59631 3.23245 6.80174L5.41722 8.03162C5.43768 8.04313 5.45783 8.05447 5.47768 8.06564C5.89671 8.30138 6.18209 8.46193 6.3969 8.64842C6.84042 9.03347 7.12498 9.50116 7.25135 10.068C7.31277 10.3434 7.31267 10.6523 7.31252 11.0884C7.31251 11.1102 7.3125 11.1323 7.3125 11.1547V13.1568C7.3125 13.5109 7.31338 13.7287 7.33238 13.8927C7.34957 14.041 7.37664 14.0908 7.39748 14.1201C7.42019 14.1521 7.46518 14.2004 7.61402 14.2772C7.77333 14.3595 7.99281 14.446 8.33942 14.5815C9.06018 14.8633 9.54498 15.0514 9.91232 15.1386C10.2713 15.2239 10.3896 15.1797 10.4508 15.1414C10.5017 15.1095 10.5805 15.0448 10.6317 14.7195C10.6858 14.3764 10.6875 13.8926 10.6875 13.1568V11.1547C10.6875 11.1323 10.6875 11.1102 10.6875 11.0884C10.6873 10.6523 10.6872 10.3434 10.7487 10.068C10.875 9.50116 11.1596 9.03347 11.6031 8.64842C11.8179 8.46194 12.1033 8.30139 12.5223 8.06565C12.5422 8.05448 12.5623 8.04314 12.5828 8.03162L14.7676 6.80174C15.1325 6.59631 15.372 6.46083 15.5473 6.33907C15.7136 6.2235 15.7823 6.14649 15.8237 6.07793C15.8639 6.0113 15.8969 5.92204 15.9162 5.73641C15.9368 5.53708 15.9375 5.27902 15.9375 4.87836V4.361C15.9375 3.84651 15.9361 3.52091 15.902 3.28262C15.8707 3.06439 15.8223 3.00379 15.7856 2.96937C15.7439 2.93018 15.6643 2.87829 15.414 2.84672C15.1511 2.81356 14.7953 2.8125 14.25 2.8125H3.75C3.20473 2.8125 2.84891 2.81356 2.58597 2.84672Z" fill="white"/>
                                    </svg>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="p2p-order-body">
                        <div class="bottom-content">
                           <!-- buy sell box content -->
                           <div class="tab-content" id="orderBuySellTabContent">
                              <!-- buy order tab -->
                              <div class="tab-pane fade show active" id="order-buy-tab-pane" role="tabpanel" aria-labelledby="order-buy-tab" tabindex="0">
                                 <!-- p2p currency options tab content -->
                                 <div class="tab-content" id="buyTabContent">
                                    <!-- p2p order USDT tab -->
                                    <div class="tab-pane fade show active" id="p2p-order-USDT-tab-pane" role="tabpanel" aria-labelledby="p2p-order-USDT-tab" tabindex="0">
                                       <div class="p2b-order-currency-contents table-responsive">
                                          <table class="td-table recent-table">
                                             <thead>
                                                <tr>
                                                   <th>Advertisers
                                                   <button type="button" class="" data-bs-toggle="tooltip" 
                                                   data-bs-placement="bottom" title="Complete identity verification to access all Pocketwage services" 
                                                   data-bs-custom-class="custom-tooltip">
                                                   <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <g clip-path="url(#xzxzx)">
                                                      <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"/>
                                                      <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"/>
                                                      <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"/>
                                                      </g>
                                                      <defs>
                                                      <clipPath id="clip0_462_16647">
                                                      <rect width="10" height="10" fill="white"/>
                                                      </clipPath>
                                                      </defs>
                                                   </svg> 
                                                   </button>
                                                   </th>
                                                   <th>Price</th>
                                                   <th>Available/Order Limit</th>
                                                   <th>Payment</th>
                                                   <th>Trade <small class="text-green">( 0.9% Fee)</small></th>
                                                </tr>
                                             </thead>
                                             <tbody>
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-online">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                               <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                               <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                               <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                               </svg>
                                                               </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr>
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-online">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                               <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#03A66D"/>
                                                               <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#03A66D"/>
                                                               </svg>
                                                               </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr>
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-online">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                               <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                               <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                               <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                               </svg>
                                                               </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr>
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-online">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                               <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#A6EF67"/>
                                                               <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#A6EF67"/>
                                                               </svg>
                                                               
                                                               </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr>
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-offline">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon">
                                                               <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                  <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#E73B79"/>
                                                                  <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#E73B79"/>
                                                                  </svg>
                                                                  
                                                             </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr> 
                                                <tr>
                                                   <td>
                                                      <div class="table-advertiser is-online">
                                                         <span class="avatar">M</span>
                                                         <div>
                                                             <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                               <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                               <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                               <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                               </svg>
                                                               </span></p>
                                                             <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                             <p class="status">Online <span class="devider">|</span> 15min</p>
                                                         </div>
                                                     </div>
                                                   </td>
                                                   <td>
                                                      <span class="text-white">1.05 USD</span>
                                                   </td>
                                                   <td>
                                                      <span class="d-block mb-1">1,036.0000 USDT</span>
                                                      <span class="text-white">$1,046-$1,046</span>
                                                   </td>
                                                   <td>
                                                      <span class="white-text">Bank Transfer</span>
                                                   </td>
                                                   <td>
                                                      <!-- Button trigger modal -->
                                                      <button type="button" class="td-badge fill-badge-success" data-bs-toggle="modal" data-bs-target="#cryptocurrencyBuyModal"> Buy USDT</button>
                                                   </td>
                                                </tr> 
                                                <!-- Add more rows as necessary -->
                                             </tbody>
                                          </table>
                                       </div>
                                    </div>
                                    <!-- p2p order BTC tab -->
                                    <div class="tab-pane fade" id="p2p-order-BTC-tab-pane" role="tabpanel" aria-labelledby="p2p-order-BTC-tab" tabindex="0">BTC</div>
                                    <!-- p2p order USDC tab -->
                                    <div class="tab-pane fade" id="p2p-order-USDC-tab-pane" role="tabpanel" aria-labelledby="p2p-order-USDC-tab" tabindex="0">USDC</div>
                                    <!-- p2p order FDUSD tab -->
                                    <div class="tab-pane fade" id="p2p-order-FDUSD-tab-pane" role="tabpanel" aria-labelledby="p2p-order-FDUSD-tab" tabindex="0">FDUSD</div>
                                    <!-- p2p order ETH tab -->
                                    <div class="tab-pane fade" id="p2p-order-ETH-tab-pane" role="tabpanel" aria-labelledby="p2p-order-ETH-tab" tabindex="0">ETH</div>
                                    <!-- p2p order ADA tab -->
                                    <div class="tab-pane fade" id="p2p-order-ADA-tab-pane" role="tabpanel" aria-labelledby="p2p-order-ADA-tab" tabindex="0">ADA</div>
                                    <!-- p2p order SHIB tab -->
                                    <div class="tab-pane fade" id="p2p-order-SHIB-tab-pane" role="tabpanel" aria-labelledby="p2p-order-SHIB-tab" tabindex="0">SHIB</div>
                                    <!-- p2p order SHIB tab -->
                                    <div class="tab-pane fade" id="p2p-order-DOGE-tab-pane" role="tabpanel" aria-labelledby="p2p-order-DOGE-tab" tabindex="0">DOGE</div>
                                    <!-- p2p order TRX tab -->
                                    <div class="tab-pane fade" id="p2p-order-TRX-tab-pane" role="tabpanel" aria-labelledby="p2p-order-TRX-tab" tabindex="0">TRX</div>
                                    <!-- p2p order TRUPM tab -->
                                    <div class="tab-pane fade" id="p2p-order-TRUPM-tab-pane" role="tabpanel" aria-labelledby="p2p-order-TRUPM-tab" tabindex="0">TRUPM</div>
                                 </div>
                              </div>
                              <!-- sell order tab -->
                              <div class="tab-pane fade" id="order-sell-tab-pane" role="tabpanel" aria-labelledby="order-sell-tab" tabindex="0">
                                 <div class="p2b-order-currency-contents table-responsive">
                                    <table class="td-table recent-table">
                                       <thead>
                                          <tr>
                                             <th>Advertisers
                                             <button type="button" class="" data-bs-toggle="tooltip" 
                                             data-bs-placement="bottom" title="Complete identity verification to access all Pocketwage services" 
                                             data-bs-custom-class="custom-tooltip">
                                             <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g clip-path="url(#xzxzx)">
                                                <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"/>
                                                <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"/>
                                                <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"/>
                                                </g>
                                                <defs>
                                                <clipPath id="clip0_462_16647">
                                                <rect width="10" height="10" fill="white"/>
                                                </clipPath>
                                                </defs>
                                             </svg> 
                                             </button>
                                             </th>
                                             <th>Price</th>
                                             <th>Available/Order Limit</th>
                                             <th>Payment</th>
                                             <th>Trade <small class="text-green">( 0.9% Fee)</small></th>
                                          </tr>
                                       </thead>
                                       <tbody>
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-online">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                         <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                         <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                         </svg>
                                                         </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr>
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-online">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#03A66D"/>
                                                         <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#03A66D"/>
                                                         </svg>
                                                         </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr>
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-online">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                         <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                         <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                         </svg>
                                                         </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr>
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-offline">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#A6EF67"/>
                                                         <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#A6EF67"/>
                                                         </svg>
                                                         
                                                         </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr>
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-online">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon">
                                                         <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M13.5 15.7505H10.125V14.0377L8.97975 13.4336L7.875 14.0248V15.7505H4.5C3.87844 15.7505 3.375 16.254 3.375 16.8755V17.438C3.375 17.7485 3.627 18.0005 3.9375 18.0005H14.0625C14.373 18.0005 14.625 17.7485 14.625 17.438V16.8755C14.625 16.254 14.1216 15.7505 13.5 15.7505Z" fill="#E73B79"/>
                                                            <path d="M15.9832 6.40702C16.6273 5.77702 16.2718 4.67958 15.3819 4.54964L12.2089 4.08727C11.8556 4.03552 11.5496 3.81277 11.3916 3.49158L9.97294 0.607078C9.57469 -0.202359 8.42437 -0.202359 8.02669 0.607078L6.60806 3.49158C6.45 3.81277 6.14456 4.03552 5.79075 4.08727L2.61769 4.54964C1.72781 4.67958 1.37231 5.77702 2.01637 6.40702L4.3125 8.65252C4.56844 8.90283 4.68487 9.26339 4.62469 9.61664L4.08244 12.7875C3.93056 13.6768 4.86094 14.3551 5.65687 13.9355L8.49469 12.4387C8.81081 12.2722 9.18881 12.2722 9.50494 12.4387L12.3427 13.9355C13.1387 14.3557 14.0696 13.6773 13.9172 12.7875L13.3749 9.61664C13.3147 9.26339 13.4312 8.90283 13.6871 8.65252L15.9832 6.40702ZM9.84356 9.56208H8.15606C7.845 9.56208 7.59356 9.31008 7.59356 8.99958C7.59356 8.68908 7.845 8.43708 8.15606 8.43708H9.84356C10.1546 8.43708 10.4061 8.68908 10.4061 8.99958C10.4061 9.31008 10.1546 9.56208 9.84356 9.56208ZM10.9686 7.31208H7.03106C6.72 7.31208 6.46856 7.06008 6.46856 6.74958C6.46856 6.43908 6.72 6.18708 7.03106 6.18708H10.9686C11.2796 6.18708 11.5311 6.43908 11.5311 6.74958C11.5311 7.06008 11.2796 7.31208 10.9686 7.31208Z" fill="#E73B79"/>
                                                            </svg>
                                                            
                                                       </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr> 
                                          <tr>
                                             <td>
                                                <div class="table-advertiser is-online">
                                                   <span class="avatar">M</span>
                                                   <div>
                                                       <p class="name">Master_Money_Trader <span class="icon"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                         <path d="M13.9219 16.2348L12.6057 15.8973L12.0207 17.118C11.8125 17.5455 11.205 17.5455 11.0025 17.1123L9.82129 14.5811C11.1882 14.4348 12.4482 13.923 13.5 13.1523L14.5688 15.4473C14.7713 15.8805 14.3832 16.3473 13.9219 16.2348Z" fill="#FFB700"/>
                                                         <path d="M8.17899 14.5811L6.99774 17.1123C6.79524 17.5455 6.18774 17.5455 5.97962 17.118L5.39462 15.8973L4.07837 16.2348C3.61712 16.3473 3.22899 15.8805 3.43149 15.4473L4.50024 13.1523C5.55212 13.923 6.81212 14.4348 8.17899 14.5811Z" fill="#FFB700"/>
                                                         <path d="M9 0.5625C5.42756 0.5625 2.53125 3.45881 2.53125 7.03125C2.53125 10.6037 5.42756 13.5 9 13.5C12.5724 13.5 15.4688 10.6037 15.4688 7.03125C15.4688 3.45881 12.5724 0.5625 9 0.5625ZM11.9188 6.87994L10.8787 7.89356L11.124 9.32512C11.205 9.79706 10.7094 10.1576 10.2853 9.93431L9 9.25875L7.71469 9.93431C7.29056 10.1571 6.795 9.79706 6.876 9.32512L7.12125 7.89356L6.08119 6.87994C5.73806 6.54525 5.92762 5.96306 6.40181 5.89387L7.839 5.68519L8.48194 4.383C8.694 3.95325 9.30656 3.95325 9.51863 4.383L10.1616 5.68519L11.5988 5.89387C12.0729 5.9625 12.2619 6.54525 11.9194 6.87994H11.9188Z" fill="#FFB700"/>
                                                         </svg>
                                                         </span></p>
                                                       <p class="details"> <strong>100</strong> orders <span class="devider">|</span> <strong>100.00%</strong> completion</p>
                                                       <p class="status">Online <span class="devider">|</span> 15min</p>
                                                   </div>
                                               </div>
                                             </td>
                                             <td>
                                                <span class="text-white">1.05 USD</span>
                                             </td>
                                             <td>
                                                <span class="d-block mb-1">1,036.0000 USDT</span>
                                                <span class="text-white">$1,046-$1,046</span>
                                             </td>
                                             <td>
                                                <span class="white-text">Bank Transfer</span>
                                             </td>
                                             <td>
                                                <!-- Button trigger modal -->
                                                <button type="button" class="td-badge fill-badge-danger" data-bs-toggle="modal" data-bs-target="#cryptocurrencySellModal"> Sell USDT</button>
                                             </td>
                                          </tr> 
                                          <!-- Add more rows as necessary -->
                                       </tbody>
                                    </table>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  <!-- p2p order area end -->
                  </div>

                  <div class="p2p-order-pagination d-flex justify-content-end mt-40">
                     <div class="td-pagination">
                        <nav>
                           <ul>
                              <li>
                                 <a href="#" class="disabled"><i class="solar--alt-arrow-left-outline"></i></a>
                              </li>
                              <li>
                                 <a class="current" href="#">1</a>
                              </li>
                              <li>
                                 <a href="#">2</a>
                              </li>
                              <li>
                                 <a href="#">
                                    <i class="solar--menu-dots-bold"></i>
                                 </a>
                              </li>
                              <li>
                                 <a href="#">9</a>
                              </li>
                              <li>
                                 <a href="#">10</a>
                              </li>
                              <li>
                                 <a href="#"><i class="solar--alt-arrow-right-outline"></i></a>
                              </li>
                           </ul>
                        </nav>
                     </div>
                  </div>
                  
                  <!-- p2p works area stat -->
                  <div class="p2p-works-area mt-20">
                     <div class="row justify-content-center section_title_space">
                        <div class="col-xxl-6">
                           <div class="heading text-center">
                              <h2>How P2P Works</h2>
                           </div>
                        </div>
                     </div>
                     <div class="row justify-content-center">
                        <div class="col-xxl-12">
                           <div class="p2p-works-wrapper">
                              <div class="p2p-works-grid">
                                 <!-- p2p works item -->
                                 <div class="p2p-works-item">
                                    <div class="icon">
                                       <img src="../assets/images/p2p-works/digital-wallet.png" alt="Credit Card">
                                    </div>
                                    <div class="contents">
                                       <h4 class="title">Place Buy Order</h4>
                                       <p class="description">Add multiple cards and track your daily expense with quality interface</p>
                                    </div>
                                 </div>
                                 <!-- p2p works item -->
                                 <div class="p2p-works-item">
                                    <div class="icon">
                                       <img src="../assets/images/p2p-works/secure-payment.png" alt="Credit Card">
                                    </div>
                                    <div class="contents">
                                       <h4 class="title">Pay the Seller</h4>
                                       <p class="description">Add multiple cards and track your daily expense with quality interface</p>
                                    </div>
                                 </div>
                                 <!-- p2p works item -->
                                 <div class="p2p-works-item">
                                    <div class="icon">
                                       <img src="../assets/images/p2p-works/credit-card.png" alt="Credit Card">
                                    </div>
                                    <div class="contents">
                                       <h4 class="title">Receive Your Crypto</h4>
                                       <p class="description">Add multiple cards and track your daily expense with quality interface</p>
                                    </div>
                                 </div>
                              </div>
                              <div class="buttons d-flex flex-wrap justify-content-center gap-3 mt-40">
                                 <a href="#" class="td-btn btn-outline-dark-gunmetal">How to Buy</a>
                                 <a href="#" class="td-btn btn-outline-white-10">How to Sell</a>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- p2p works area end -->
               </div>
            </div>
         </div>
         <!-- App page body contents end -->
          
      </div> 
   <!-- Page body end-->

      </div>
   </div>
   <!-- Page wrapper end-->

   <!-- JS here -->
   <script src="../assets/js/jquery-3.7.1.min.js"></script>
   <script src="../assets/js/bootstrap.bundle.min.js"></script>
   <script src="../assets/js/iconify-icon.min.js"></script>
   <script src="../assets/js/scrollbar/simplebar.js"></script>
   <script src="../assets/js/scrollbar/custom.js"></script>
   <script src="../assets/js/popper.min.js"></script>
   <script src="../assets/js/dashboard-script.js"></script>
   <script src="../assets/js/sidebar-menu.js"></script>
   <script src="../assets/js/select2.js"></script>
   <script src="../assets/js/iconify.min.js"></script>

   <!-- for daterangepicker script  -->
   <script src="../assets/js/moment.min.js"></script>
   <script src="../assets/js/daterangepicker.js"></script>
   
   <script src="../assets/js/main.js"></script>
   <script>
      if ($.fn.select2) {
          $(document).ready(function () {
              function formatSelection(option) {
                  let isSelected = $("#payment-methods").val()?.includes(option.id);
                  return $(
                      `<label class="custom-checkbox">
                          <input type="checkbox" value="${option.id}" ${isSelected ? 'checked' : ''}> ${option.text}
                      </label>`
                  );
              }
  
              $('#payment-methods').select2({
                  closeOnSelect: false,
                  allowClear: true,
                  placeholder: "Select Payment Methods",
                  templateResult: formatSelection,
                  templateSelection: function (selectedItems) {
                      if (!selectedItems || selectedItems.length === 0) {
                          return "Select Payment Methods";
                      }
  
                      let values = $("#payment-methods").val() || [];
                      
  
                      // Remove "All Payments" from the displayed list
                      let filteredItems = values.filter(value => value !== "all").map(value => {
                          return $("#payment-methods option[value='" + value + "']").text();
                      });

                      console.log(filteredItems[0]);
                      
  
                      // Show only the first selected item
                      return filteredItems.length ? filteredItems[0] : "Select Payment Methods";
                  }
              });
  
              // Handle checkbox clicks
              $(document).on("click", ".custom-checkbox input", function (e) {
                  let value = $(this).val();
                  let selectedValues = $("#payment-methods").val() || [];
  
                  if (value === "all") {
                      // Select or Deselect all
                      if (this.checked) {
                          $("#payment-methods").val($("#payment-methods option").map(function () {
                              return $(this).val();
                          }).get()).trigger("change");
                      } else {
                          $("#payment-methods").val([]).trigger("change");
                      }
                  } else {
                      if (this.checked) {
                          selectedValues.push(value);
                      } else {
                          selectedValues = selectedValues.filter(val => val !== value);
                      }
                      $("#payment-methods").val(selectedValues).trigger("change");
                  }
  
                  e.stopPropagation(); // Prevent dropdown from closing
              });
  
              // Sync checkboxes when selecting/deselecting items
              $('#payment-methods').on("select2:select select2:unselect", function () {
                  setTimeout(() => {
                      $(".custom-checkbox input").each(function () {
                          let val = $(this).val();
                          let isChecked = $("#payment-methods").val()?.includes(val);
                          $(this).prop("checked", isChecked);
                      });
                  }, 50);
              });
          });
      }
  </script>
</body>

</html>