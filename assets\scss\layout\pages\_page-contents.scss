@use '../../utils' as *;

/*----------------------------------------*/
/*  Terms conditions styles
/*----------------------------------------*/

.td-page-contents {

	h3 {
		font-size: 38px;
		line-height: 1.3;
		margin-bottom: 30px;
	}

	h4 {
		font-size: 24px;
		margin-bottom: 20px;

		@media #{$xs,$sm,$md} {
			font-size: 18px;
			line-height: 1.5;
		}
	}

	p {
		font-size: 18px;
		line-height: 28px;

		@media #{$xs,$sm} {
			font-size: 16px;
		}

		&:not(:last-child) {
			margin-bottom: 30px;
		}
	}

	ul {
		margin-bottom: 40px;

		li {
			margin-bottom: 14px;
			list-style: none;
			color: var(--td-text-primary);
			display: flex;
			gap: 10px;

			span {
				font-size: 16px;
				font-weight: 700;
				display: flex;
				align-items: center;

				i {
					font-size: 5px;
					border-radius: 0;
					color: var(--td-text-primary);
					background-color: transparent;
				}
			}
		}
	}

	a {
		font-size: 17px;
		font-weight: 600;
		line-height: 27px;
		color: var(--td-primary);
		text-decoration: underline;
	}
}