@use '../../utils' as *;

/*----------------------------------------*/
/*  Payment method styles
/*----------------------------------------*/
// payment method info
.payment-method-info {
    display: inline-grid;
    align-items: center;
    grid-template-columns: auto 1px 1fr;
    gap: 30px;
    background: #091628;
    padding: 25px 320px 25px 18px;
    border-radius: 16px;
    overflow: hidden;
    background-image: url(../images/bg/payment-info-bg.png);
    background-repeat: no-repeat;
    background-position: right bottom;

    @media #{$lg} {
        padding: 25px 200px 25px 18px;
    }

    @media #{$md} {
        display: grid;
        padding: 25px 100px 25px 18px;
    }

    @media #{$xs,$sm} {
        display: grid;
        padding: 25px 18px 25px 18px;
        grid-template-columns: 1fr;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        width: max-content;

        .avatar {
            background: #1c2c44;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            font-size: 20px;
            font-weight: 700;
            color: var(--td-white);

            .contents {
                .name {
                    font-size: 20px;
                    font-weight: 700;
                }
            }
        }
    }

    .devider {
        position: relative;
        height: 100%;

        @media #{$xs} {
            height: 1px;
            width: 100%;

        }

        &:before {
            position: absolute;
            content: "";
            border-width: 1px;
            border-style: solid;
            border-image: linear-gradient(90deg, rgba(9, 22, 40, 0.1) 0%, rgb(166, 239, 103) 50%, rgba(9, 22, 40, 0.1) 100%);
            border-image-slice: 1;
            opacity: 0.5;
            width: 100px;
            height: 0px;
            transform-origin: 0 0;
            transform: rotate(90deg);
            top: -25px;

            @media #{$xs,$sm} {
                width: 100%;
                height: 0px;
                transform-origin: 0 0;
                transform: rotate(90deg);
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }

    .payment-status {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        span {
            background: rgba(153, 153, 153, 0.11);
            border-radius: 12px;
            padding: 0px 16px 0px 16px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-weight: 700;

            &.active {
                color: var(--td-white);

                i {
                    color: var(--td-green);
                }
            }
        }
    }
}

// Payment stats
.payment-method-stats {
    display: grid;
    align-items: center;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));

    .stat-box {
        background: #091628;
        border-radius: 12px;
        padding: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        z-index: 1;
        overflow: hidden;
        background-image: url(../images/bg/payment-stats-bg.png);
        background-repeat: no-repeat;
        background-size: cover;

        &::before {
            position: absolute;
            content: "";
            background: #1f4a55;
            border-radius: 50%;
            flex-shrink: 0;
            width: 127px;
            height: 128px;
            filter: blur(70px);
            right: -30px;
            top: 20px;
            ;
        }

        .icon {
            display: flex;
            align-items: center;
            border-radius: 50%;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: #131F31;
            border-radius: 16px;
        }

        .contents {
            span {
                font-weight: 700;
                display: inline-block;
                margin-bottom: rem(14);
            }

            h4 {
                font-size: rem(24);

                @media #{$xs,$sm} {
                    font-size: 20px;
                }
            }
        }
    }
}

// Add payment method card
.add-payment-method-card {
    background: #0e1b2c;
    border-radius: 30px;
    padding: 30px 30px;
    width: 513px;
    margin: 0 auto;

    @media #{$xs} {
        width: 100%;
        padding: 20px 20px;
    }

    .heading {
        margin-bottom: 16px;

        .title {
            font-size: 16px;
            line-height: 12px;
            font-weight: 700;
        }
    }

    .method {
        display: flex;
        gap: 8px 20px;
        margin-bottom: 18px;
        flex-wrap: wrap;
    }

    .input-group {
        margin-bottom: 18px;
    }

    .input-row {
        display: flex;
        gap: 0 30px;
    }

    .buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        gap: 18px;
    }
}

.payment-options-wrapper {
    .payment-attention-text {
        margin-top: 22px;
        font-size: 14px;
        letter-spacing: 0.03em;
        font-weight: 700;

        span {
            color: #eb4e5c;
        }
    }

    .method-list {
        margin-top: 10px;
        // max-height: 200px;
        // overflow-y: auto;
    }

    .method {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: transparent;
        padding: 0 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 14px;
        font-weight: 700;
        height: 50px;

        &:not(:last-child) {
            margin-bottom: 16px;
        }

        &:hover {
            border-color: rgba($primary, $alpha: 0.3);
        }

        input {
            display: none;
        }

        &.selected {
            border-color: rgba($primary, $alpha: 0.3);
            color: var(--td-white);

            .custom-checkbox {
                border-color: var(--td-primary);
                background-color: var(--td-primary);

                &::after {
                    content: '✔';
                    font-size: 12px;
                    color: var(--td-black);
                }
            }
        }
    }

    .custom-checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: 0.3s;
        position: relative;

        &::before {
            position: absolute;
            content: "";
            width: 100%;
            top: 50%;
            left: -12px;
            height: 22px;
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transform: translateY(-50%);
        }
    }
}