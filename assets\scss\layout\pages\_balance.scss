@use '../../utils' as *;

/*----------------------------------------*/
/*  Balance styles
/*----------------------------------------*/

.stat-box-wrapper {
    margin-bottom: 10px;

    .currency-box {
        min-width: 156px;
    }

    .currency-box,
    .stat-box {
        flex-direction: column;
        align-items: flex-start;
        padding: 8px 10px;
        width: 165px;
        background: #091628;
        border-radius: 4px;
        height: 58px;
        display: inline-flex;
        justify-content: center;

        @media #{$x3l,$xl} {
            width: 136px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-weight: 500;
            display: block;
            margin-bottom: 6px;
        }

        .stat-value {
            color: var(--td-white);
            font-size: 12px;
            font-weight: 700;
        }

        .current-price {
            color: #eb4e5c;
            text-align: left;
            font-size: 12px;
            font-weight: 500;
        }

        .change-value {
            color: #eb4e5c;
            text-align: left;
            font-size: 12px;
            font-weight: 700;
            margin-top: 6px;
        }
    }

    .stat-grid {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        column-gap: 20px;
        row-gap: 16px;

        @media #{$xl} {
            column-gap: 12px;
            row-gap: 12px;
        }
    }

    .currency-select {
        position: relative;
        display: flex;
        align-items: center;

        .inner {
            background: rgba(59, 87, 231, 0.11);
            border-radius: 4px;
            padding: 4px 10px 4px 10px;
        }
    }

    .currency-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }

    select {
        appearance: none;
        background: hsl(219.57deg 62.67% 14.71%);
        border: none;
        color: var(--td-white);
        font-size: 12px;
        cursor: pointer;
        width: max-content;
        padding-right: 20px;
        font-weight: 500;
    }

    .dropdown-arrow {
        position: absolute;
        right: 15px;
        width: 12px;
        height: 12px;
        pointer-events: none;
        top: 3px;
    }

    select:focus {
        outline: none;
    }
}

.market-table-inner {
    padding: 12px 8px 8px;
    min-height: 568px;
    overflow-y: auto;
    margin-right: 8px;
    padding-right: 8px;

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
    }
}

.trade-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    margin-right: 10px;

    thead {
        th {
            font-size: 12px;
            font-weight: 700;
            padding-bottom: 12px;
            padding-top: 0;
        }

        tr {
            display: flex;
            justify-content: space-between;
        }
    }

    tbody {
        max-height: 400px;
        display: block;

        tr {
            display: flex;
            justify-content: space-between;
            align-items: center;

            &:hover {
                background-color: #1E2A3C;
            }
        }
    }

    .price-green {
        color: #4AFF80;
    }

    .price-red {
        color: #EB4E5C;
    }

    th,
    td {
        padding: 0.4rem 0.3rem;
        font-size: 12px;
        font-weight: 500;
        ;

        &:last-child {
            text-align: right;
        }
    }
}


.trading-nav-list {
    ul {
        li {
            list-style: none;
            color: var(--td-white);
            text-align: left;
            font-family: "Satoshi-Bold", sans-serif;
            font-size: 14px;
            font-weight: 700;
        }
    }
}