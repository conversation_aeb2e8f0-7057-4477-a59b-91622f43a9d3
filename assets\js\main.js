// ==================================================
// * Project Name   : Moneychain Crypto Instant Exchange System
// * File           :  JS Base
// * Version        :  1.0
// * Last change    :  1 march 2025, Saturday
// * Author         :  tdevs (https://codecanyon.net/user/tdevs/portfolio)
// ==================================================

(function ($) {
  'use strict';

  // When the window has fully loaded
  $(window).on('load', function (event) {
    $('.preloader').delay(500).fadeOut(500);
  });

  // Mobile dropdown menu
  $(document).ready(function () {
    $(".mobile-menu ul ul").hide(); // Hide all submenus initially

    // Main Menu Click
    $(".mobile-menu > ul > li > a").click(function (e) {
      e.preventDefault();

      const $submenu = $(this).siblings("ul");
      const $icon = $(this).find("i");

      if ($submenu.length > 0) {
        const isOpen = $submenu.is(":visible");

        // Close other main menus
        $(".mobile-menu > ul > li > ul").not($submenu).slideUp(300);
        $(".mobile-menu > ul > li > a i").removeClass("ic--round-minus").addClass("ic--round-plus");

        // Toggle the clicked menu
        if (!isOpen) {
          $submenu.stop(true, true).slideDown(300);
          $icon.removeClass("ic--round-plus").addClass("ic--round-minus");
        } else {
          $submenu.stop(true, true).slideUp(300);
          $icon.removeClass("ic--round-minus").addClass("ic--round-plus");
        }
      }
    });

    // Submenu Click (Nested Menus)
    $(".mobile-menu ul ul li > a").click(function (e) {
      e.preventDefault();

      const $submenu = $(this).siblings("ul");
      const $icon = $(this).find("i");

      if ($submenu.length > 0) {
        const isOpen = $submenu.is(":visible");

        // Close only sibling submenus
        $(this).parent().siblings().find("ul").slideUp(300);
        $(this).parent().siblings().find("i").removeClass("ic--round-minus").addClass("ic--round-plus");

        // Toggle clicked submenu
        if (!isOpen) {
          $submenu.stop(true, true).slideDown(300);
          $icon.removeClass("ic--round-plus").addClass("ic--round-minus");
        } else {
          $submenu.stop(true, true).slideUp(300);
          $icon.removeClass("ic--round-minus").addClass("ic--round-plus");
        }
      }
    });
  });

  // Offcanvas Js
  $(".offcanvas-close,.offcanvas-overlay").on("click", function () {
    $(".offcanvas-area").removeClass("info-open");
    $(".offcanvas-overlay").removeClass("overlay-open");
  });
  $(".sidebar-toggle").on("click", function () {
    $(".offcanvas-area").addClass("info-open");
    $(".offcanvas-overlay").addClass("overlay-open");
  });

  // Body overlay Js
  $(".body-overlay").on("click", function () {
    $(".offcanvas-area").removeClass("opened");
    $(".body-overlay").removeClass("opened");
  });

  // Header sticky
  $(window).scroll(function () {
    if ($(this).scrollTop() > 250) {
      $("#header-sticky").addClass("active-sticky");
    } else {
      $("#header-sticky").removeClass("active-sticky");
    }
  });

  // Back to top js  
  if ($(".back-to-top-wrap path").length > 0) {
    var progressPath = document.querySelector(".back-to-top-wrap path");
    var pathLength = progressPath.getTotalLength();
    progressPath.style.transition = progressPath.style.WebkitTransition =
      "none";
    progressPath.style.strokeDasharray = pathLength + " " + pathLength;
    progressPath.style.strokeDashoffset = pathLength;
    progressPath.getBoundingClientRect();
    progressPath.style.transition = progressPath.style.WebkitTransition =
      "stroke-dashoffset 10ms linear";
    var updateProgress = function () {
      var scroll = $(window).scrollTop();
      var height = $(document).height() - $(window).height();
      var progress = pathLength - (scroll * pathLength) / height;
      progressPath.style.strokeDashoffset = progress;
    };
    updateProgress();
    $(window).scroll(updateProgress);
    var offset = 150;
    var duration = 550;
    jQuery(window).on("scroll", function () {
      if (jQuery(this).scrollTop() > offset) {
        jQuery(".back-to-top-wrap").addClass("active-progress");
      } else {
        jQuery(".back-to-top-wrap").removeClass("active-progress");
      }
    });
    jQuery(".back-to-top-wrap").on("click", function (event) {
      event.preventDefault();
      jQuery("html, body").animate({
        scrollTop: 0
      }, duration);
      return false;
    });
  }

  // Apply background image from 'data-background' attribute
  $("[data-background]").each(function () {
    let bg = $(this).attr("data-background");
    if (bg) {
      $(this).css("background-image", `url(${bg})`);
    }
  });

  // Set width from 'data-width' attribute
  $("[data-width]").each(function () {
    let width = $(this).attr("data-width");
    if (width) {
      $(this).css("width", width);
    }
  });

  // Apply background color from 'data-bg-color' attribute
  $("[data-bg-color]").each(function () {
    let bgColor = $(this).attr("data-bg-color");
    if (bgColor) {
      $(this).css("background-color", bgColor);
    }
  });

  // Initialize Theme Switching
  document.addEventListener("DOMContentLoaded", () => {
    // Select the theme toggle button and body
    const themeToggleButton = document.getElementById("theme-toggle");
    const body = document.body;

    // Check saved theme from localStorage
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme === "dark-theme" && !body.classList.contains("not-dark-page")) {
      body.classList.add("dark-theme"); // Apply dark theme if saved and not on landing page
    }

    // Only proceed if the toggle button exists
    if (themeToggleButton) {
      // Toggle theme on button click
      themeToggleButton.addEventListener("click", () => {
        if (body.classList.contains("not-dark-page")) {
          console.warn("Theme toggle disabled on landing page."); // Optional console message
          return; // Prevent toggling theme
        }

        const isDarkMode = body.classList.toggle("dark-theme"); // Toggle class
        localStorage.setItem("theme", isDarkMode ? "dark-theme" : ""); // Save preference
      });
    }
  });

  // Update Swiper on direction change
  document.querySelectorAll(".lang_dir button").forEach(button => {
    button.addEventListener("click", function () {
      const newDirection = button.getAttribute("data-mode");
      document.documentElement.setAttribute("dir", newDirection); // Change HTML direction attribute

      // Update active button
      document.querySelectorAll(".lang_dir button").forEach(btn => btn.classList.remove("active"));
      button.classList.add("active");

      // Reinitialize Swiper with new direction
      initializeSwiper();
    });
  });

  // Initialize Select2
  $(function () {
    if ($.fn.select2) {
      // Function to render icons with images
      function renderIcon(option) {
        if (!option.id) return option.text; // Return plain text for placeholder

        const imgSrc = $(option.element).data("image"); // Get the image URL
        const iconClass = $(option.element).data("icon"); // Get icon class if available

        const imgHtml = imgSrc ? `<img src="${imgSrc}" style="width:20px; margin-right:5px;">` : "";
        const iconHtml = iconClass ? `<i class="${iconClass} mr-1"></i>` : "";

        return $(`<span>${imgHtml} ${iconHtml} ${option.text}</span>`);
      }

      // Initialize Select2 with Icons & Images
      $('.select2Icons').each(function () {
        $(this).select2({
          dropdownParent: $(this).parent(),
          templateResult: renderIcon,
          templateSelection: renderIcon,
          escapeMarkup: function (markup) {
            return markup; // Allows HTML rendering
          }
        });
      });

      // Function to render flags (for `.defaultselect2`)
      function renderFlag(option) {
        if (!option.id) return option.text; // Return plain text for placeholder

        const flagClass = $(option.element).data("icon"); // Get flag class
        const flagHtml = flagClass ? `<i class="${flagClass} mr-1"></i>` : "";

        return $(`<span>${flagHtml} ${option.text}</span>`);
      }

      // Initialize Select2 with Flags (for `.defaultselect2`)
      $('.defaultselect2').each(function () {
        $(this).select2({
          dropdownParent: $(this).parent(),
          templateResult: renderFlag,
          templateSelection: function (option) {
            if ($(this).attr("multiple")) {
              return option.text; // Prevent rendering icon in selected items when multiple
            }
            return renderFlag(option);
          },
          minimumResultsForSearch: -1,
          escapeMarkup: function (markup) {
            return markup;
          }
        });
      });
    }
  });

  // Bootstrap tooltip activation
  var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl)
  })
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    new bootstrap.Tooltip(tooltipTriggerEl, {
      customClass: 'custom-tooltip'
    });
  });

  // Initialize dropdowns
  document.addEventListener('DOMContentLoaded', () => {
    // Notification Dropdown Logic
    function setupNotificationDropdown() {
      const notificationBox = document.querySelector('.notification-panel-box');
      const notificationPanel = document.querySelector('.notification-panel');
      const closeButtons = document.querySelectorAll('.notification-item .close-btn');
      const dropdownClose = document.querySelector('.notifications-dropdown-close');

      if (notificationBox && notificationPanel) {
        notificationBox.addEventListener('click', (e) => {
          e.stopPropagation();
          closeAllDropdowns(); // Close other dropdowns
          notificationPanel.classList.toggle('active');
        });

        // Ensure clicking the close button inside notifications hides the panel
        if (dropdownClose) {
          dropdownClose.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent global document click listener
            notificationPanel.classList.remove('active');
          });
        }

        // Remove individual notification items
        closeButtons.forEach((btn) => {
          btn.addEventListener('click', (e) => {
            e.target.closest('.notification-item').remove();
          });
        });

        // Prevent notification-panel from closing on inner clicks
        notificationPanel.addEventListener('click', (e) => {
          e.stopPropagation();
        });

        // Close notification panel if clicking outside
        document.addEventListener('click', () => {
          notificationPanel.classList.remove('active');
        });
      }
    }

    // User Profile Dropdown Logic
    function setupUserProfileDropdown() {
      const userProfileDrop = document.querySelector('.user-profile-drop');
      const profileDropdownMenu = document.querySelector('.dropdown-menu');

      if (userProfileDrop && profileDropdownMenu) {
        userProfileDrop.addEventListener('click', (event) => {
          event.stopPropagation(); // Prevent closing when clicked inside
          closeAllDropdowns(); // Close other dropdowns
          profileDropdownMenu.classList.toggle('show'); // Toggle the show class
        });

        // Close profile dropdown if clicking outside
        document.addEventListener('click', () => {
          profileDropdownMenu.classList.remove('show'); // Remove show class
        });
      }
    }

    // Language Dropdown Logic
    function setupLanguageDropdown() {
      $(document).on("click", function (e) {
        const target = $(e.target);
        // Prevent closing when clicking inside the language dropdown
        if (!target.closest(".translate_wrapper").length) {
          closeAllDropdowns(); // Close all other dropdowns
        }
      });

      $(document).on("click", function (e) {
        $(".translate_wrapper, .more_lang").removeClass("active");
      });
      $(".translate_wrapper .current_lang").on("click", function (e) {
        e.stopPropagation(); // Prevent global click listener
        const parent = $(this).parent();

        // Toggle the active class
        if (!parent.hasClass("active")) {
          closeAllDropdowns(); // Close other dropdowns
        }
        parent.toggleClass("active");

        setTimeout(() => {
          $(".more_lang").toggleClass("active");
        }, 5);
      });

    }

    // Function to close all dropdowns
    function closeAllDropdowns() {
      // Close notification panel and other dropdowns
      const openDropdowns = document.querySelectorAll('.dropdown-menu.show, .notification-panel.active');
      openDropdowns.forEach(dropdown => dropdown.classList.remove('show', 'active'));

      // Close the language dropdown
      $(".translate_wrapper, .more_lang").removeClass("active");
    }

    // Initialize all dropdowns
    setupNotificationDropdown();
    setupUserProfileDropdown();
    setupLanguageDropdown();
  });

  document.addEventListener("DOMContentLoaded", function () {
    const tabs = document.querySelectorAll(".market-tabs, .menu-tabs");
    const scrollLeft = document.querySelector(".scroll-left");
    const scrollRight = document.querySelector(".scroll-right");

    function scrollTabs(direction) {
      tabs.forEach(tab => {
        tab.scrollBy({ left: direction * 100, behavior: "smooth" });
      });
    }

    // Scroll with Arrow Buttons
    scrollLeft.addEventListener("click", () => scrollTabs(-1));
    scrollRight.addEventListener("click", () => scrollTabs(1));

    // Mouse Scroll Left/Right
    tabs.forEach(tab => {
      tab.addEventListener("wheel", (event) => {
        event.preventDefault();
        tab.scrollBy({ left: event.deltaY * 3, behavior: "smooth" });
      });
    });
  });

  $(document).on('click', '.accordion-item', function () {
    $('.accordion-item').removeClass('accordion-active'); // Remove active class from all
    $(this).addClass('accordion-active'); // Add active class to the clicked item
 });

  

  //  document.addEventListener("DOMContentLoaded", () => {
  //   const loadingOverlay = document.querySelector(".td-loading-overlay");
  //   const loadingDiv = document.getElementById("td-loadingDiv");

  //   if (!loadingOverlay || !loadingDiv) return; // Ensure elements exist

  //   let progress = 0; // Start from 0%

  //   const loading = (percentage) => {
  //      loadingOverlay.style.width = `${percentage}%`;
  //   };

  //   window.loading = loading;

  //   window.removeLoading = () => {
  //      setTimeout(() => {
  //         loadingDiv.style.opacity = "0"; // Fade out smoothly
  //         setTimeout(() => {
  //            loadingDiv?.remove(); // Remove after fade-out
  //         }, 500);
  //      }, 1000);
  //   };

  //   const animateLoading = () => {
  //      if (progress < 100) { // Ensure it fully loads
  //         progress += Math.floor(Math.random() * 5) + 1;
  //         if (progress > 100) progress = 100;
  //         loading(progress);
  //         setTimeout(animateLoading, 50);
  //      } else {
  //         removeLoading(); // Remove after completion
  //      }
  //   };

  //   animateLoading(); // Start animation

  //   let mode = localStorage.getItem("droipMode") || "dark";
  //   document.documentElement.setAttribute("data-mode", mode);

  //   if (mode === "dark") {
  //      loadingDiv.style.backgroundColor = "#1d1d1d";
  //   } else {
  //      loadingDiv.style.backgroundColor = "#FFFFFF";
  //   }
  // });
  
  const loadingOverlay = document.querySelector(".td-loading-overlay");
  const loadingDiv = document.getElementById("td-loadingDiv");

  if (!loadingOverlay || !loadingDiv) return; // Ensure elements exist

  const startTime = performance.now(); // Record load start time
  let progress = 0;

  // Function to update loading bar width
  const loading = (percentage) => {
    loadingOverlay.style.width = `${percentage}%`;
  };

  // Store globally for debugging
  window.loading = loading;

  // Remove loading screen smoothly
  window.removeLoading = () => {
    setTimeout(() => {
      loadingDiv.style.opacity = "0"; // Fade out smoothly
      setTimeout(() => {
        loadingDiv?.remove(); // Remove after fade-out
      }, 500);
    }, 800);
  };

  // Function to animate progress based on actual load time
  const animateLoading = (duration) => {
    const start = performance.now();
    const step = () => {
      const elapsed = performance.now() - start;
      progress = Math.min((elapsed / duration) * 100, 100); // Scale progress
      loading(progress);

      if (progress < 100) {
        requestAnimationFrame(step); // Continue until complete
      } else {
        removeLoading(); // Remove after completion
      }
    };
    step();
  };

  // When page fully loads, calculate actual load time & animate
  window.addEventListener("load", () => {
    const loadTime = performance.now() - startTime; // Measure actual load time
    animateLoading(loadTime); // Use it for progress animation

    // Set theme mode
    let mode = localStorage.getItem("droipMode") || "dark";
    document.documentElement.setAttribute("data-mode", mode);
    loadingDiv.style.backgroundColor = mode === "dark" ? "#1d1d1d" : "#FFFFFF";
  });

// Event listener for mouseover on menu items with submenus
$(document).on('mouseover', '.td-main-menu nav > ul > li > ul > li.menu-has-children', function () {
  // When hovered, add the 'active' class to the closest parent <ul> element
  $(this).closest('ul').addClass('active'); 
});

// Event listener for mouseout when the hover ends on menu items with submenus
$(document).on('mouseout', '.td-main-menu nav > ul > li > ul > li.menu-has-children', function () {
  // When hover ends, remove the 'active' class from the closest parent <ul> element
  $(this).closest('ul').removeClass('active');
});

      // Initialize Odometer
      $(document).ready(function () {
        function startOdometer(entries, observer) {
           entries.forEach(entry => {
              if (entry.isIntersecting) {
                 var countNumber = $(entry.target).attr('data-count');
                 $(entry.target).html(countNumber);
                 observer.unobserve(entry.target); // Stop observing after it appears
              }
           });
        }

        // Initialize Intersection Observer
        let observer = new IntersectionObserver(startOdometer, { threshold: 0.5 });

        // Target all odometer elements
        $('.odometer').each(function () {
           observer.observe(this);
        });
     });

})(jQuery);