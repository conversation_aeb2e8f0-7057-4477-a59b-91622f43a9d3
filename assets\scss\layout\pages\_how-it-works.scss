@use '../../utils' as *;

/*----------------------------------------*/
/*  how it work styles
/*----------------------------------------*/
.how-it-work-shapes {
    .shape-one {
        position: absolute;
        bottom: 90px;
        right: 100px;
    }

    .shape-two {
        position: absolute;
        top: 20px;
        left: 30px;
    }

    .shape-three {
        position: absolute;
        right: 15%;
        top: 70px;
    }

}

.how-it-work {
    padding: 0px 50px;
    position: relative;
    z-index: 1;

    @media #{$xs,$sm,$md,$lg} {
        padding: 0;
    }

    &::before {
        position: absolute;
        content: "";
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        width: 320px;
        height: 1px;
        top: 40px;
        left: 65%;
        z-index: -1;

        @media #{$xl} {
            width: 270px;
        }

        @media #{$lg} {
            width: 220px;
        }

        @media #{$xs,$sm,$md} {
            display: none;
        }
    }

    &:nth-child(3) {
        &::before {
            display: none;
        }
    }

    .icon {
        margin-bottom: 25px;

        img {
            width: 80px;
            height: 80px;
        }
    }

    .contents {
        .title {
            font-size: 20px;
            font-weight: 700;
            color: var(--td-white);
            margin-bottom: 10px;

            @media #{$xs} {
                font-size: 18px;
            }
        }
    }
}

.row {
    [class*=col-] {

        &:nth-child(3),
        &:nth-child(6),
        &:nth-child(3),
        &:last-child {
            .how-it-work {
                &::before {
                    display: none;
                }
            }
        }
    }
}

// Styles Two
.how-it-work-contents-two {
    padding-left: 140px;
    padding-right: 80px;

    @media #{$xl} {
        padding-left: 60px;
    }

    @media #{$xs,$sm,$md,$lg} {
        padding-left: 0;
        padding-right: 0;
    }
}

.how-it-work-grid {
    display: flex;
    flex-direction: column;
    row-gap: 76px;
}

.how-it-work-two {
    display: flex;
    align-items: center;
    gap: 24px;

    @media #{$xs} {
        gap: 18px;
    }

    &:not(:last-child) {
        position: relative;
    }

    &:not(:last-child) {
        &::before {
            position: absolute;
            content: "";
            height: 40px;
            width: 40px;
            background-image: url(../images/how-it-work/down-arrow.png);
            background-repeat: no-repeat;
            top: calc(100% + 16px);
            left: 30px;
        }
    }

    .icon {
        border: 1px solid #0E1F37;
        border-radius: 50%;
        padding: 6px;
    }

    .contents {
        .title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 2px;
        }

        .description {
            font-size: 14px;
            font-weight: 500;
        }
    }
}

.how-it-work-shaspes-two {
    .shape-one {
        position: absolute;
        bottom: -100px;
        width: 396px;
        left: 0;

        @media #{$xxl,$xl} {
            width: 280px;
        }

        @media #{$sm,$md,$lg} {
            width: 230px;
        }
    }
}