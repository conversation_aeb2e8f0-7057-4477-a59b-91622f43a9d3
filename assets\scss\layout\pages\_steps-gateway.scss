@use '../../utils' as *;

/*----------------------------------------*/
/*  Steps Gateway styles
/*----------------------------------------*/

.pages-heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px 12px;

    .title-inner {
        .title {
            font-size: rem(24);
            font-weight: 700;
        }
    }

    .page-links {
        ul {

            li {
                position: relative;
                list-style: none;

                .dp-menu {
                    width: 170px;
                    position: absolute;
                    right: 0px;
                    opacity: 0;
                    pointer-events: none;
                    transition: all 0.5s;
                    display: flex;
                    flex-direction: column;
                    z-index: 11;
                    padding: 10px 10px;
                    background: #171c35;
                    border-radius: 12px;
                    gap: 4px;

                    li {
                        width: 100%;
                        &.active {
                            a {
                                background-color: var(--td-primary);
                                color: var(--td-heading);
                            }
                        }

                        &:hover {
                            a {
                                background: rgba(255, 255, 255, 0.04);
                                color: var(--td-white);
                            }
                        }

                        a {
                            font-size: 13px;
                            font-weight: 700;
                            border-radius: 8px;
                            padding: 8px 10px;
                            display: block;
                        }
                    }
                }
            }
        }

        &>ul {
            display: flex;
            align-items: center;
            gap: rem(12);

            &>li {
                &.active {

                   > a,
                    .link {
                        color: var(--td-primary);
                        text-decoration: underline;
                    }
                }

                >a,
                .link {
                    font-size: 14px;
                    font-weight: 700;
                    position: relative;
                    padding: 8px 0;
                    display: block;

                    &:hover {
                        color: var(--td-primary);
                        text-decoration: underline;
                    }
                }

                &:hover {
                    &>ul {
                        opacity: 1;
                        pointer-events: all;
                    }
                }

                &:has(ul) {
                    &>a {
                        &::after {
                            content: "";
                            margin-inline-start: 6px;
                            position: relative;
                            display: inline-block;
                            width: 7px;
                            height: 7px;
                            border-bottom: 1.5px solid var(--td-text-primary);
                            border-right: 1.5px solid var(--td-text-primary);
                            color: rgba(255, 255, 255, 0.5);
                            top: calc(50% + -9px);
                            transform: translateY(-50%) rotate(45deg);
                        }
                    }
                }
            }
        }
    }
}

.dashboard-middle-box {
    max-width: 606px;
    margin: 0 auto;
    background: #091628;
    border-radius: 16px;
    padding: rem(30) rem(30);
    position: relative;
    z-index: 1;

    @media #{$xs} {
        padding: rem(20) rem(20);
    }
}

.gateway-steps-wrapper {
    margin-bottom: 30px;

    .multi-steps {
        >li.is-active {
            ~ {
                li {
                    &:before {
                        content: counter(stepNum);
                        font-family: inherit;
                        font-weight: 700;
                        background-color: #e1e1e1;
                        border-color: #e1e1e1;
                        color: #808080;
                    }

                    &:after {
                        background-color: #e1e1e1;
                    }

                    color: #808080;
                }
            }

            &:before {
                content: counter(stepNum);
                font-family: inherit;
                font-weight: 700;
                background-color: #03A66D;
                border-color: #03A66D;
                color: var(--td-white);
                animation: pulse 2s infinite;
            }

            &:after {
                background-color: #e1e1e1;
            }
        }

        display: table;
        table-layout: fixed;
        width: 100%;

        >li {
            counter-increment: stepNum;
            text-align: center;
            display: table-cell;
            position: relative;
            color: #03A66D;
            font-size: 16px;
            font-weight: 500;

            @media #{$xs} {
                font-size: 14px;
            }

            &:before {
                content: "";
                content: "✓;";
                content: "𐀃";
                content: "𐀄";
                content: "✓";
                display: block;
                margin: 0 auto 4px;
                background-color: #03A66D;
                width: 24px;
                height: 24px;
                border-width: 2px;
                border-style: solid;
                border-color: #03A66D;
                border-radius: 50%;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: rem(10);
            }

            &:last-child {
                &:after {
                    display: none;
                }
            }
        }
    }

    .is-complete {
        background: linear-gradient(to right, #03A66D 50%, #e1e1e1 50%);
        background-size: 200% 100%;
        background-position: right bottom;
        transition: all 0.5s ease-out;
    }

    .progress-bar {
        cursor: pointer;
        user-select: none;
        background-color: #e1e1e1;
        height: 2px;
        overflow: hidden;
        position: absolute;
        left: 50%;
        bottom: calc(50% + 15px);
        width: 100%;
        z-index: -1;
    }

    .progress-bar--success {
        background-color: #03A66D;
    }

    .progress-bar__bar {
        background-color: #1A2431;
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        transition: all 500ms ease-out;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 #03A66D70;
    }

    100% {
        box-shadow: 0 0 0 10px #03A66D00;
    }
}

@keyframes nextStep {
    0% {
        width: 0%;
    }

    100% {
        width: 100%;
    }
}


.payment-status-box {
    text-align: center;
    padding-top: 10px;
    margin-bottom: rem(35);

    .payment-title {
        font-size: rem(30);
        margin-top: rem(10);
        margin-bottom: rem(6);
        @media #{$xs,$sm} {
            font-size: rem(24);
        }
    }

    .payment-description {
        font-size: 14px;
        font-weight: 500;

        .btn-copy {
            i {
                width: 16px;
                height: 17px;
                position: relative;
                top: 2px;
            }
        }
    }
}