@charset "UTF-8";
.mt-1 {
  margin-top: 1px;
}

.mb-1 {
  margin-bottom: 1px;
}

.ml-1 {
  margin-inline-start: 1px;
}

.mr-1 {
  margin-inline-end: 1px;
}

.mt-2 {
  margin-top: 2px;
}

.mb-2 {
  margin-bottom: 2px;
}

.ml-2 {
  margin-inline-start: 2px;
}

.mr-2 {
  margin-inline-end: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mb-3 {
  margin-bottom: 3px;
}

.ml-3 {
  margin-inline-start: 3px;
}

.mr-3 {
  margin-inline-end: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-inline-start: 4px;
}

.mr-4 {
  margin-inline-end: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-inline-start: 5px;
}

.mr-5 {
  margin-inline-end: 5px;
}

.mt-6 {
  margin-top: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-inline-start: 6px;
}

.mr-6 {
  margin-inline-end: 6px;
}

.mt-7 {
  margin-top: 7px;
}

.mb-7 {
  margin-bottom: 7px;
}

.ml-7 {
  margin-inline-start: 7px;
}

.mr-7 {
  margin-inline-end: 7px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-inline-start: 8px;
}

.mr-8 {
  margin-inline-end: 8px;
}

.mt-9 {
  margin-top: 9px;
}

.mb-9 {
  margin-bottom: 9px;
}

.ml-9 {
  margin-inline-start: 9px;
}

.mr-9 {
  margin-inline-end: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-inline-start: 10px;
}

.mr-10 {
  margin-inline-end: 10px;
}

.mt-11 {
  margin-top: 11px;
}

.mb-11 {
  margin-bottom: 11px;
}

.ml-11 {
  margin-inline-start: 11px;
}

.mr-11 {
  margin-inline-end: 11px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-inline-start: 12px;
}

.mr-12 {
  margin-inline-end: 12px;
}

.mt-13 {
  margin-top: 13px;
}

.mb-13 {
  margin-bottom: 13px;
}

.ml-13 {
  margin-inline-start: 13px;
}

.mr-13 {
  margin-inline-end: 13px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.ml-14 {
  margin-inline-start: 14px;
}

.mr-14 {
  margin-inline-end: 14px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-inline-start: 15px;
}

.mr-15 {
  margin-inline-end: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-inline-start: 16px;
}

.mr-16 {
  margin-inline-end: 16px;
}

.mt-17 {
  margin-top: 17px;
}

.mb-17 {
  margin-bottom: 17px;
}

.ml-17 {
  margin-inline-start: 17px;
}

.mr-17 {
  margin-inline-end: 17px;
}

.mt-18 {
  margin-top: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-inline-start: 18px;
}

.mr-18 {
  margin-inline-end: 18px;
}

.mt-19 {
  margin-top: 19px;
}

.mb-19 {
  margin-bottom: 19px;
}

.ml-19 {
  margin-inline-start: 19px;
}

.mr-19 {
  margin-inline-end: 19px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-inline-start: 20px;
}

.mr-20 {
  margin-inline-end: 20px;
}

.mt-21 {
  margin-top: 21px;
}

.mb-21 {
  margin-bottom: 21px;
}

.ml-21 {
  margin-inline-start: 21px;
}

.mr-21 {
  margin-inline-end: 21px;
}

.mt-22 {
  margin-top: 22px;
}

.mb-22 {
  margin-bottom: 22px;
}

.ml-22 {
  margin-inline-start: 22px;
}

.mr-22 {
  margin-inline-end: 22px;
}

.mt-23 {
  margin-top: 23px;
}

.mb-23 {
  margin-bottom: 23px;
}

.ml-23 {
  margin-inline-start: 23px;
}

.mr-23 {
  margin-inline-end: 23px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-inline-start: 24px;
}

.mr-24 {
  margin-inline-end: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-inline-start: 25px;
}

.mr-25 {
  margin-inline-end: 25px;
}

.mt-26 {
  margin-top: 26px;
}

.mb-26 {
  margin-bottom: 26px;
}

.ml-26 {
  margin-inline-start: 26px;
}

.mr-26 {
  margin-inline-end: 26px;
}

.mt-27 {
  margin-top: 27px;
}

.mb-27 {
  margin-bottom: 27px;
}

.ml-27 {
  margin-inline-start: 27px;
}

.mr-27 {
  margin-inline-end: 27px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.ml-28 {
  margin-inline-start: 28px;
}

.mr-28 {
  margin-inline-end: 28px;
}

.mt-29 {
  margin-top: 29px;
}

.mb-29 {
  margin-bottom: 29px;
}

.ml-29 {
  margin-inline-start: 29px;
}

.mr-29 {
  margin-inline-end: 29px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-inline-start: 30px;
}

.mr-30 {
  margin-inline-end: 30px;
}

.mt-31 {
  margin-top: 31px;
}

.mb-31 {
  margin-bottom: 31px;
}

.ml-31 {
  margin-inline-start: 31px;
}

.mr-31 {
  margin-inline-end: 31px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.ml-32 {
  margin-inline-start: 32px;
}

.mr-32 {
  margin-inline-end: 32px;
}

.mt-33 {
  margin-top: 33px;
}

.mb-33 {
  margin-bottom: 33px;
}

.ml-33 {
  margin-inline-start: 33px;
}

.mr-33 {
  margin-inline-end: 33px;
}

.mt-34 {
  margin-top: 34px;
}

.mb-34 {
  margin-bottom: 34px;
}

.ml-34 {
  margin-inline-start: 34px;
}

.mr-34 {
  margin-inline-end: 34px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-inline-start: 35px;
}

.mr-35 {
  margin-inline-end: 35px;
}

.mt-36 {
  margin-top: 36px;
}

.mb-36 {
  margin-bottom: 36px;
}

.ml-36 {
  margin-inline-start: 36px;
}

.mr-36 {
  margin-inline-end: 36px;
}

.mt-37 {
  margin-top: 37px;
}

.mb-37 {
  margin-bottom: 37px;
}

.ml-37 {
  margin-inline-start: 37px;
}

.mr-37 {
  margin-inline-end: 37px;
}

.mt-38 {
  margin-top: 38px;
}

.mb-38 {
  margin-bottom: 38px;
}

.ml-38 {
  margin-inline-start: 38px;
}

.mr-38 {
  margin-inline-end: 38px;
}

.mt-39 {
  margin-top: 39px;
}

.mb-39 {
  margin-bottom: 39px;
}

.ml-39 {
  margin-inline-start: 39px;
}

.mr-39 {
  margin-inline-end: 39px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-inline-start: 40px;
}

.mr-40 {
  margin-inline-end: 40px;
}

.mt-41 {
  margin-top: 41px;
}

.mb-41 {
  margin-bottom: 41px;
}

.ml-41 {
  margin-inline-start: 41px;
}

.mr-41 {
  margin-inline-end: 41px;
}

.mt-42 {
  margin-top: 42px;
}

.mb-42 {
  margin-bottom: 42px;
}

.ml-42 {
  margin-inline-start: 42px;
}

.mr-42 {
  margin-inline-end: 42px;
}

.mt-43 {
  margin-top: 43px;
}

.mb-43 {
  margin-bottom: 43px;
}

.ml-43 {
  margin-inline-start: 43px;
}

.mr-43 {
  margin-inline-end: 43px;
}

.mt-44 {
  margin-top: 44px;
}

.mb-44 {
  margin-bottom: 44px;
}

.ml-44 {
  margin-inline-start: 44px;
}

.mr-44 {
  margin-inline-end: 44px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-inline-start: 45px;
}

.mr-45 {
  margin-inline-end: 45px;
}

.mt-46 {
  margin-top: 46px;
}

.mb-46 {
  margin-bottom: 46px;
}

.ml-46 {
  margin-inline-start: 46px;
}

.mr-46 {
  margin-inline-end: 46px;
}

.mt-47 {
  margin-top: 47px;
}

.mb-47 {
  margin-bottom: 47px;
}

.ml-47 {
  margin-inline-start: 47px;
}

.mr-47 {
  margin-inline-end: 47px;
}

.mt-48 {
  margin-top: 48px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-48 {
  margin-inline-start: 48px;
}

.mr-48 {
  margin-inline-end: 48px;
}

.mt-49 {
  margin-top: 49px;
}

.mb-49 {
  margin-bottom: 49px;
}

.ml-49 {
  margin-inline-start: 49px;
}

.mr-49 {
  margin-inline-end: 49px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-inline-start: 50px;
}

.mr-50 {
  margin-inline-end: 50px;
}

.mt-51 {
  margin-top: 51px;
}

.mb-51 {
  margin-bottom: 51px;
}

.ml-51 {
  margin-inline-start: 51px;
}

.mr-51 {
  margin-inline-end: 51px;
}

.mt-52 {
  margin-top: 52px;
}

.mb-52 {
  margin-bottom: 52px;
}

.ml-52 {
  margin-inline-start: 52px;
}

.mr-52 {
  margin-inline-end: 52px;
}

.mt-53 {
  margin-top: 53px;
}

.mb-53 {
  margin-bottom: 53px;
}

.ml-53 {
  margin-inline-start: 53px;
}

.mr-53 {
  margin-inline-end: 53px;
}

.mt-54 {
  margin-top: 54px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-54 {
  margin-inline-start: 54px;
}

.mr-54 {
  margin-inline-end: 54px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-inline-start: 55px;
}

.mr-55 {
  margin-inline-end: 55px;
}

.mt-56 {
  margin-top: 56px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-56 {
  margin-inline-start: 56px;
}

.mr-56 {
  margin-inline-end: 56px;
}

.mt-57 {
  margin-top: 57px;
}

.mb-57 {
  margin-bottom: 57px;
}

.ml-57 {
  margin-inline-start: 57px;
}

.mr-57 {
  margin-inline-end: 57px;
}

.mt-58 {
  margin-top: 58px;
}

.mb-58 {
  margin-bottom: 58px;
}

.ml-58 {
  margin-inline-start: 58px;
}

.mr-58 {
  margin-inline-end: 58px;
}

.mt-59 {
  margin-top: 59px;
}

.mb-59 {
  margin-bottom: 59px;
}

.ml-59 {
  margin-inline-start: 59px;
}

.mr-59 {
  margin-inline-end: 59px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-inline-start: 60px;
}

.mr-60 {
  margin-inline-end: 60px;
}

.mt-61 {
  margin-top: 61px;
}

.mb-61 {
  margin-bottom: 61px;
}

.ml-61 {
  margin-inline-start: 61px;
}

.mr-61 {
  margin-inline-end: 61px;
}

.mt-62 {
  margin-top: 62px;
}

.mb-62 {
  margin-bottom: 62px;
}

.ml-62 {
  margin-inline-start: 62px;
}

.mr-62 {
  margin-inline-end: 62px;
}

.mt-63 {
  margin-top: 63px;
}

.mb-63 {
  margin-bottom: 63px;
}

.ml-63 {
  margin-inline-start: 63px;
}

.mr-63 {
  margin-inline-end: 63px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.ml-64 {
  margin-inline-start: 64px;
}

.mr-64 {
  margin-inline-end: 64px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-inline-start: 65px;
}

.mr-65 {
  margin-inline-end: 65px;
}

.mt-66 {
  margin-top: 66px;
}

.mb-66 {
  margin-bottom: 66px;
}

.ml-66 {
  margin-inline-start: 66px;
}

.mr-66 {
  margin-inline-end: 66px;
}

.mt-67 {
  margin-top: 67px;
}

.mb-67 {
  margin-bottom: 67px;
}

.ml-67 {
  margin-inline-start: 67px;
}

.mr-67 {
  margin-inline-end: 67px;
}

.mt-68 {
  margin-top: 68px;
}

.mb-68 {
  margin-bottom: 68px;
}

.ml-68 {
  margin-inline-start: 68px;
}

.mr-68 {
  margin-inline-end: 68px;
}

.mt-69 {
  margin-top: 69px;
}

.mb-69 {
  margin-bottom: 69px;
}

.ml-69 {
  margin-inline-start: 69px;
}

.mr-69 {
  margin-inline-end: 69px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-inline-start: 70px;
}

.mr-70 {
  margin-inline-end: 70px;
}

.mt-71 {
  margin-top: 71px;
}

.mb-71 {
  margin-bottom: 71px;
}

.ml-71 {
  margin-inline-start: 71px;
}

.mr-71 {
  margin-inline-end: 71px;
}

.mt-72 {
  margin-top: 72px;
}

.mb-72 {
  margin-bottom: 72px;
}

.ml-72 {
  margin-inline-start: 72px;
}

.mr-72 {
  margin-inline-end: 72px;
}

.mt-73 {
  margin-top: 73px;
}

.mb-73 {
  margin-bottom: 73px;
}

.ml-73 {
  margin-inline-start: 73px;
}

.mr-73 {
  margin-inline-end: 73px;
}

.mt-74 {
  margin-top: 74px;
}

.mb-74 {
  margin-bottom: 74px;
}

.ml-74 {
  margin-inline-start: 74px;
}

.mr-74 {
  margin-inline-end: 74px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-inline-start: 75px;
}

.mr-75 {
  margin-inline-end: 75px;
}

.mt-76 {
  margin-top: 76px;
}

.mb-76 {
  margin-bottom: 76px;
}

.ml-76 {
  margin-inline-start: 76px;
}

.mr-76 {
  margin-inline-end: 76px;
}

.mt-77 {
  margin-top: 77px;
}

.mb-77 {
  margin-bottom: 77px;
}

.ml-77 {
  margin-inline-start: 77px;
}

.mr-77 {
  margin-inline-end: 77px;
}

.mt-78 {
  margin-top: 78px;
}

.mb-78 {
  margin-bottom: 78px;
}

.ml-78 {
  margin-inline-start: 78px;
}

.mr-78 {
  margin-inline-end: 78px;
}

.mt-79 {
  margin-top: 79px;
}

.mb-79 {
  margin-bottom: 79px;
}

.ml-79 {
  margin-inline-start: 79px;
}

.mr-79 {
  margin-inline-end: 79px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-inline-start: 80px;
}

.mr-80 {
  margin-inline-end: 80px;
}

.mt-81 {
  margin-top: 81px;
}

.mb-81 {
  margin-bottom: 81px;
}

.ml-81 {
  margin-inline-start: 81px;
}

.mr-81 {
  margin-inline-end: 81px;
}

.mt-82 {
  margin-top: 82px;
}

.mb-82 {
  margin-bottom: 82px;
}

.ml-82 {
  margin-inline-start: 82px;
}

.mr-82 {
  margin-inline-end: 82px;
}

.mt-83 {
  margin-top: 83px;
}

.mb-83 {
  margin-bottom: 83px;
}

.ml-83 {
  margin-inline-start: 83px;
}

.mr-83 {
  margin-inline-end: 83px;
}

.mt-84 {
  margin-top: 84px;
}

.mb-84 {
  margin-bottom: 84px;
}

.ml-84 {
  margin-inline-start: 84px;
}

.mr-84 {
  margin-inline-end: 84px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-inline-start: 85px;
}

.mr-85 {
  margin-inline-end: 85px;
}

.mt-86 {
  margin-top: 86px;
}

.mb-86 {
  margin-bottom: 86px;
}

.ml-86 {
  margin-inline-start: 86px;
}

.mr-86 {
  margin-inline-end: 86px;
}

.mt-87 {
  margin-top: 87px;
}

.mb-87 {
  margin-bottom: 87px;
}

.ml-87 {
  margin-inline-start: 87px;
}

.mr-87 {
  margin-inline-end: 87px;
}

.mt-88 {
  margin-top: 88px;
}

.mb-88 {
  margin-bottom: 88px;
}

.ml-88 {
  margin-inline-start: 88px;
}

.mr-88 {
  margin-inline-end: 88px;
}

.mt-89 {
  margin-top: 89px;
}

.mb-89 {
  margin-bottom: 89px;
}

.ml-89 {
  margin-inline-start: 89px;
}

.mr-89 {
  margin-inline-end: 89px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-inline-start: 90px;
}

.mr-90 {
  margin-inline-end: 90px;
}

.mt-91 {
  margin-top: 91px;
}

.mb-91 {
  margin-bottom: 91px;
}

.ml-91 {
  margin-inline-start: 91px;
}

.mr-91 {
  margin-inline-end: 91px;
}

.mt-92 {
  margin-top: 92px;
}

.mb-92 {
  margin-bottom: 92px;
}

.ml-92 {
  margin-inline-start: 92px;
}

.mr-92 {
  margin-inline-end: 92px;
}

.mt-93 {
  margin-top: 93px;
}

.mb-93 {
  margin-bottom: 93px;
}

.ml-93 {
  margin-inline-start: 93px;
}

.mr-93 {
  margin-inline-end: 93px;
}

.mt-94 {
  margin-top: 94px;
}

.mb-94 {
  margin-bottom: 94px;
}

.ml-94 {
  margin-inline-start: 94px;
}

.mr-94 {
  margin-inline-end: 94px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-inline-start: 95px;
}

.mr-95 {
  margin-inline-end: 95px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-96 {
  margin-bottom: 96px;
}

.ml-96 {
  margin-inline-start: 96px;
}

.mr-96 {
  margin-inline-end: 96px;
}

.mt-97 {
  margin-top: 97px;
}

.mb-97 {
  margin-bottom: 97px;
}

.ml-97 {
  margin-inline-start: 97px;
}

.mr-97 {
  margin-inline-end: 97px;
}

.mt-98 {
  margin-top: 98px;
}

.mb-98 {
  margin-bottom: 98px;
}

.ml-98 {
  margin-inline-start: 98px;
}

.mr-98 {
  margin-inline-end: 98px;
}

.mt-99 {
  margin-top: 99px;
}

.mb-99 {
  margin-bottom: 99px;
}

.ml-99 {
  margin-inline-start: 99px;
}

.mr-99 {
  margin-inline-end: 99px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-inline-start: 100px;
}

.mr-100 {
  margin-inline-end: 100px;
}

.mt-101 {
  margin-top: 101px;
}

.mb-101 {
  margin-bottom: 101px;
}

.ml-101 {
  margin-inline-start: 101px;
}

.mr-101 {
  margin-inline-end: 101px;
}

.mt-102 {
  margin-top: 102px;
}

.mb-102 {
  margin-bottom: 102px;
}

.ml-102 {
  margin-inline-start: 102px;
}

.mr-102 {
  margin-inline-end: 102px;
}

.mt-103 {
  margin-top: 103px;
}

.mb-103 {
  margin-bottom: 103px;
}

.ml-103 {
  margin-inline-start: 103px;
}

.mr-103 {
  margin-inline-end: 103px;
}

.mt-104 {
  margin-top: 104px;
}

.mb-104 {
  margin-bottom: 104px;
}

.ml-104 {
  margin-inline-start: 104px;
}

.mr-104 {
  margin-inline-end: 104px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-inline-start: 105px;
}

.mr-105 {
  margin-inline-end: 105px;
}

.mt-106 {
  margin-top: 106px;
}

.mb-106 {
  margin-bottom: 106px;
}

.ml-106 {
  margin-inline-start: 106px;
}

.mr-106 {
  margin-inline-end: 106px;
}

.mt-107 {
  margin-top: 107px;
}

.mb-107 {
  margin-bottom: 107px;
}

.ml-107 {
  margin-inline-start: 107px;
}

.mr-107 {
  margin-inline-end: 107px;
}

.mt-108 {
  margin-top: 108px;
}

.mb-108 {
  margin-bottom: 108px;
}

.ml-108 {
  margin-inline-start: 108px;
}

.mr-108 {
  margin-inline-end: 108px;
}

.mt-109 {
  margin-top: 109px;
}

.mb-109 {
  margin-bottom: 109px;
}

.ml-109 {
  margin-inline-start: 109px;
}

.mr-109 {
  margin-inline-end: 109px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-inline-start: 110px;
}

.mr-110 {
  margin-inline-end: 110px;
}

.mt-111 {
  margin-top: 111px;
}

.mb-111 {
  margin-bottom: 111px;
}

.ml-111 {
  margin-inline-start: 111px;
}

.mr-111 {
  margin-inline-end: 111px;
}

.mt-112 {
  margin-top: 112px;
}

.mb-112 {
  margin-bottom: 112px;
}

.ml-112 {
  margin-inline-start: 112px;
}

.mr-112 {
  margin-inline-end: 112px;
}

.mt-113 {
  margin-top: 113px;
}

.mb-113 {
  margin-bottom: 113px;
}

.ml-113 {
  margin-inline-start: 113px;
}

.mr-113 {
  margin-inline-end: 113px;
}

.mt-114 {
  margin-top: 114px;
}

.mb-114 {
  margin-bottom: 114px;
}

.ml-114 {
  margin-inline-start: 114px;
}

.mr-114 {
  margin-inline-end: 114px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-inline-start: 115px;
}

.mr-115 {
  margin-inline-end: 115px;
}

.mt-116 {
  margin-top: 116px;
}

.mb-116 {
  margin-bottom: 116px;
}

.ml-116 {
  margin-inline-start: 116px;
}

.mr-116 {
  margin-inline-end: 116px;
}

.mt-117 {
  margin-top: 117px;
}

.mb-117 {
  margin-bottom: 117px;
}

.ml-117 {
  margin-inline-start: 117px;
}

.mr-117 {
  margin-inline-end: 117px;
}

.mt-118 {
  margin-top: 118px;
}

.mb-118 {
  margin-bottom: 118px;
}

.ml-118 {
  margin-inline-start: 118px;
}

.mr-118 {
  margin-inline-end: 118px;
}

.mt-119 {
  margin-top: 119px;
}

.mb-119 {
  margin-bottom: 119px;
}

.ml-119 {
  margin-inline-start: 119px;
}

.mr-119 {
  margin-inline-end: 119px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-inline-start: 120px;
}

.mr-120 {
  margin-inline-end: 120px;
}

.mt-121 {
  margin-top: 121px;
}

.mb-121 {
  margin-bottom: 121px;
}

.ml-121 {
  margin-inline-start: 121px;
}

.mr-121 {
  margin-inline-end: 121px;
}

.mt-122 {
  margin-top: 122px;
}

.mb-122 {
  margin-bottom: 122px;
}

.ml-122 {
  margin-inline-start: 122px;
}

.mr-122 {
  margin-inline-end: 122px;
}

.mt-123 {
  margin-top: 123px;
}

.mb-123 {
  margin-bottom: 123px;
}

.ml-123 {
  margin-inline-start: 123px;
}

.mr-123 {
  margin-inline-end: 123px;
}

.mt-124 {
  margin-top: 124px;
}

.mb-124 {
  margin-bottom: 124px;
}

.ml-124 {
  margin-inline-start: 124px;
}

.mr-124 {
  margin-inline-end: 124px;
}

.mt-125 {
  margin-top: 125px;
}

.mb-125 {
  margin-bottom: 125px;
}

.ml-125 {
  margin-inline-start: 125px;
}

.mr-125 {
  margin-inline-end: 125px;
}

.mt-126 {
  margin-top: 126px;
}

.mb-126 {
  margin-bottom: 126px;
}

.ml-126 {
  margin-inline-start: 126px;
}

.mr-126 {
  margin-inline-end: 126px;
}

.mt-127 {
  margin-top: 127px;
}

.mb-127 {
  margin-bottom: 127px;
}

.ml-127 {
  margin-inline-start: 127px;
}

.mr-127 {
  margin-inline-end: 127px;
}

.mt-128 {
  margin-top: 128px;
}

.mb-128 {
  margin-bottom: 128px;
}

.ml-128 {
  margin-inline-start: 128px;
}

.mr-128 {
  margin-inline-end: 128px;
}

.mt-129 {
  margin-top: 129px;
}

.mb-129 {
  margin-bottom: 129px;
}

.ml-129 {
  margin-inline-start: 129px;
}

.mr-129 {
  margin-inline-end: 129px;
}

.mt-130 {
  margin-top: 130px;
}

.mb-130 {
  margin-bottom: 130px;
}

.ml-130 {
  margin-inline-start: 130px;
}

.mr-130 {
  margin-inline-end: 130px;
}

.mt-131 {
  margin-top: 131px;
}

.mb-131 {
  margin-bottom: 131px;
}

.ml-131 {
  margin-inline-start: 131px;
}

.mr-131 {
  margin-inline-end: 131px;
}

.mt-132 {
  margin-top: 132px;
}

.mb-132 {
  margin-bottom: 132px;
}

.ml-132 {
  margin-inline-start: 132px;
}

.mr-132 {
  margin-inline-end: 132px;
}

.mt-133 {
  margin-top: 133px;
}

.mb-133 {
  margin-bottom: 133px;
}

.ml-133 {
  margin-inline-start: 133px;
}

.mr-133 {
  margin-inline-end: 133px;
}

.mt-134 {
  margin-top: 134px;
}

.mb-134 {
  margin-bottom: 134px;
}

.ml-134 {
  margin-inline-start: 134px;
}

.mr-134 {
  margin-inline-end: 134px;
}

.mt-135 {
  margin-top: 135px;
}

.mb-135 {
  margin-bottom: 135px;
}

.ml-135 {
  margin-inline-start: 135px;
}

.mr-135 {
  margin-inline-end: 135px;
}

.mt-136 {
  margin-top: 136px;
}

.mb-136 {
  margin-bottom: 136px;
}

.ml-136 {
  margin-inline-start: 136px;
}

.mr-136 {
  margin-inline-end: 136px;
}

.mt-137 {
  margin-top: 137px;
}

.mb-137 {
  margin-bottom: 137px;
}

.ml-137 {
  margin-inline-start: 137px;
}

.mr-137 {
  margin-inline-end: 137px;
}

.mt-138 {
  margin-top: 138px;
}

.mb-138 {
  margin-bottom: 138px;
}

.ml-138 {
  margin-inline-start: 138px;
}

.mr-138 {
  margin-inline-end: 138px;
}

.mt-139 {
  margin-top: 139px;
}

.mb-139 {
  margin-bottom: 139px;
}

.ml-139 {
  margin-inline-start: 139px;
}

.mr-139 {
  margin-inline-end: 139px;
}

.mt-140 {
  margin-top: 140px;
}

.mb-140 {
  margin-bottom: 140px;
}

.ml-140 {
  margin-inline-start: 140px;
}

.mr-140 {
  margin-inline-end: 140px;
}

.mt-141 {
  margin-top: 141px;
}

.mb-141 {
  margin-bottom: 141px;
}

.ml-141 {
  margin-inline-start: 141px;
}

.mr-141 {
  margin-inline-end: 141px;
}

.mt-142 {
  margin-top: 142px;
}

.mb-142 {
  margin-bottom: 142px;
}

.ml-142 {
  margin-inline-start: 142px;
}

.mr-142 {
  margin-inline-end: 142px;
}

.mt-143 {
  margin-top: 143px;
}

.mb-143 {
  margin-bottom: 143px;
}

.ml-143 {
  margin-inline-start: 143px;
}

.mr-143 {
  margin-inline-end: 143px;
}

.mt-144 {
  margin-top: 144px;
}

.mb-144 {
  margin-bottom: 144px;
}

.ml-144 {
  margin-inline-start: 144px;
}

.mr-144 {
  margin-inline-end: 144px;
}

.mt-145 {
  margin-top: 145px;
}

.mb-145 {
  margin-bottom: 145px;
}

.ml-145 {
  margin-inline-start: 145px;
}

.mr-145 {
  margin-inline-end: 145px;
}

.mt-146 {
  margin-top: 146px;
}

.mb-146 {
  margin-bottom: 146px;
}

.ml-146 {
  margin-inline-start: 146px;
}

.mr-146 {
  margin-inline-end: 146px;
}

.mt-147 {
  margin-top: 147px;
}

.mb-147 {
  margin-bottom: 147px;
}

.ml-147 {
  margin-inline-start: 147px;
}

.mr-147 {
  margin-inline-end: 147px;
}

.mt-148 {
  margin-top: 148px;
}

.mb-148 {
  margin-bottom: 148px;
}

.ml-148 {
  margin-inline-start: 148px;
}

.mr-148 {
  margin-inline-end: 148px;
}

.mt-149 {
  margin-top: 149px;
}

.mb-149 {
  margin-bottom: 149px;
}

.ml-149 {
  margin-inline-start: 149px;
}

.mr-149 {
  margin-inline-end: 149px;
}

.mt-150 {
  margin-top: 150px;
}

.mb-150 {
  margin-bottom: 150px;
}

.ml-150 {
  margin-inline-start: 150px;
}

.mr-150 {
  margin-inline-end: 150px;
}

.mt-151 {
  margin-top: 151px;
}

.mb-151 {
  margin-bottom: 151px;
}

.ml-151 {
  margin-inline-start: 151px;
}

.mr-151 {
  margin-inline-end: 151px;
}

.mt-152 {
  margin-top: 152px;
}

.mb-152 {
  margin-bottom: 152px;
}

.ml-152 {
  margin-inline-start: 152px;
}

.mr-152 {
  margin-inline-end: 152px;
}

.mt-153 {
  margin-top: 153px;
}

.mb-153 {
  margin-bottom: 153px;
}

.ml-153 {
  margin-inline-start: 153px;
}

.mr-153 {
  margin-inline-end: 153px;
}

.mt-154 {
  margin-top: 154px;
}

.mb-154 {
  margin-bottom: 154px;
}

.ml-154 {
  margin-inline-start: 154px;
}

.mr-154 {
  margin-inline-end: 154px;
}

.mt-155 {
  margin-top: 155px;
}

.mb-155 {
  margin-bottom: 155px;
}

.ml-155 {
  margin-inline-start: 155px;
}

.mr-155 {
  margin-inline-end: 155px;
}

.mt-156 {
  margin-top: 156px;
}

.mb-156 {
  margin-bottom: 156px;
}

.ml-156 {
  margin-inline-start: 156px;
}

.mr-156 {
  margin-inline-end: 156px;
}

.mt-157 {
  margin-top: 157px;
}

.mb-157 {
  margin-bottom: 157px;
}

.ml-157 {
  margin-inline-start: 157px;
}

.mr-157 {
  margin-inline-end: 157px;
}

.mt-158 {
  margin-top: 158px;
}

.mb-158 {
  margin-bottom: 158px;
}

.ml-158 {
  margin-inline-start: 158px;
}

.mr-158 {
  margin-inline-end: 158px;
}

.mt-159 {
  margin-top: 159px;
}

.mb-159 {
  margin-bottom: 159px;
}

.ml-159 {
  margin-inline-start: 159px;
}

.mr-159 {
  margin-inline-end: 159px;
}

.mt-160 {
  margin-top: 160px;
}

.mb-160 {
  margin-bottom: 160px;
}

.ml-160 {
  margin-inline-start: 160px;
}

.mr-160 {
  margin-inline-end: 160px;
}

.mt-161 {
  margin-top: 161px;
}

.mb-161 {
  margin-bottom: 161px;
}

.ml-161 {
  margin-inline-start: 161px;
}

.mr-161 {
  margin-inline-end: 161px;
}

.mt-162 {
  margin-top: 162px;
}

.mb-162 {
  margin-bottom: 162px;
}

.ml-162 {
  margin-inline-start: 162px;
}

.mr-162 {
  margin-inline-end: 162px;
}

.mt-163 {
  margin-top: 163px;
}

.mb-163 {
  margin-bottom: 163px;
}

.ml-163 {
  margin-inline-start: 163px;
}

.mr-163 {
  margin-inline-end: 163px;
}

.mt-164 {
  margin-top: 164px;
}

.mb-164 {
  margin-bottom: 164px;
}

.ml-164 {
  margin-inline-start: 164px;
}

.mr-164 {
  margin-inline-end: 164px;
}

.mt-165 {
  margin-top: 165px;
}

.mb-165 {
  margin-bottom: 165px;
}

.ml-165 {
  margin-inline-start: 165px;
}

.mr-165 {
  margin-inline-end: 165px;
}

.mt-166 {
  margin-top: 166px;
}

.mb-166 {
  margin-bottom: 166px;
}

.ml-166 {
  margin-inline-start: 166px;
}

.mr-166 {
  margin-inline-end: 166px;
}

.mt-167 {
  margin-top: 167px;
}

.mb-167 {
  margin-bottom: 167px;
}

.ml-167 {
  margin-inline-start: 167px;
}

.mr-167 {
  margin-inline-end: 167px;
}

.mt-168 {
  margin-top: 168px;
}

.mb-168 {
  margin-bottom: 168px;
}

.ml-168 {
  margin-inline-start: 168px;
}

.mr-168 {
  margin-inline-end: 168px;
}

.mt-169 {
  margin-top: 169px;
}

.mb-169 {
  margin-bottom: 169px;
}

.ml-169 {
  margin-inline-start: 169px;
}

.mr-169 {
  margin-inline-end: 169px;
}

.mt-170 {
  margin-top: 170px;
}

.mb-170 {
  margin-bottom: 170px;
}

.ml-170 {
  margin-inline-start: 170px;
}

.mr-170 {
  margin-inline-end: 170px;
}

.mt-171 {
  margin-top: 171px;
}

.mb-171 {
  margin-bottom: 171px;
}

.ml-171 {
  margin-inline-start: 171px;
}

.mr-171 {
  margin-inline-end: 171px;
}

.mt-172 {
  margin-top: 172px;
}

.mb-172 {
  margin-bottom: 172px;
}

.ml-172 {
  margin-inline-start: 172px;
}

.mr-172 {
  margin-inline-end: 172px;
}

.mt-173 {
  margin-top: 173px;
}

.mb-173 {
  margin-bottom: 173px;
}

.ml-173 {
  margin-inline-start: 173px;
}

.mr-173 {
  margin-inline-end: 173px;
}

.mt-174 {
  margin-top: 174px;
}

.mb-174 {
  margin-bottom: 174px;
}

.ml-174 {
  margin-inline-start: 174px;
}

.mr-174 {
  margin-inline-end: 174px;
}

.mt-175 {
  margin-top: 175px;
}

.mb-175 {
  margin-bottom: 175px;
}

.ml-175 {
  margin-inline-start: 175px;
}

.mr-175 {
  margin-inline-end: 175px;
}

.mt-176 {
  margin-top: 176px;
}

.mb-176 {
  margin-bottom: 176px;
}

.ml-176 {
  margin-inline-start: 176px;
}

.mr-176 {
  margin-inline-end: 176px;
}

.mt-177 {
  margin-top: 177px;
}

.mb-177 {
  margin-bottom: 177px;
}

.ml-177 {
  margin-inline-start: 177px;
}

.mr-177 {
  margin-inline-end: 177px;
}

.mt-178 {
  margin-top: 178px;
}

.mb-178 {
  margin-bottom: 178px;
}

.ml-178 {
  margin-inline-start: 178px;
}

.mr-178 {
  margin-inline-end: 178px;
}

.mt-179 {
  margin-top: 179px;
}

.mb-179 {
  margin-bottom: 179px;
}

.ml-179 {
  margin-inline-start: 179px;
}

.mr-179 {
  margin-inline-end: 179px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.ml-180 {
  margin-inline-start: 180px;
}

.mr-180 {
  margin-inline-end: 180px;
}

.mt-181 {
  margin-top: 181px;
}

.mb-181 {
  margin-bottom: 181px;
}

.ml-181 {
  margin-inline-start: 181px;
}

.mr-181 {
  margin-inline-end: 181px;
}

.mt-182 {
  margin-top: 182px;
}

.mb-182 {
  margin-bottom: 182px;
}

.ml-182 {
  margin-inline-start: 182px;
}

.mr-182 {
  margin-inline-end: 182px;
}

.mt-183 {
  margin-top: 183px;
}

.mb-183 {
  margin-bottom: 183px;
}

.ml-183 {
  margin-inline-start: 183px;
}

.mr-183 {
  margin-inline-end: 183px;
}

.mt-184 {
  margin-top: 184px;
}

.mb-184 {
  margin-bottom: 184px;
}

.ml-184 {
  margin-inline-start: 184px;
}

.mr-184 {
  margin-inline-end: 184px;
}

.mt-185 {
  margin-top: 185px;
}

.mb-185 {
  margin-bottom: 185px;
}

.ml-185 {
  margin-inline-start: 185px;
}

.mr-185 {
  margin-inline-end: 185px;
}

.mt-186 {
  margin-top: 186px;
}

.mb-186 {
  margin-bottom: 186px;
}

.ml-186 {
  margin-inline-start: 186px;
}

.mr-186 {
  margin-inline-end: 186px;
}

.mt-187 {
  margin-top: 187px;
}

.mb-187 {
  margin-bottom: 187px;
}

.ml-187 {
  margin-inline-start: 187px;
}

.mr-187 {
  margin-inline-end: 187px;
}

.mt-188 {
  margin-top: 188px;
}

.mb-188 {
  margin-bottom: 188px;
}

.ml-188 {
  margin-inline-start: 188px;
}

.mr-188 {
  margin-inline-end: 188px;
}

.mt-189 {
  margin-top: 189px;
}

.mb-189 {
  margin-bottom: 189px;
}

.ml-189 {
  margin-inline-start: 189px;
}

.mr-189 {
  margin-inline-end: 189px;
}

.mt-190 {
  margin-top: 190px;
}

.mb-190 {
  margin-bottom: 190px;
}

.ml-190 {
  margin-inline-start: 190px;
}

.mr-190 {
  margin-inline-end: 190px;
}

.mt-191 {
  margin-top: 191px;
}

.mb-191 {
  margin-bottom: 191px;
}

.ml-191 {
  margin-inline-start: 191px;
}

.mr-191 {
  margin-inline-end: 191px;
}

.mt-192 {
  margin-top: 192px;
}

.mb-192 {
  margin-bottom: 192px;
}

.ml-192 {
  margin-inline-start: 192px;
}

.mr-192 {
  margin-inline-end: 192px;
}

.mt-193 {
  margin-top: 193px;
}

.mb-193 {
  margin-bottom: 193px;
}

.ml-193 {
  margin-inline-start: 193px;
}

.mr-193 {
  margin-inline-end: 193px;
}

.mt-194 {
  margin-top: 194px;
}

.mb-194 {
  margin-bottom: 194px;
}

.ml-194 {
  margin-inline-start: 194px;
}

.mr-194 {
  margin-inline-end: 194px;
}

.mt-195 {
  margin-top: 195px;
}

.mb-195 {
  margin-bottom: 195px;
}

.ml-195 {
  margin-inline-start: 195px;
}

.mr-195 {
  margin-inline-end: 195px;
}

.mt-196 {
  margin-top: 196px;
}

.mb-196 {
  margin-bottom: 196px;
}

.ml-196 {
  margin-inline-start: 196px;
}

.mr-196 {
  margin-inline-end: 196px;
}

.mt-197 {
  margin-top: 197px;
}

.mb-197 {
  margin-bottom: 197px;
}

.ml-197 {
  margin-inline-start: 197px;
}

.mr-197 {
  margin-inline-end: 197px;
}

.mt-198 {
  margin-top: 198px;
}

.mb-198 {
  margin-bottom: 198px;
}

.ml-198 {
  margin-inline-start: 198px;
}

.mr-198 {
  margin-inline-end: 198px;
}

.mt-199 {
  margin-top: 199px;
}

.mb-199 {
  margin-bottom: 199px;
}

.ml-199 {
  margin-inline-start: 199px;
}

.mr-199 {
  margin-inline-end: 199px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-200 {
  margin-inline-start: 200px;
}

.mr-200 {
  margin-inline-end: 200px;
}

.pt-1 {
  padding-top: 1px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pl-1 {
  padding-inline-start: 1px;
}

.pr-1 {
  padding-inline-end: 1px;
}

.pt-2 {
  padding-top: 2px;
}

.pb-2 {
  padding-bottom: 2px;
}

.pl-2 {
  padding-inline-start: 2px;
}

.pr-2 {
  padding-inline-end: 2px;
}

.pt-3 {
  padding-top: 3px;
}

.pb-3 {
  padding-bottom: 3px;
}

.pl-3 {
  padding-inline-start: 3px;
}

.pr-3 {
  padding-inline-end: 3px;
}

.pt-4 {
  padding-top: 4px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pl-4 {
  padding-inline-start: 4px;
}

.pr-4 {
  padding-inline-end: 4px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-inline-start: 5px;
}

.pr-5 {
  padding-inline-end: 5px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.pl-6 {
  padding-inline-start: 6px;
}

.pr-6 {
  padding-inline-end: 6px;
}

.pt-7 {
  padding-top: 7px;
}

.pb-7 {
  padding-bottom: 7px;
}

.pl-7 {
  padding-inline-start: 7px;
}

.pr-7 {
  padding-inline-end: 7px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pl-8 {
  padding-inline-start: 8px;
}

.pr-8 {
  padding-inline-end: 8px;
}

.pt-9 {
  padding-top: 9px;
}

.pb-9 {
  padding-bottom: 9px;
}

.pl-9 {
  padding-inline-start: 9px;
}

.pr-9 {
  padding-inline-end: 9px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-inline-start: 10px;
}

.pr-10 {
  padding-inline-end: 10px;
}

.pt-11 {
  padding-top: 11px;
}

.pb-11 {
  padding-bottom: 11px;
}

.pl-11 {
  padding-inline-start: 11px;
}

.pr-11 {
  padding-inline-end: 11px;
}

.pt-12 {
  padding-top: 12px;
}

.pb-12 {
  padding-bottom: 12px;
}

.pl-12 {
  padding-inline-start: 12px;
}

.pr-12 {
  padding-inline-end: 12px;
}

.pt-13 {
  padding-top: 13px;
}

.pb-13 {
  padding-bottom: 13px;
}

.pl-13 {
  padding-inline-start: 13px;
}

.pr-13 {
  padding-inline-end: 13px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-14 {
  padding-bottom: 14px;
}

.pl-14 {
  padding-inline-start: 14px;
}

.pr-14 {
  padding-inline-end: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-inline-start: 15px;
}

.pr-15 {
  padding-inline-end: 15px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pl-16 {
  padding-inline-start: 16px;
}

.pr-16 {
  padding-inline-end: 16px;
}

.pt-17 {
  padding-top: 17px;
}

.pb-17 {
  padding-bottom: 17px;
}

.pl-17 {
  padding-inline-start: 17px;
}

.pr-17 {
  padding-inline-end: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pb-18 {
  padding-bottom: 18px;
}

.pl-18 {
  padding-inline-start: 18px;
}

.pr-18 {
  padding-inline-end: 18px;
}

.pt-19 {
  padding-top: 19px;
}

.pb-19 {
  padding-bottom: 19px;
}

.pl-19 {
  padding-inline-start: 19px;
}

.pr-19 {
  padding-inline-end: 19px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-inline-start: 20px;
}

.pr-20 {
  padding-inline-end: 20px;
}

.pt-21 {
  padding-top: 21px;
}

.pb-21 {
  padding-bottom: 21px;
}

.pl-21 {
  padding-inline-start: 21px;
}

.pr-21 {
  padding-inline-end: 21px;
}

.pt-22 {
  padding-top: 22px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pl-22 {
  padding-inline-start: 22px;
}

.pr-22 {
  padding-inline-end: 22px;
}

.pt-23 {
  padding-top: 23px;
}

.pb-23 {
  padding-bottom: 23px;
}

.pl-23 {
  padding-inline-start: 23px;
}

.pr-23 {
  padding-inline-end: 23px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pl-24 {
  padding-inline-start: 24px;
}

.pr-24 {
  padding-inline-end: 24px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-inline-start: 25px;
}

.pr-25 {
  padding-inline-end: 25px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pl-26 {
  padding-inline-start: 26px;
}

.pr-26 {
  padding-inline-end: 26px;
}

.pt-27 {
  padding-top: 27px;
}

.pb-27 {
  padding-bottom: 27px;
}

.pl-27 {
  padding-inline-start: 27px;
}

.pr-27 {
  padding-inline-end: 27px;
}

.pt-28 {
  padding-top: 28px;
}

.pb-28 {
  padding-bottom: 28px;
}

.pl-28 {
  padding-inline-start: 28px;
}

.pr-28 {
  padding-inline-end: 28px;
}

.pt-29 {
  padding-top: 29px;
}

.pb-29 {
  padding-bottom: 29px;
}

.pl-29 {
  padding-inline-start: 29px;
}

.pr-29 {
  padding-inline-end: 29px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-inline-start: 30px;
}

.pr-30 {
  padding-inline-end: 30px;
}

.pt-31 {
  padding-top: 31px;
}

.pb-31 {
  padding-bottom: 31px;
}

.pl-31 {
  padding-inline-start: 31px;
}

.pr-31 {
  padding-inline-end: 31px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.pl-32 {
  padding-inline-start: 32px;
}

.pr-32 {
  padding-inline-end: 32px;
}

.pt-33 {
  padding-top: 33px;
}

.pb-33 {
  padding-bottom: 33px;
}

.pl-33 {
  padding-inline-start: 33px;
}

.pr-33 {
  padding-inline-end: 33px;
}

.pt-34 {
  padding-top: 34px;
}

.pb-34 {
  padding-bottom: 34px;
}

.pl-34 {
  padding-inline-start: 34px;
}

.pr-34 {
  padding-inline-end: 34px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-inline-start: 35px;
}

.pr-35 {
  padding-inline-end: 35px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pl-36 {
  padding-inline-start: 36px;
}

.pr-36 {
  padding-inline-end: 36px;
}

.pt-37 {
  padding-top: 37px;
}

.pb-37 {
  padding-bottom: 37px;
}

.pl-37 {
  padding-inline-start: 37px;
}

.pr-37 {
  padding-inline-end: 37px;
}

.pt-38 {
  padding-top: 38px;
}

.pb-38 {
  padding-bottom: 38px;
}

.pl-38 {
  padding-inline-start: 38px;
}

.pr-38 {
  padding-inline-end: 38px;
}

.pt-39 {
  padding-top: 39px;
}

.pb-39 {
  padding-bottom: 39px;
}

.pl-39 {
  padding-inline-start: 39px;
}

.pr-39 {
  padding-inline-end: 39px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-inline-start: 40px;
}

.pr-40 {
  padding-inline-end: 40px;
}

.pt-41 {
  padding-top: 41px;
}

.pb-41 {
  padding-bottom: 41px;
}

.pl-41 {
  padding-inline-start: 41px;
}

.pr-41 {
  padding-inline-end: 41px;
}

.pt-42 {
  padding-top: 42px;
}

.pb-42 {
  padding-bottom: 42px;
}

.pl-42 {
  padding-inline-start: 42px;
}

.pr-42 {
  padding-inline-end: 42px;
}

.pt-43 {
  padding-top: 43px;
}

.pb-43 {
  padding-bottom: 43px;
}

.pl-43 {
  padding-inline-start: 43px;
}

.pr-43 {
  padding-inline-end: 43px;
}

.pt-44 {
  padding-top: 44px;
}

.pb-44 {
  padding-bottom: 44px;
}

.pl-44 {
  padding-inline-start: 44px;
}

.pr-44 {
  padding-inline-end: 44px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-inline-start: 45px;
}

.pr-45 {
  padding-inline-end: 45px;
}

.pt-46 {
  padding-top: 46px;
}

.pb-46 {
  padding-bottom: 46px;
}

.pl-46 {
  padding-inline-start: 46px;
}

.pr-46 {
  padding-inline-end: 46px;
}

.pt-47 {
  padding-top: 47px;
}

.pb-47 {
  padding-bottom: 47px;
}

.pl-47 {
  padding-inline-start: 47px;
}

.pr-47 {
  padding-inline-end: 47px;
}

.pt-48 {
  padding-top: 48px;
}

.pb-48 {
  padding-bottom: 48px;
}

.pl-48 {
  padding-inline-start: 48px;
}

.pr-48 {
  padding-inline-end: 48px;
}

.pt-49 {
  padding-top: 49px;
}

.pb-49 {
  padding-bottom: 49px;
}

.pl-49 {
  padding-inline-start: 49px;
}

.pr-49 {
  padding-inline-end: 49px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-inline-start: 50px;
}

.pr-50 {
  padding-inline-end: 50px;
}

.pt-51 {
  padding-top: 51px;
}

.pb-51 {
  padding-bottom: 51px;
}

.pl-51 {
  padding-inline-start: 51px;
}

.pr-51 {
  padding-inline-end: 51px;
}

.pt-52 {
  padding-top: 52px;
}

.pb-52 {
  padding-bottom: 52px;
}

.pl-52 {
  padding-inline-start: 52px;
}

.pr-52 {
  padding-inline-end: 52px;
}

.pt-53 {
  padding-top: 53px;
}

.pb-53 {
  padding-bottom: 53px;
}

.pl-53 {
  padding-inline-start: 53px;
}

.pr-53 {
  padding-inline-end: 53px;
}

.pt-54 {
  padding-top: 54px;
}

.pb-54 {
  padding-bottom: 54px;
}

.pl-54 {
  padding-inline-start: 54px;
}

.pr-54 {
  padding-inline-end: 54px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-inline-start: 55px;
}

.pr-55 {
  padding-inline-end: 55px;
}

.pt-56 {
  padding-top: 56px;
}

.pb-56 {
  padding-bottom: 56px;
}

.pl-56 {
  padding-inline-start: 56px;
}

.pr-56 {
  padding-inline-end: 56px;
}

.pt-57 {
  padding-top: 57px;
}

.pb-57 {
  padding-bottom: 57px;
}

.pl-57 {
  padding-inline-start: 57px;
}

.pr-57 {
  padding-inline-end: 57px;
}

.pt-58 {
  padding-top: 58px;
}

.pb-58 {
  padding-bottom: 58px;
}

.pl-58 {
  padding-inline-start: 58px;
}

.pr-58 {
  padding-inline-end: 58px;
}

.pt-59 {
  padding-top: 59px;
}

.pb-59 {
  padding-bottom: 59px;
}

.pl-59 {
  padding-inline-start: 59px;
}

.pr-59 {
  padding-inline-end: 59px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-inline-start: 60px;
}

.pr-60 {
  padding-inline-end: 60px;
}

.pt-61 {
  padding-top: 61px;
}

.pb-61 {
  padding-bottom: 61px;
}

.pl-61 {
  padding-inline-start: 61px;
}

.pr-61 {
  padding-inline-end: 61px;
}

.pt-62 {
  padding-top: 62px;
}

.pb-62 {
  padding-bottom: 62px;
}

.pl-62 {
  padding-inline-start: 62px;
}

.pr-62 {
  padding-inline-end: 62px;
}

.pt-63 {
  padding-top: 63px;
}

.pb-63 {
  padding-bottom: 63px;
}

.pl-63 {
  padding-inline-start: 63px;
}

.pr-63 {
  padding-inline-end: 63px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.pl-64 {
  padding-inline-start: 64px;
}

.pr-64 {
  padding-inline-end: 64px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-inline-start: 65px;
}

.pr-65 {
  padding-inline-end: 65px;
}

.pt-66 {
  padding-top: 66px;
}

.pb-66 {
  padding-bottom: 66px;
}

.pl-66 {
  padding-inline-start: 66px;
}

.pr-66 {
  padding-inline-end: 66px;
}

.pt-67 {
  padding-top: 67px;
}

.pb-67 {
  padding-bottom: 67px;
}

.pl-67 {
  padding-inline-start: 67px;
}

.pr-67 {
  padding-inline-end: 67px;
}

.pt-68 {
  padding-top: 68px;
}

.pb-68 {
  padding-bottom: 68px;
}

.pl-68 {
  padding-inline-start: 68px;
}

.pr-68 {
  padding-inline-end: 68px;
}

.pt-69 {
  padding-top: 69px;
}

.pb-69 {
  padding-bottom: 69px;
}

.pl-69 {
  padding-inline-start: 69px;
}

.pr-69 {
  padding-inline-end: 69px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-inline-start: 70px;
}

.pr-70 {
  padding-inline-end: 70px;
}

.pt-71 {
  padding-top: 71px;
}

.pb-71 {
  padding-bottom: 71px;
}

.pl-71 {
  padding-inline-start: 71px;
}

.pr-71 {
  padding-inline-end: 71px;
}

.pt-72 {
  padding-top: 72px;
}

.pb-72 {
  padding-bottom: 72px;
}

.pl-72 {
  padding-inline-start: 72px;
}

.pr-72 {
  padding-inline-end: 72px;
}

.pt-73 {
  padding-top: 73px;
}

.pb-73 {
  padding-bottom: 73px;
}

.pl-73 {
  padding-inline-start: 73px;
}

.pr-73 {
  padding-inline-end: 73px;
}

.pt-74 {
  padding-top: 74px;
}

.pb-74 {
  padding-bottom: 74px;
}

.pl-74 {
  padding-inline-start: 74px;
}

.pr-74 {
  padding-inline-end: 74px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-inline-start: 75px;
}

.pr-75 {
  padding-inline-end: 75px;
}

.pt-76 {
  padding-top: 76px;
}

.pb-76 {
  padding-bottom: 76px;
}

.pl-76 {
  padding-inline-start: 76px;
}

.pr-76 {
  padding-inline-end: 76px;
}

.pt-77 {
  padding-top: 77px;
}

.pb-77 {
  padding-bottom: 77px;
}

.pl-77 {
  padding-inline-start: 77px;
}

.pr-77 {
  padding-inline-end: 77px;
}

.pt-78 {
  padding-top: 78px;
}

.pb-78 {
  padding-bottom: 78px;
}

.pl-78 {
  padding-inline-start: 78px;
}

.pr-78 {
  padding-inline-end: 78px;
}

.pt-79 {
  padding-top: 79px;
}

.pb-79 {
  padding-bottom: 79px;
}

.pl-79 {
  padding-inline-start: 79px;
}

.pr-79 {
  padding-inline-end: 79px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-inline-start: 80px;
}

.pr-80 {
  padding-inline-end: 80px;
}

.pt-81 {
  padding-top: 81px;
}

.pb-81 {
  padding-bottom: 81px;
}

.pl-81 {
  padding-inline-start: 81px;
}

.pr-81 {
  padding-inline-end: 81px;
}

.pt-82 {
  padding-top: 82px;
}

.pb-82 {
  padding-bottom: 82px;
}

.pl-82 {
  padding-inline-start: 82px;
}

.pr-82 {
  padding-inline-end: 82px;
}

.pt-83 {
  padding-top: 83px;
}

.pb-83 {
  padding-bottom: 83px;
}

.pl-83 {
  padding-inline-start: 83px;
}

.pr-83 {
  padding-inline-end: 83px;
}

.pt-84 {
  padding-top: 84px;
}

.pb-84 {
  padding-bottom: 84px;
}

.pl-84 {
  padding-inline-start: 84px;
}

.pr-84 {
  padding-inline-end: 84px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-inline-start: 85px;
}

.pr-85 {
  padding-inline-end: 85px;
}

.pt-86 {
  padding-top: 86px;
}

.pb-86 {
  padding-bottom: 86px;
}

.pl-86 {
  padding-inline-start: 86px;
}

.pr-86 {
  padding-inline-end: 86px;
}

.pt-87 {
  padding-top: 87px;
}

.pb-87 {
  padding-bottom: 87px;
}

.pl-87 {
  padding-inline-start: 87px;
}

.pr-87 {
  padding-inline-end: 87px;
}

.pt-88 {
  padding-top: 88px;
}

.pb-88 {
  padding-bottom: 88px;
}

.pl-88 {
  padding-inline-start: 88px;
}

.pr-88 {
  padding-inline-end: 88px;
}

.pt-89 {
  padding-top: 89px;
}

.pb-89 {
  padding-bottom: 89px;
}

.pl-89 {
  padding-inline-start: 89px;
}

.pr-89 {
  padding-inline-end: 89px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-inline-start: 90px;
}

.pr-90 {
  padding-inline-end: 90px;
}

.pt-91 {
  padding-top: 91px;
}

.pb-91 {
  padding-bottom: 91px;
}

.pl-91 {
  padding-inline-start: 91px;
}

.pr-91 {
  padding-inline-end: 91px;
}

.pt-92 {
  padding-top: 92px;
}

.pb-92 {
  padding-bottom: 92px;
}

.pl-92 {
  padding-inline-start: 92px;
}

.pr-92 {
  padding-inline-end: 92px;
}

.pt-93 {
  padding-top: 93px;
}

.pb-93 {
  padding-bottom: 93px;
}

.pl-93 {
  padding-inline-start: 93px;
}

.pr-93 {
  padding-inline-end: 93px;
}

.pt-94 {
  padding-top: 94px;
}

.pb-94 {
  padding-bottom: 94px;
}

.pl-94 {
  padding-inline-start: 94px;
}

.pr-94 {
  padding-inline-end: 94px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-inline-start: 95px;
}

.pr-95 {
  padding-inline-end: 95px;
}

.pt-96 {
  padding-top: 96px;
}

.pb-96 {
  padding-bottom: 96px;
}

.pl-96 {
  padding-inline-start: 96px;
}

.pr-96 {
  padding-inline-end: 96px;
}

.pt-97 {
  padding-top: 97px;
}

.pb-97 {
  padding-bottom: 97px;
}

.pl-97 {
  padding-inline-start: 97px;
}

.pr-97 {
  padding-inline-end: 97px;
}

.pt-98 {
  padding-top: 98px;
}

.pb-98 {
  padding-bottom: 98px;
}

.pl-98 {
  padding-inline-start: 98px;
}

.pr-98 {
  padding-inline-end: 98px;
}

.pt-99 {
  padding-top: 99px;
}

.pb-99 {
  padding-bottom: 99px;
}

.pl-99 {
  padding-inline-start: 99px;
}

.pr-99 {
  padding-inline-end: 99px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-inline-start: 100px;
}

.pr-100 {
  padding-inline-end: 100px;
}

.pt-101 {
  padding-top: 101px;
}

.pb-101 {
  padding-bottom: 101px;
}

.pl-101 {
  padding-inline-start: 101px;
}

.pr-101 {
  padding-inline-end: 101px;
}

.pt-102 {
  padding-top: 102px;
}

.pb-102 {
  padding-bottom: 102px;
}

.pl-102 {
  padding-inline-start: 102px;
}

.pr-102 {
  padding-inline-end: 102px;
}

.pt-103 {
  padding-top: 103px;
}

.pb-103 {
  padding-bottom: 103px;
}

.pl-103 {
  padding-inline-start: 103px;
}

.pr-103 {
  padding-inline-end: 103px;
}

.pt-104 {
  padding-top: 104px;
}

.pb-104 {
  padding-bottom: 104px;
}

.pl-104 {
  padding-inline-start: 104px;
}

.pr-104 {
  padding-inline-end: 104px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-inline-start: 105px;
}

.pr-105 {
  padding-inline-end: 105px;
}

.pt-106 {
  padding-top: 106px;
}

.pb-106 {
  padding-bottom: 106px;
}

.pl-106 {
  padding-inline-start: 106px;
}

.pr-106 {
  padding-inline-end: 106px;
}

.pt-107 {
  padding-top: 107px;
}

.pb-107 {
  padding-bottom: 107px;
}

.pl-107 {
  padding-inline-start: 107px;
}

.pr-107 {
  padding-inline-end: 107px;
}

.pt-108 {
  padding-top: 108px;
}

.pb-108 {
  padding-bottom: 108px;
}

.pl-108 {
  padding-inline-start: 108px;
}

.pr-108 {
  padding-inline-end: 108px;
}

.pt-109 {
  padding-top: 109px;
}

.pb-109 {
  padding-bottom: 109px;
}

.pl-109 {
  padding-inline-start: 109px;
}

.pr-109 {
  padding-inline-end: 109px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-inline-start: 110px;
}

.pr-110 {
  padding-inline-end: 110px;
}

.pt-111 {
  padding-top: 111px;
}

.pb-111 {
  padding-bottom: 111px;
}

.pl-111 {
  padding-inline-start: 111px;
}

.pr-111 {
  padding-inline-end: 111px;
}

.pt-112 {
  padding-top: 112px;
}

.pb-112 {
  padding-bottom: 112px;
}

.pl-112 {
  padding-inline-start: 112px;
}

.pr-112 {
  padding-inline-end: 112px;
}

.pt-113 {
  padding-top: 113px;
}

.pb-113 {
  padding-bottom: 113px;
}

.pl-113 {
  padding-inline-start: 113px;
}

.pr-113 {
  padding-inline-end: 113px;
}

.pt-114 {
  padding-top: 114px;
}

.pb-114 {
  padding-bottom: 114px;
}

.pl-114 {
  padding-inline-start: 114px;
}

.pr-114 {
  padding-inline-end: 114px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-inline-start: 115px;
}

.pr-115 {
  padding-inline-end: 115px;
}

.pt-116 {
  padding-top: 116px;
}

.pb-116 {
  padding-bottom: 116px;
}

.pl-116 {
  padding-inline-start: 116px;
}

.pr-116 {
  padding-inline-end: 116px;
}

.pt-117 {
  padding-top: 117px;
}

.pb-117 {
  padding-bottom: 117px;
}

.pl-117 {
  padding-inline-start: 117px;
}

.pr-117 {
  padding-inline-end: 117px;
}

.pt-118 {
  padding-top: 118px;
}

.pb-118 {
  padding-bottom: 118px;
}

.pl-118 {
  padding-inline-start: 118px;
}

.pr-118 {
  padding-inline-end: 118px;
}

.pt-119 {
  padding-top: 119px;
}

.pb-119 {
  padding-bottom: 119px;
}

.pl-119 {
  padding-inline-start: 119px;
}

.pr-119 {
  padding-inline-end: 119px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-inline-start: 120px;
}

.pr-120 {
  padding-inline-end: 120px;
}

.pt-121 {
  padding-top: 121px;
}

.pb-121 {
  padding-bottom: 121px;
}

.pl-121 {
  padding-inline-start: 121px;
}

.pr-121 {
  padding-inline-end: 121px;
}

.pt-122 {
  padding-top: 122px;
}

.pb-122 {
  padding-bottom: 122px;
}

.pl-122 {
  padding-inline-start: 122px;
}

.pr-122 {
  padding-inline-end: 122px;
}

.pt-123 {
  padding-top: 123px;
}

.pb-123 {
  padding-bottom: 123px;
}

.pl-123 {
  padding-inline-start: 123px;
}

.pr-123 {
  padding-inline-end: 123px;
}

.pt-124 {
  padding-top: 124px;
}

.pb-124 {
  padding-bottom: 124px;
}

.pl-124 {
  padding-inline-start: 124px;
}

.pr-124 {
  padding-inline-end: 124px;
}

.pt-125 {
  padding-top: 125px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pl-125 {
  padding-inline-start: 125px;
}

.pr-125 {
  padding-inline-end: 125px;
}

.pt-126 {
  padding-top: 126px;
}

.pb-126 {
  padding-bottom: 126px;
}

.pl-126 {
  padding-inline-start: 126px;
}

.pr-126 {
  padding-inline-end: 126px;
}

.pt-127 {
  padding-top: 127px;
}

.pb-127 {
  padding-bottom: 127px;
}

.pl-127 {
  padding-inline-start: 127px;
}

.pr-127 {
  padding-inline-end: 127px;
}

.pt-128 {
  padding-top: 128px;
}

.pb-128 {
  padding-bottom: 128px;
}

.pl-128 {
  padding-inline-start: 128px;
}

.pr-128 {
  padding-inline-end: 128px;
}

.pt-129 {
  padding-top: 129px;
}

.pb-129 {
  padding-bottom: 129px;
}

.pl-129 {
  padding-inline-start: 129px;
}

.pr-129 {
  padding-inline-end: 129px;
}

.pt-130 {
  padding-top: 130px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pl-130 {
  padding-inline-start: 130px;
}

.pr-130 {
  padding-inline-end: 130px;
}

.pt-131 {
  padding-top: 131px;
}

.pb-131 {
  padding-bottom: 131px;
}

.pl-131 {
  padding-inline-start: 131px;
}

.pr-131 {
  padding-inline-end: 131px;
}

.pt-132 {
  padding-top: 132px;
}

.pb-132 {
  padding-bottom: 132px;
}

.pl-132 {
  padding-inline-start: 132px;
}

.pr-132 {
  padding-inline-end: 132px;
}

.pt-133 {
  padding-top: 133px;
}

.pb-133 {
  padding-bottom: 133px;
}

.pl-133 {
  padding-inline-start: 133px;
}

.pr-133 {
  padding-inline-end: 133px;
}

.pt-134 {
  padding-top: 134px;
}

.pb-134 {
  padding-bottom: 134px;
}

.pl-134 {
  padding-inline-start: 134px;
}

.pr-134 {
  padding-inline-end: 134px;
}

.pt-135 {
  padding-top: 135px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pl-135 {
  padding-inline-start: 135px;
}

.pr-135 {
  padding-inline-end: 135px;
}

.pt-136 {
  padding-top: 136px;
}

.pb-136 {
  padding-bottom: 136px;
}

.pl-136 {
  padding-inline-start: 136px;
}

.pr-136 {
  padding-inline-end: 136px;
}

.pt-137 {
  padding-top: 137px;
}

.pb-137 {
  padding-bottom: 137px;
}

.pl-137 {
  padding-inline-start: 137px;
}

.pr-137 {
  padding-inline-end: 137px;
}

.pt-138 {
  padding-top: 138px;
}

.pb-138 {
  padding-bottom: 138px;
}

.pl-138 {
  padding-inline-start: 138px;
}

.pr-138 {
  padding-inline-end: 138px;
}

.pt-139 {
  padding-top: 139px;
}

.pb-139 {
  padding-bottom: 139px;
}

.pl-139 {
  padding-inline-start: 139px;
}

.pr-139 {
  padding-inline-end: 139px;
}

.pt-140 {
  padding-top: 140px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pl-140 {
  padding-inline-start: 140px;
}

.pr-140 {
  padding-inline-end: 140px;
}

.pt-141 {
  padding-top: 141px;
}

.pb-141 {
  padding-bottom: 141px;
}

.pl-141 {
  padding-inline-start: 141px;
}

.pr-141 {
  padding-inline-end: 141px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-142 {
  padding-bottom: 142px;
}

.pl-142 {
  padding-inline-start: 142px;
}

.pr-142 {
  padding-inline-end: 142px;
}

.pt-143 {
  padding-top: 143px;
}

.pb-143 {
  padding-bottom: 143px;
}

.pl-143 {
  padding-inline-start: 143px;
}

.pr-143 {
  padding-inline-end: 143px;
}

.pt-144 {
  padding-top: 144px;
}

.pb-144 {
  padding-bottom: 144px;
}

.pl-144 {
  padding-inline-start: 144px;
}

.pr-144 {
  padding-inline-end: 144px;
}

.pt-145 {
  padding-top: 145px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pl-145 {
  padding-inline-start: 145px;
}

.pr-145 {
  padding-inline-end: 145px;
}

.pt-146 {
  padding-top: 146px;
}

.pb-146 {
  padding-bottom: 146px;
}

.pl-146 {
  padding-inline-start: 146px;
}

.pr-146 {
  padding-inline-end: 146px;
}

.pt-147 {
  padding-top: 147px;
}

.pb-147 {
  padding-bottom: 147px;
}

.pl-147 {
  padding-inline-start: 147px;
}

.pr-147 {
  padding-inline-end: 147px;
}

.pt-148 {
  padding-top: 148px;
}

.pb-148 {
  padding-bottom: 148px;
}

.pl-148 {
  padding-inline-start: 148px;
}

.pr-148 {
  padding-inline-end: 148px;
}

.pt-149 {
  padding-top: 149px;
}

.pb-149 {
  padding-bottom: 149px;
}

.pl-149 {
  padding-inline-start: 149px;
}

.pr-149 {
  padding-inline-end: 149px;
}

.pt-150 {
  padding-top: 150px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pl-150 {
  padding-inline-start: 150px;
}

.pr-150 {
  padding-inline-end: 150px;
}

.pt-151 {
  padding-top: 151px;
}

.pb-151 {
  padding-bottom: 151px;
}

.pl-151 {
  padding-inline-start: 151px;
}

.pr-151 {
  padding-inline-end: 151px;
}

.pt-152 {
  padding-top: 152px;
}

.pb-152 {
  padding-bottom: 152px;
}

.pl-152 {
  padding-inline-start: 152px;
}

.pr-152 {
  padding-inline-end: 152px;
}

.pt-153 {
  padding-top: 153px;
}

.pb-153 {
  padding-bottom: 153px;
}

.pl-153 {
  padding-inline-start: 153px;
}

.pr-153 {
  padding-inline-end: 153px;
}

.pt-154 {
  padding-top: 154px;
}

.pb-154 {
  padding-bottom: 154px;
}

.pl-154 {
  padding-inline-start: 154px;
}

.pr-154 {
  padding-inline-end: 154px;
}

.pt-155 {
  padding-top: 155px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pl-155 {
  padding-inline-start: 155px;
}

.pr-155 {
  padding-inline-end: 155px;
}

.pt-156 {
  padding-top: 156px;
}

.pb-156 {
  padding-bottom: 156px;
}

.pl-156 {
  padding-inline-start: 156px;
}

.pr-156 {
  padding-inline-end: 156px;
}

.pt-157 {
  padding-top: 157px;
}

.pb-157 {
  padding-bottom: 157px;
}

.pl-157 {
  padding-inline-start: 157px;
}

.pr-157 {
  padding-inline-end: 157px;
}

.pt-158 {
  padding-top: 158px;
}

.pb-158 {
  padding-bottom: 158px;
}

.pl-158 {
  padding-inline-start: 158px;
}

.pr-158 {
  padding-inline-end: 158px;
}

.pt-159 {
  padding-top: 159px;
}

.pb-159 {
  padding-bottom: 159px;
}

.pl-159 {
  padding-inline-start: 159px;
}

.pr-159 {
  padding-inline-end: 159px;
}

.pt-160 {
  padding-top: 160px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pl-160 {
  padding-inline-start: 160px;
}

.pr-160 {
  padding-inline-end: 160px;
}

.pt-161 {
  padding-top: 161px;
}

.pb-161 {
  padding-bottom: 161px;
}

.pl-161 {
  padding-inline-start: 161px;
}

.pr-161 {
  padding-inline-end: 161px;
}

.pt-162 {
  padding-top: 162px;
}

.pb-162 {
  padding-bottom: 162px;
}

.pl-162 {
  padding-inline-start: 162px;
}

.pr-162 {
  padding-inline-end: 162px;
}

.pt-163 {
  padding-top: 163px;
}

.pb-163 {
  padding-bottom: 163px;
}

.pl-163 {
  padding-inline-start: 163px;
}

.pr-163 {
  padding-inline-end: 163px;
}

.pt-164 {
  padding-top: 164px;
}

.pb-164 {
  padding-bottom: 164px;
}

.pl-164 {
  padding-inline-start: 164px;
}

.pr-164 {
  padding-inline-end: 164px;
}

.pt-165 {
  padding-top: 165px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pl-165 {
  padding-inline-start: 165px;
}

.pr-165 {
  padding-inline-end: 165px;
}

.pt-166 {
  padding-top: 166px;
}

.pb-166 {
  padding-bottom: 166px;
}

.pl-166 {
  padding-inline-start: 166px;
}

.pr-166 {
  padding-inline-end: 166px;
}

.pt-167 {
  padding-top: 167px;
}

.pb-167 {
  padding-bottom: 167px;
}

.pl-167 {
  padding-inline-start: 167px;
}

.pr-167 {
  padding-inline-end: 167px;
}

.pt-168 {
  padding-top: 168px;
}

.pb-168 {
  padding-bottom: 168px;
}

.pl-168 {
  padding-inline-start: 168px;
}

.pr-168 {
  padding-inline-end: 168px;
}

.pt-169 {
  padding-top: 169px;
}

.pb-169 {
  padding-bottom: 169px;
}

.pl-169 {
  padding-inline-start: 169px;
}

.pr-169 {
  padding-inline-end: 169px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pl-170 {
  padding-inline-start: 170px;
}

.pr-170 {
  padding-inline-end: 170px;
}

.pt-171 {
  padding-top: 171px;
}

.pb-171 {
  padding-bottom: 171px;
}

.pl-171 {
  padding-inline-start: 171px;
}

.pr-171 {
  padding-inline-end: 171px;
}

.pt-172 {
  padding-top: 172px;
}

.pb-172 {
  padding-bottom: 172px;
}

.pl-172 {
  padding-inline-start: 172px;
}

.pr-172 {
  padding-inline-end: 172px;
}

.pt-173 {
  padding-top: 173px;
}

.pb-173 {
  padding-bottom: 173px;
}

.pl-173 {
  padding-inline-start: 173px;
}

.pr-173 {
  padding-inline-end: 173px;
}

.pt-174 {
  padding-top: 174px;
}

.pb-174 {
  padding-bottom: 174px;
}

.pl-174 {
  padding-inline-start: 174px;
}

.pr-174 {
  padding-inline-end: 174px;
}

.pt-175 {
  padding-top: 175px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pl-175 {
  padding-inline-start: 175px;
}

.pr-175 {
  padding-inline-end: 175px;
}

.pt-176 {
  padding-top: 176px;
}

.pb-176 {
  padding-bottom: 176px;
}

.pl-176 {
  padding-inline-start: 176px;
}

.pr-176 {
  padding-inline-end: 176px;
}

.pt-177 {
  padding-top: 177px;
}

.pb-177 {
  padding-bottom: 177px;
}

.pl-177 {
  padding-inline-start: 177px;
}

.pr-177 {
  padding-inline-end: 177px;
}

.pt-178 {
  padding-top: 178px;
}

.pb-178 {
  padding-bottom: 178px;
}

.pl-178 {
  padding-inline-start: 178px;
}

.pr-178 {
  padding-inline-end: 178px;
}

.pt-179 {
  padding-top: 179px;
}

.pb-179 {
  padding-bottom: 179px;
}

.pl-179 {
  padding-inline-start: 179px;
}

.pr-179 {
  padding-inline-end: 179px;
}

.pt-180 {
  padding-top: 180px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pl-180 {
  padding-inline-start: 180px;
}

.pr-180 {
  padding-inline-end: 180px;
}

.pt-181 {
  padding-top: 181px;
}

.pb-181 {
  padding-bottom: 181px;
}

.pl-181 {
  padding-inline-start: 181px;
}

.pr-181 {
  padding-inline-end: 181px;
}

.pt-182 {
  padding-top: 182px;
}

.pb-182 {
  padding-bottom: 182px;
}

.pl-182 {
  padding-inline-start: 182px;
}

.pr-182 {
  padding-inline-end: 182px;
}

.pt-183 {
  padding-top: 183px;
}

.pb-183 {
  padding-bottom: 183px;
}

.pl-183 {
  padding-inline-start: 183px;
}

.pr-183 {
  padding-inline-end: 183px;
}

.pt-184 {
  padding-top: 184px;
}

.pb-184 {
  padding-bottom: 184px;
}

.pl-184 {
  padding-inline-start: 184px;
}

.pr-184 {
  padding-inline-end: 184px;
}

.pt-185 {
  padding-top: 185px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pl-185 {
  padding-inline-start: 185px;
}

.pr-185 {
  padding-inline-end: 185px;
}

.pt-186 {
  padding-top: 186px;
}

.pb-186 {
  padding-bottom: 186px;
}

.pl-186 {
  padding-inline-start: 186px;
}

.pr-186 {
  padding-inline-end: 186px;
}

.pt-187 {
  padding-top: 187px;
}

.pb-187 {
  padding-bottom: 187px;
}

.pl-187 {
  padding-inline-start: 187px;
}

.pr-187 {
  padding-inline-end: 187px;
}

.pt-188 {
  padding-top: 188px;
}

.pb-188 {
  padding-bottom: 188px;
}

.pl-188 {
  padding-inline-start: 188px;
}

.pr-188 {
  padding-inline-end: 188px;
}

.pt-189 {
  padding-top: 189px;
}

.pb-189 {
  padding-bottom: 189px;
}

.pl-189 {
  padding-inline-start: 189px;
}

.pr-189 {
  padding-inline-end: 189px;
}

.pt-190 {
  padding-top: 190px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pl-190 {
  padding-inline-start: 190px;
}

.pr-190 {
  padding-inline-end: 190px;
}

.pt-191 {
  padding-top: 191px;
}

.pb-191 {
  padding-bottom: 191px;
}

.pl-191 {
  padding-inline-start: 191px;
}

.pr-191 {
  padding-inline-end: 191px;
}

.pt-192 {
  padding-top: 192px;
}

.pb-192 {
  padding-bottom: 192px;
}

.pl-192 {
  padding-inline-start: 192px;
}

.pr-192 {
  padding-inline-end: 192px;
}

.pt-193 {
  padding-top: 193px;
}

.pb-193 {
  padding-bottom: 193px;
}

.pl-193 {
  padding-inline-start: 193px;
}

.pr-193 {
  padding-inline-end: 193px;
}

.pt-194 {
  padding-top: 194px;
}

.pb-194 {
  padding-bottom: 194px;
}

.pl-194 {
  padding-inline-start: 194px;
}

.pr-194 {
  padding-inline-end: 194px;
}

.pt-195 {
  padding-top: 195px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pl-195 {
  padding-inline-start: 195px;
}

.pr-195 {
  padding-inline-end: 195px;
}

.pt-196 {
  padding-top: 196px;
}

.pb-196 {
  padding-bottom: 196px;
}

.pl-196 {
  padding-inline-start: 196px;
}

.pr-196 {
  padding-inline-end: 196px;
}

.pt-197 {
  padding-top: 197px;
}

.pb-197 {
  padding-bottom: 197px;
}

.pl-197 {
  padding-inline-start: 197px;
}

.pr-197 {
  padding-inline-end: 197px;
}

.pt-198 {
  padding-top: 198px;
}

.pb-198 {
  padding-bottom: 198px;
}

.pl-198 {
  padding-inline-start: 198px;
}

.pr-198 {
  padding-inline-end: 198px;
}

.pt-199 {
  padding-top: 199px;
}

.pb-199 {
  padding-bottom: 199px;
}

.pl-199 {
  padding-inline-start: 199px;
}

.pr-199 {
  padding-inline-end: 199px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-200 {
  padding-inline-start: 200px;
}

.pr-200 {
  padding-inline-end: 200px;
}

:root {
  --td-ff-body: "Satoshi-Variable";
  --td-ff-fontawesome: "Font Awesome 6 Pro";
  --td-white: hsl(0, 0%, 100%);
  --td-always-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --td-body: #010C1A;
  --td-heading: #080808;
  --td-primary: #a6ef67;
  --td-deep-black: #1A1D1F;
  --td-text-primary: #999999;
  --td-border-primary: #eaeaea;
  --td-yellow: #f2c94c;
  --td-warning: #FFA336;
  --td-success: #03A66D;
  --td-danger: #eb4e5c;
  --td-danger-alt: #eb4e5c;
  --td-green: #03A66D;
  --td-eerie-black: #091628;
  --td-dark-gunmetal: #142032;
  --td-kuwanomi-purple: #0B1623;
  --td-chaos-black: #0F0F0F;
  --td-liberty-blue: #0A1729;
  --td-fw-normal: normal;
  --td-fw-thin: 100;
  --td-fw-elight: 200;
  --td-fw-light: 300;
  --td-fw-regular: 400;
  --td-fw-medium: 500;
  --td-fw-sbold: 600;
  --td-fw-bold: 700;
  --td-fw-ebold: 800;
  --td-fw-black: 900;
  --td-fs-body: 16px;
  --td-fs-p: 16px;
  --td-fs-h1: 52px;
  --td-fs-h2: 42px;
  --td-fs-h3: 32px;
  --td-fs-h4: 24px;
  --td-fs-h5: 20px;
  --td-fs-h6: 16px;
  --td-body-color: #ffffff;
}

/*----------------------------------------*/
/*  Default Spacing
/*----------------------------------------*/
.section_space {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem); /* ~60px to 80px */
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space-top {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space-bottom {
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_title_space {
  margin-bottom: 50px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section_title_space {
    margin-bottom: 44px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .section_title_space {
    margin-bottom: 38px;
  }
}

/*----------------------------------------*/
/*   Typography scss
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--td-ff-body);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--td-text-primary);
  background-color: var(--td-white);
}
body .banner-two.dark-theme {
  background-color: #000000;
}

body.banner-two.dark-theme {
  background-color: #000000;
}

.auth-overlay-bg {
  position: fixed;
  pointer-events: none;
  top: 0;
  opacity: 1;
  inset-inline-start: 0;
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-position: top left;
  background-image: url(../images/bg/auth-body-bg.png);
  z-index: -1;
  background-size: cover;
}
.auth-overlay-bg::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: -2;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--td-white);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
  font-weight: 500;
}

h1,
.h1 {
  font-size: clamp(2.5rem, 4vw + 1rem, 2.986rem);
  /* ~47.78pt */
  line-height: 1.2;
  font-weight: 700;
}

h2,
.h2 {
  font-size: clamp(2rem, 3.5vw + 0.8rem, 2.488rem);
  /* ~38.22pt */
  line-height: 1.2;
  font-weight: 700;
}

h3,
.h3 {
  font-size: clamp(1.75rem, 3vw + 0.5rem, 2.074rem);
  /* ~30.56pt */
  line-height: 1.3;
  font-weight: 700;
}

h4,
.h4 {
  font-size: clamp(1.5rem, 2.5vw + 0.3rem, 1.728rem);
  /* ~24.44pt */
  line-height: 1.4;
}

h5,
.h5 {
  font-size: clamp(1.25rem, 2vw + 0.2rem, 1.44rem);
  /* ~19.56pt */
  line-height: 1.5;
}

h6,
.h6 {
  font-size: clamp(1.1rem, 1.5vw + 0.1rem, 1.2rem);
  /* ~16pt */
  line-height: 1.56;
}

ul {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: 1rem;
  line-height: 1.5;
}
p.b1 {
  font-size: clamp(0.8125rem, 0.25vw + 0.7rem, 0.875rem); /* 13px–14px */
  line-height: 1.4;
}
p.b2 {
  font-size: clamp(0.875rem, 0.4vw + 0.7rem, 1rem); /* 14px–16px */
  line-height: 1.5;
}
p.b3 {
  font-size: clamp(1rem, 0.5vw + 0.8rem, 1.125rem); /* 16px–18px */
  line-height: 1.625;
}
p.b4 {
  font-size: clamp(1.125rem, 0.6vw + 0.8rem, 1.25rem); /* 18px–20px */
  line-height: 1.754;
}
p:last-child {
  margin-bottom: 0px;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

.o-x-clip {
  overflow-x: clip;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
  font-family: var(--td-ff-body);
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: rgba(0, 0, 0, 0.1);
  opacity: 1;
  border-width: 1px;
}

*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.zi-1 {
  z-index: 1;
}

.zi-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

/*-----------------------------------------------------------------------------------

  Project Name:  PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange
  Author: Tdevs
  Support: 
  Description:  PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange
  Version: 1.0

-----------------------------------------------------------------------------------

/*----------------------------------------*/
/*   Globals Default
/*----------------------------------------*/
/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  --bs-gutter-x: 30px;
}

@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1350px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.g-0,
.gx-0 {
  --bs-gutter-x: ;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0 ;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 10px;
}

.gy-12 {
  --bs-gutter-y: 12px;
}

.gy-14 {
  --bs-gutter-y: 14px;
}

.gx-14 {
  --bs-gutter-x: 14px;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}

.g-20 {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-20 {
  --bs-gutter-x: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-24 {
  --bs-gutter-x: 24px;
}

.gy-24 {
  --bs-gutter-y: 24px;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 30px;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 30px;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 40px;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 40px;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 50px;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 50px;
}

.g-60,
.gy-60 {
  --bs-gutter-y: 60px;
}

/*----------------------------------------
  Body Overlay 
-----------------------------------------*/
.body-overlay {
  background-color: #000000;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  inset-inline-start: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.solar--bell-line-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-width='1.5'%3E%3Cpath d='M18.75 9.71v-.705C18.75 5.136 15.726 2 12 2S5.25 5.136 5.25 9.005v.705a4.4 4.4 0 0 1-.692 2.375L3.45 13.81c-1.011 1.575-.239 3.716 1.52 4.214a25.8 25.8 0 0 0 14.06 0c1.759-.498 2.531-2.639 1.52-4.213l-1.108-1.725a4.4 4.4 0 0 1-.693-2.375Z'/%3E%3Cpath stroke-linecap='round' d='M7.5 19c.655 1.748 2.422 3 4.5 3s3.845-1.252 4.5-3' opacity='0.5'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--global-line-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-width='1.5'%3E%3Cpath stroke-linecap='round' d='M2 12h20m-6 0c0 1.313-.104 2.614-.305 3.827c-.2 1.213-.495 2.315-.867 3.244c-.371.929-.812 1.665-1.297 2.168c-.486.502-1.006.761-1.531.761s-1.045-.259-1.53-.761c-.486-.503-.927-1.24-1.298-2.168c-.372-.929-.667-2.03-.868-3.244A23.6 23.6 0 0 1 8 12c0-1.313.103-2.614.304-3.827s.496-2.315.868-3.244c.371-.929.812-1.665 1.297-2.168C10.955 2.26 11.475 2 12 2s1.045.259 1.53.761c.486.503.927 1.24 1.298 2.168c.372.929.667 2.03.867 3.244C15.897 9.386 16 10.687 16 12Z' opacity='0.5'/%3E%3Cpath d='M22 12a10 10 0 1 1-20.001 0A10 10 0 0 1 22 12Z'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--widget-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M6.448 1.75c-.898 0-1.648 0-2.242.08c-.628.084-1.195.27-1.65.725c-.456.456-.642 1.023-.726 1.65c-.08.595-.08 1.345-.08 2.243v.104c0 .898 0 1.648.08 2.242c.084.628.27 1.195.725 1.65c.456.456 1.023.642 1.65.726c.595.08 1.345.08 2.243.08h.104c.898 0 1.648 0 2.242-.08c.628-.084 1.195-.27 1.65-.726c.456-.455.642-1.022.726-1.65c.08-.594.08-1.344.08-2.242v-.104c0-.898 0-1.648-.08-2.242c-.084-.628-.27-1.195-.726-1.65c-.455-.456-1.022-.642-1.65-.726c-.594-.08-1.344-.08-2.242-.08zM3.616 3.616c.13-.13.328-.237.79-.3c.482-.064 1.13-.066 2.094-.066s1.612.002 2.095.067c.461.062.659.169.789.3s.237.327.3.788c.064.483.066 1.131.066 2.095s-.002 1.612-.067 2.095c-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066s-1.612-.002-2.095-.067c-.461-.062-.659-.169-.789-.3s-.237-.327-.3-.788C3.253 8.112 3.25 7.464 3.25 6.5s.002-1.612.067-2.095c.062-.461.169-.659.3-.789m13.831 9.134c-.899 0-1.648 0-2.242.08c-.628.084-1.195.27-1.65.726c-.456.455-.642 1.022-.726 1.65c-.08.594-.08 1.343-.08 2.242v.104c0 .899 0 1.648.08 2.242c.084.628.27 1.195.726 1.65c.455.456 1.022.642 1.65.726c.594.08 1.343.08 2.242.08h.104c.898 0 1.648 0 2.242-.08c.628-.084 1.195-.27 1.65-.726c.456-.455.642-1.022.726-1.65c.08-.594.08-1.343.08-2.242v-.104c0-.898 0-1.648-.08-2.242c-.084-.628-.27-1.195-.726-1.65c-.455-.456-1.022-.642-1.65-.726c-.594-.08-1.343-.08-2.242-.08zm-2.832 1.866c.13-.13.328-.237.79-.3c.482-.064 1.13-.066 2.094-.066s1.612.002 2.095.066c.461.063.659.17.789.3s.237.328.3.79c.064.482.066 1.13.066 2.094s-.002 1.612-.067 2.095c-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066s-1.612-.002-2.095-.067c-.461-.062-.659-.169-.789-.3s-.237-.327-.3-.788c-.064-.483-.066-1.131-.066-2.095s.002-1.612.066-2.095c.063-.461.17-.659.3-.789M6.448 12.75h.104c.898 0 1.648 0 2.242.08c.628.084 1.195.27 1.65.726c.456.455.642 1.022.726 1.65c.08.594.08 1.343.08 2.242v.104c0 .899 0 1.648-.08 2.242c-.084.628-.27 1.195-.726 1.65c-.455.456-1.022.642-1.65.726c-.594.08-1.343.08-2.242.08h-.104c-.898 0-1.648 0-2.242-.08c-.628-.084-1.195-.27-1.65-.726c-.456-.455-.642-1.022-.726-1.65c-.08-.594-.08-1.343-.08-2.242v-.104c0-.899 0-1.648.08-2.242c.084-.628.27-1.195.725-1.65c.456-.456 1.023-.642 1.65-.726c.595-.08 1.345-.08 2.243-.08m-2.043 1.566c-.461.063-.659.17-.789.3s-.237.328-.3.79c-.064.482-.066 1.13-.066 2.094s.002 1.612.067 2.095c.062.461.169.659.3.789s.327.237.788.3c.483.064 1.131.066 2.095.066s1.612-.002 2.095-.067c.461-.062.659-.169.789-.3s.237-.327.3-.788c.064-.483.066-1.131.066-2.095s-.002-1.612-.067-2.095c-.062-.461-.169-.659-.3-.789s-.327-.237-.788-.3c-.483-.064-1.131-.066-2.095-.066s-1.612.002-2.095.066M17.448 1.75c-.899 0-1.648 0-2.242.08c-.628.084-1.195.27-1.65.725c-.456.456-.642 1.023-.726 1.65c-.08.595-.08 1.345-.08 2.243v.104c0 .898 0 1.648.08 2.242c.084.628.27 1.195.726 1.65c.455.456 1.022.642 1.65.726c.594.08 1.343.08 2.242.08h.104c.899 0 1.648 0 2.242-.08c.628-.084 1.195-.27 1.65-.726c.456-.455.642-1.022.726-1.65c.08-.594.08-1.344.08-2.242v-.104c0-.898 0-1.648-.08-2.242c-.084-.628-.27-1.195-.726-1.65c-.455-.456-1.022-.642-1.65-.726c-.594-.08-1.343-.08-2.242-.08zm-2.832 1.866c.13-.13.328-.237.79-.3c.482-.064 1.13-.066 2.094-.066s1.612.002 2.095.067c.461.062.659.169.789.3s.237.327.3.788c.064.483.066 1.131.066 2.095s-.002 1.612-.067 2.095c-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066s-1.612-.002-2.095-.067c-.461-.062-.659-.169-.789-.3s-.237-.327-.3-.788c-.064-.483-.066-1.131-.066-2.095s.002-1.612.066-2.095c.063-.461.17-.659.3-.789' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--user-bold-duotone {
  display: inline-block;
  width: 16px;
  height: 16px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='6' r='4' fill='%23000'/%3E%3Cpath fill='%23000' d='M20 17.5c0 2.485 0 4.5-8 4.5s-8-2.015-8-4.5S7.582 13 12 13s8 2.015 8 4.5' opacity='0.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--danger-triangle-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 7.25a.75.75 0 0 1 .75.75v5a.75.75 0 0 1-1.5 0V8a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M8.294 4.476C9.366 3.115 10.502 2.25 12 2.25s2.634.865 3.706 2.226c1.054 1.34 2.17 3.32 3.6 5.855l.436.772c1.181 2.095 2.115 3.75 2.605 5.077c.5 1.358.62 2.59-.138 3.677c-.735 1.055-1.962 1.486-3.51 1.69c-1.541.203-3.615.203-6.274.203h-.85c-2.66 0-4.733 0-6.274-.203c-1.548-.204-2.775-.635-3.51-1.69c-.758-1.087-.639-2.32-.138-3.677c.49-1.328 1.424-2.982 2.605-5.077l.436-.772c1.429-2.535 2.546-4.516 3.6-5.855m1.179.928C8.499 6.641 7.437 8.52 5.965 11.13l-.364.645c-1.226 2.174-2.097 3.724-2.54 4.925c-.438 1.186-.378 1.814-.04 2.3c.361.516 1.038.87 2.476 1.06c1.432.188 3.406.19 6.14.19h.727c2.733 0 4.707-.002 6.14-.19c1.437-.19 2.114-.544 2.474-1.06c.339-.486.4-1.114-.038-2.3c-.444-1.201-1.315-2.751-2.541-4.925l-.364-.645c-1.472-2.61-2.534-4.489-3.508-5.726C13.562 4.18 12.813 3.75 12 3.75s-1.562.429-2.527 1.654' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--copy-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M15 1.25h-4.056c-1.838 0-3.294 0-4.433.153c-1.172.158-2.121.49-2.87 1.238c-.748.749-1.08 1.698-1.238 2.87c-.153 1.14-.153 2.595-.153 4.433V16a3.75 3.75 0 0 0 3.166 3.705c.137.764.402 1.416.932 1.947c.602.602 1.36.86 2.26.982c.867.116 1.97.116 3.337.116h3.11c1.367 0 2.47 0 3.337-.116c.9-.122 1.658-.38 2.26-.982s.86-1.36.982-2.26c.116-.867.116-1.97.116-3.337v-5.11c0-1.367 0-2.47-.116-3.337c-.122-.9-.38-1.658-.982-2.26c-.531-.53-1.183-.795-1.947-.932A3.75 3.75 0 0 0 15 1.25m2.13 3.021A2.25 2.25 0 0 0 15 2.75h-4c-1.907 0-3.261.002-4.29.14c-1.005.135-1.585.389-2.008.812S4.025 4.705 3.89 5.71c-.138 1.029-.14 2.383-.14 4.29v6a2.25 2.25 0 0 0 1.521 2.13c-.021-.61-.021-1.3-.021-2.075v-5.11c0-1.367 0-2.47.117-3.337c.12-.9.38-1.658.981-2.26c.602-.602 1.36-.86 2.26-.981c.867-.117 1.97-.117 3.337-.117h3.11c.775 0 1.464 0 2.074.021M7.408 6.41c.277-.277.665-.457 1.4-.556c.754-.101 1.756-.103 3.191-.103h3c1.435 0 2.436.002 3.192.103c.734.099 1.122.28 1.399.556c.277.277.457.665.556 1.4c.101.754.103 1.756.103 3.191v5c0 1.435-.002 2.436-.103 3.192c-.099.734-.28 1.122-.556 1.399c-.277.277-.665.457-1.4.556c-.755.101-1.756.103-3.191.103h-3c-1.435 0-2.437-.002-3.192-.103c-.734-.099-1.122-.28-1.399-.556c-.277-.277-.457-.665-.556-1.4c-.101-.755-.103-1.756-.103-3.191v-5c0-1.435.002-2.437.103-3.192c.099-.734.28-1.122.556-1.399' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--sale-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M9.592 3.2a6 6 0 0 1-.495.399c-.298.2-.633.338-.985.408c-.153.03-.313.043-.632.068c-.801.064-1.202.096-1.536.214a2.71 2.71 0 0 0-1.655 1.655c-.118.334-.15.735-.214 1.536a6 6 0 0 1-.068.632c-.07.352-.208.687-.408.985c-.087.13-.191.252-.399.495c-.521.612-.782.918-.935 1.238c-.353.74-.353 1.6 0 2.34c.153.32.414.626.935 1.238c.208.243.312.365.399.495c.2.298.338.633.408.985c.03.153.043.313.068.632c.064.801.096 1.202.214 1.536a2.71 2.71 0 0 0 1.655 1.655c.334.118.735.15 1.536.214c.319.025.479.038.632.068c.352.07.687.209.985.408c.**************.495.399c.612.521.918.782 1.238.935c.74.353 1.6.353 2.34 0c.32-.153.626-.414 1.238-.935c.243-.208.365-.312.495-.399c.298-.2.633-.338.985-.408c.153-.03.313-.043.632-.068c.801-.064 1.202-.096 1.536-.214a2.71 2.71 0 0 0 1.655-1.655c.118-.334.15-.735.214-1.536c.025-.319.038-.479.068-.632c.07-.352.209-.687.408-.985c.087-.13.191-.252.399-.495c.521-.612.782-.918.935-1.238c.353-.74.353-1.6 0-2.34c-.153-.32-.414-.626-.935-1.238a6 6 0 0 1-.399-.495a2.7 2.7 0 0 1-.408-.985a6 6 0 0 1-.068-.632c-.064-.801-.096-1.202-.214-1.536a2.71 2.71 0 0 0-1.655-1.655c-.334-.118-.735-.15-1.536-.214a6 6 0 0 1-.632-.068a2.7 2.7 0 0 1-.985-.408a6 6 0 0 1-.495-.399c-.612-.521-.918-.782-1.238-.935a2.71 2.71 0 0 0-2.34 0c-.32.153-.626.414-1.238.935' opacity='0.5'/%3E%3Cpath fill='%23000' d='M15.83 8.17a.814.814 0 0 1 0 1.151l-6.51 6.51a.814.814 0 0 1-1.151-1.15l6.51-6.511a.814.814 0 0 1 1.152 0m-.033 6.544a1.085 1.085 0 1 1-2.17 0a1.085 1.085 0 0 1 2.17 0m-6.511-4.341a1.085 1.085 0 1 0 0-2.17a1.085 1.085 0 0 0 0 2.17'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--cart-4-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M3.045 11.75c.126.714.303 1.541.51 2.507l.428 2c.487 2.273.731 3.409 1.556 4.076S7.526 21 9.85 21h4.3c2.324 0 3.486 0 4.31-.667c.826-.667 1.07-1.803 1.556-4.076l.429-2c.207-.966.384-1.793.51-2.507z' opacity='0.5'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M9.25 14a.75.75 0 0 1 .75-.75h4a.75.75 0 0 1 0 1.5h-4a.75.75 0 0 1-.75-.75' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M8.33 2.665a.75.75 0 0 1 1.341.67l-1.835 3.67Q8.56 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 1.342-.67l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.436.539.576 1.209.525 2.136H21q.075 0 .146.014a13 13 0 0 1-.19 1.486H3.045a13 13 0 0 1-.192-1.486A1 1 0 0 1 3 10.25h-.147c-.051-.927.09-1.597.525-2.136c.548-.678 1.397-.943 2.75-1.047z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--users-group-two-rounded-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M15.5 7.5a3.5 3.5 0 1 1-7 0a3.5 3.5 0 0 1 7 0'/%3E%3Cpath fill='%23000' d='M19.5 7.5a2.5 2.5 0 1 1-5 0a2.5 2.5 0 0 1 5 0m-15 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 0 0-5 0' opacity='0.4'/%3E%3Cpath fill='%23000' d='M18 16.5c0 1.933-2.686 3.5-6 3.5s-6-1.567-6-3.5S8.686 13 12 13s6 1.567 6 3.5'/%3E%3Cpath fill='%23000' d='M22 16.5c0 1.38-1.79 2.5-4 2.5s-4-1.12-4-2.5s1.79-2.5 4-2.5s4 1.12 4 2.5m-20 0C2 17.88 3.79 19 6 19s4-1.12 4-2.5S8.21 14 6 14s-4 1.12-4 2.5' opacity='0.4'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--cup-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 16c-5.76 0-6.78-5.74-6.96-10.294c-.051-1.266-.076-1.9.4-2.485c.475-.586 1.044-.682 2.183-.874A26.4 26.4 0 0 1 12 2c1.784 0 3.253.157 4.377.347c1.139.192 1.708.288 2.184.874s.45 1.219.4 2.485C18.781 10.26 17.761 16 12.001 16' opacity='0.5'/%3E%3Cpath fill='%23000' d='m17.64 12.422l2.817-1.565c.752-.418 1.128-.627 1.336-.979C22 9.526 22 9.096 22 8.235v-.073c0-1.043 0-1.565-.283-1.958s-.778-.558-1.768-.888L19 5l-.017.085q-.008.283-.022.621c-.088 2.225-.377 4.733-1.32 6.716M5.04 5.706c.087 2.225.376 4.733 1.32 6.716l-2.817-1.565c-.752-.418-1.129-.627-1.336-.979S2 9.096 2 8.235v-.073c0-1.043 0-1.565.283-1.958s.778-.558 1.768-.888L5 5l.017.087q.008.281.022.62'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M5.25 22a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5H6a.75.75 0 0 1-.75-.75' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M15.458 21.25H8.542l.297-1.75a1 1 0 0 1 .98-.804h4.361a1 1 0 0 1 .98.804z' opacity='0.5'/%3E%3Cpath fill='%23000' d='M12 16q-.39 0-.75-.034v2.73h1.5v-2.73A8 8 0 0 1 12 16'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--settings-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M14.279 2.152C13.909 2 13.439 2 12.5 2s-1.408 0-1.779.152a2 2 0 0 0-1.09 1.083c-.094.223-.13.484-.145.863a1.62 1.62 0 0 1-.796 1.353a1.64 1.64 0 0 1-1.579.008c-.338-.178-.583-.276-.825-.308a2.03 2.03 0 0 0-1.49.396c-.318.242-.553.646-1.022 1.453c-.47.807-.704 1.21-.757 1.605c-.07.526.074 1.058.4 1.479c.148.192.357.353.68.555c.477.297.783.803.783 1.361s-.306 1.064-.782 1.36c-.324.203-.533.364-.682.556a2 2 0 0 0-.399 1.479c.053.394.287.798.757 1.605s.704 1.21 1.022 1.453c.424.323.96.465 1.49.396c.242-.032.487-.13.825-.308a1.64 1.64 0 0 1 1.58.008c.486.28.774.795.795 1.353c.015.38.051.64.145.863c.204.49.596.88 1.09 1.083c.37.152.84.152 1.779.152s1.409 0 1.779-.152a2 2 0 0 0 1.09-1.083c.094-.223.13-.483.145-.863c.02-.558.309-1.074.796-1.353a1.64 1.64 0 0 1 1.579-.008c.338.178.583.276.825.308c.53.07 1.066-.073 1.49-.396c.318-.242.553-.646 1.022-1.453c.47-.807.704-1.21.757-1.605a2 2 0 0 0-.4-1.479c-.148-.192-.357-.353-.68-.555c-.477-.297-.783-.803-.783-1.361s.306-1.064.782-1.36c.324-.203.533-.364.682-.556a2 2 0 0 0 .399-1.479c-.053-.394-.287-.798-.757-1.605s-.704-1.21-1.022-1.453a2.03 2.03 0 0 0-1.49-.396c-.242.032-.487.13-.825.308a1.64 1.64 0 0 1-1.58-.008a1.62 1.62 0 0 1-.795-1.353c-.015-.38-.051-.64-.145-.863a2 2 0 0 0-1.09-1.083' clip-rule='evenodd' opacity='0.5'/%3E%3Cpath fill='%23000' d='M15.523 12c0 1.657-1.354 3-3.023 3s-3.023-1.343-3.023-3S10.83 9 12.5 9s3.023 1.343 3.023 3'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--recive-square-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M2 12c0-4.714 0-7.071 1.464-8.536C4.93 2 7.286 2 12 2s7.071 0 8.535 1.464C22 4.93 22 7.286 22 12s0 7.071-1.465 8.535C19.072 22 16.714 22 12 22s-7.071 0-8.536-1.465C2 19.072 2 16.714 2 12' opacity='0.5'/%3E%3Cpath fill='%23000' d='M16.25 8a.75.75 0 0 1 1.5 0v8a.75.75 0 0 1-1.5 0zM7 12.75a.75.75 0 0 1 0-1.5h5.19l-1.72-1.72a.75.75 0 0 1 1.06-1.06l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--star-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M9.153 5.408C10.42 3.136 11.053 2 12 2s1.58 1.136 2.847 3.408l.328.588c.36.646.54.969.82 1.182s.63.292 1.33.45l.636.144c2.46.557 3.689.835 3.982 1.776c.292.94-.546 1.921-2.223 3.882l-.434.507c-.476.557-.715.836-.822 1.18c-.107.345-.071.717.001 1.46l.066.677c.253 2.617.38 3.925-.386 4.506s-1.918.051-4.22-1.009l-.597-.274c-.654-.302-.981-.452-1.328-.452s-.674.15-1.328.452l-.596.274c-2.303 1.06-3.455 1.59-4.22 1.01c-.767-.582-.64-1.89-.387-4.507l.066-.676c.072-.744.108-1.116 0-1.46c-.106-.345-.345-.624-.821-1.18l-.434-.508c-1.677-1.96-2.515-2.941-2.223-3.882S3.58 8.328 6.04 7.772l.636-.144c.699-.158 1.048-.237 1.329-.45s.46-.536.82-1.182z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-down-line-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 9l-7 6l-7-6'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-up-line-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 15l-7-6l-7 6'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--eye-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M9.75 12a2.25 2.25 0 1 1 4.5 0a2.25 2.25 0 0 1-4.5 0'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M2 12c0 1.64.425 2.191 1.275 3.296C4.972 17.5 7.818 20 12 20s7.028-2.5 8.725-4.704C21.575 14.192 22 13.639 22 12c0-1.64-.425-2.191-1.275-3.296C19.028 6.5 16.182 4 12 4S4.972 6.5 3.275 8.704C2.425 9.81 2 10.361 2 12m10-3.75a3.75 3.75 0 1 0 0 7.5a3.75 3.75 0 0 0 0-7.5' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--eye-broken {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-width='1.5'%3E%3Cpath stroke-linecap='round' d='M9 4.46A9.8 9.8 0 0 1 12 4c4.182 0 7.028 2.5 8.725 4.704C21.575 9.81 22 10.361 22 12c0 1.64-.425 2.191-1.275 3.296C19.028 17.5 16.182 20 12 20s-7.028-2.5-8.725-4.704C2.425 14.192 2 13.639 2 12c0-1.64.425-2.191 1.275-3.296A14.5 14.5 0 0 1 5 6.821'/%3E%3Cpath d='M15 12a3 3 0 1 1-6 0a3 3 0 0 1 6 0Z'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--download-minimalistic-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12.554 16.506a.75.75 0 0 1-1.107 0l-4-4.375a.75.75 0 0 1 1.107-1.012l2.696 2.95V3a.75.75 0 0 1 1.5 0v11.068l2.697-2.95a.75.75 0 1 1 1.107 1.013z'/%3E%3Cpath fill='%23000' d='M3.75 15a.75.75 0 0 0-1.5 0v.055c0 1.367 0 2.47.117 3.337c.12.9.38 1.658.981 2.26c.602.602 1.36.86 2.26.982c.867.116 1.97.116 3.337.116h6.11c1.367 0 2.47 0 3.337-.116c.9-.122 1.658-.38 2.26-.982s.86-1.36.982-2.26c.116-.867.116-1.97.116-3.337V15a.75.75 0 0 0-1.5 0c0 1.435-.002 2.436-.103 3.192c-.099.734-.28 1.122-.556 1.399c-.277.277-.665.457-1.4.556c-.755.101-1.756.103-3.191.103H9c-1.435 0-2.437-.002-3.192-.103c-.734-.099-1.122-.28-1.399-.556c-.277-.277-.457-.665-.556-1.4c-.101-.755-.103-1.756-.103-3.191'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-down-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-right-linear {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m9 5l6 7l-6 7'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.ic--round-minus {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M18 12.998H6a1 1 0 0 1 0-2h12a1 1 0 0 1 0 2'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--widget-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M2 6.5c0-2.121 0-3.182.659-3.841S4.379 2 6.5 2s3.182 0 3.841.659S11 4.379 11 6.5s0 3.182-.659 3.841S8.621 11 6.5 11s-3.182 0-3.841-.659S2 8.621 2 6.5m11 11c0-2.121 0-3.182.659-3.841S15.379 13 17.5 13s3.182 0 3.841.659S22 15.379 22 17.5s0 3.182-.659 3.841S19.621 22 17.5 22s-3.182 0-3.841-.659S13 19.621 13 17.5' opacity='0.5'/%3E%3Cpath fill='%23000' d='M2 17.5c0-2.121 0-3.182.659-3.841S4.379 13 6.5 13s3.182 0 3.841.659S11 15.379 11 17.5s0 3.182-.659 3.841S8.621 22 6.5 22s-3.182 0-3.841-.659S2 19.621 2 17.5m11-11c0-2.121 0-3.182.659-3.841S15.379 2 17.5 2s3.182 0 3.841.659S22 4.379 22 6.5s0 3.182-.659 3.841S19.621 11 17.5 11s-3.182 0-3.841-.659S13 8.621 13 6.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--card-send-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M14 20h-4c-3.771 0-5.657 0-6.828-1.172S2 15.771 2 12c0-.442.002-1.608.004-2H22c.002.392 0 1.558 0 2c0 .66 0 1.261-.006 1.812l-1.403-1.403a2.25 2.25 0 0 0-3.182 0l-2 2a2.25 2.25 0 0 0 1.341 3.827v1.738C15.964 20 15.056 20 14 20' opacity='0.5'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M18.47 13.47a.75.75 0 0 1 1.06 0l2 2a.75.75 0 1 1-1.06 1.06l-.72-.72V20a.75.75 0 0 1-1.5 0v-4.19l-.72.72a.75.75 0 1 1-1.06-1.06z' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M12.5 15.25a.75.75 0 0 0 0 1.5H14a.75.75 0 0 0 0-1.5zm-6.5 0a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5zM9.995 4h4.01c3.781 0 5.672 0 6.846 1.116c.846.803 1.083 1.96 1.149 3.884v1H2V9c.066-1.925.303-3.08 1.149-3.884C4.323 4 6.214 4 9.995 4'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--card-recive-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M10 20h4c.66 0 1.261 0 1.812-.006l-.403-.403a2.25 2.25 0 0 1 1.341-3.827V14a2.25 2.25 0 0 1 4.5 0v1.764c.224.025.445.083.654.175C22 14.917 22 13.636 22 12c0-.442 0-1.608-.002-2H2.002C2 10.392 2 11.558 2 12c0 3.771 0 5.657 1.172 6.828S6.229 20 10 20' opacity='0.5'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M18.47 20.53a.75.75 0 0 0 1.06 0l2-2a.75.75 0 1 0-1.06-1.06l-.72.72V14a.75.75 0 0 0-1.5 0v4.19l-.72-.72a.75.75 0 1 0-1.06 1.06z' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M12.5 15.25a.75.75 0 0 0 0 1.5H14a.75.75 0 0 0 0-1.5zm-6.5 0a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5zM9.995 4h4.01c3.781 0 5.672 0 6.846 1.116c.846.803 1.083 1.96 1.149 3.884v1H2V9c.066-1.925.303-3.08 1.149-3.884C4.323 4 6.214 4 9.995 4'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--cash-out-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M9 20h6c1.885 0 2.828 0 3.414-.586c.471-.471.563-1.174.581-2.414H5.004c.018 1.24.11 1.943.581 2.414C6.171 20 7.114 20 9 20' opacity='0.5'/%3E%3Cpath fill='%23000' d='M11.25 6H9c-1.886 0-2.828 0-3.414.586S5 8.114 5 10v6q-.001.551.004 1h13.992q.005-.449.004-1v-6c0-1.886 0-2.828-.586-3.414S16.886 6 15 6h-2.25v4.973l.68-.794a.75.75 0 1 1 1.14.976l-2 2.333a.75.75 0 0 1-1.14 0l-2-2.333a.75.75 0 0 1 1.14-.976l.68.794z'/%3E%3Cpath fill='%23000' d='M18.111 3H5.89C3.74 3 2 4.8 2 7.02c0 1.905 1.28 3.5 3 3.915V10c0-1.886 0-2.828.586-3.414S7.114 6 9 6h6c1.886 0 2.828 0 3.414.586S19 8.114 19 10v.935c1.72-.415 3-2.01 3-3.915C22 4.8 20.259 3 18.111 3' opacity='0.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--clock-circle-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10' opacity='0.5'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M12 7.25a.75.75 0 0 1 .75.75v3.69l2.28 2.28a.75.75 0 1 1-1.06 1.06l-2.5-2.5a.75.75 0 0 1-.22-.53V8a.75.75 0 0 1 .75-.75' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--wallet-money-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M4.892 9.614c0-.402.323-.728.722-.728H9.47c.4 0 .723.326.723.728a.726.726 0 0 1-.723.729H5.614a.726.726 0 0 1-.722-.729'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M21.188 10.004q-.094-.005-.2-.004h-2.773C15.944 10 14 11.736 14 14s1.944 4 4.215 4h2.773q.106.001.2-.004c.923-.056 1.739-.757 1.808-1.737c.004-.064.004-.133.004-.197v-4.124c0-.064 0-.133-.004-.197c-.069-.98-.885-1.68-1.808-1.737m-3.217 5.063c.584 0 1.058-.478 1.058-1.067c0-.59-.474-1.067-1.058-1.067s-1.06.478-1.06 1.067c0 .59.475 1.067 1.06 1.067' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M21.14 10.002c0-1.181-.044-2.448-.798-3.355a4 4 0 0 0-.233-.256c-.749-.748-1.698-1.08-2.87-1.238C16.099 5 14.644 5 12.806 5h-2.112C8.856 5 7.4 5 6.26 5.153c-1.172.158-2.121.49-2.87 1.238c-.748.749-1.08 1.698-1.238 2.87C2 10.401 2 11.856 2 13.694v.112c0 1.838 0 3.294.153 4.433c.158 1.172.49 2.121 1.238 2.87c.749.748 1.698 1.08 2.87 1.238c1.14.153 2.595.153 4.433.153h2.112c1.838 0 3.294 0 4.433-.153c1.172-.158 2.121-.49 2.87-1.238q.305-.308.526-.66c.45-.72.504-1.602.504-2.45l-.15.001h-2.774C15.944 18 14 16.264 14 14s1.944-4 4.215-4h2.773q.079 0 .151.002' opacity='0.5'/%3E%3Cpath fill='%23000' d='M10.101 2.572L8 3.992l-1.733 1.16C7.405 5 8.859 5 10.694 5h2.112c1.838 0 3.294 0 4.433.153q.344.045.662.114L16 4l-2.113-1.428a3.42 3.42 0 0 0-3.786 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--rocket-2-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='m15.502 14.367l5.03-5.014c.724-.722 1.087-1.083 1.277-1.543C22 7.351 22 6.84 22 5.82v-.49c0-1.57 0-2.355-.49-2.843C21.022 2 20.235 2 18.659 2h-.489c-1.024 0-1.537 0-1.997.19s-.823.551-1.547 1.274l-5.03 5.014c-.846.844-1.371 1.367-1.574 1.873c-.064.16-.097.317-.097.483c0 .69.557 1.245 1.671 2.356l.15.149l1.754-1.78a.645.645 0 0 1 .919.906l-1.76 1.785l.119.117c1.114 1.11 1.67 1.666 2.362 1.666q.228 0 .447-.081c.519-.191 1.048-.72 1.916-1.585m2.363-5.888c-.652.65-1.71.65-2.363 0a1.66 1.66 0 0 1 0-2.356c.653-.65 1.71-.65 2.363 0s.653 1.705 0 2.356M2.774 12.481a.76.76 0 0 1 0 1.074l-.156.155a.34.34 0 0 0 0 .48a.34.34 0 0 0 .483 0l1.713-1.71a.76.76 0 0 1 1.072 1.075l-1.712 1.71a1.86 1.86 0 0 1-2.629 0a1.857 1.857 0 0 1 0-2.629l.156-.155a.76.76 0 0 1 1.073 0m4.523 4.215c.293.3.288.78-.012 1.073l-1.73 1.692a.759.759 0 0 1-1.061-1.085l1.73-1.692a.76.76 0 0 1 1.073.012m4.184 1.422a.76.76 0 0 1 0 1.074l-1.713 1.71a.34.34 0 0 0 0 .48c.**************.484 0l.156-.155A.759.759 0 0 1 11.48 22.3l-.155.155a1.86 1.86 0 0 1-2.63 0a1.857 1.857 0 0 1 0-2.629l1.713-1.71a.76.76 0 0 1 1.073.001' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M10.846 5.41L8.658 7.59c-.402.401-.77.769-1.062 1.101a5 5 0 0 0-.532.706l-.022-.021l-.08-.08a4.2 4.2 0 0 0-1.319-.865l-.106-.042l-.325-.13a.658.658 0 0 1-.223-1.077c.963-.96 2.12-2.114 2.679-2.346a2.9 2.9 0 0 1 1.537-.197c.47.07.915.311 1.641.77m3.736 11.484c.176.18.293.306.399.44q.21.268.373.567c.123.223.218.462.408.939c.155.388.67.491.968.193l.073-.072c.963-.96 2.12-2.114 2.353-2.67a2.9 2.9 0 0 0 .197-1.534c-.07-.468-.312-.912-.772-1.636l-2.195 2.189c-.411.41-.789.786-1.13 1.08a5 5 0 0 1-.674.504m-6.896-2.33a.759.759 0 1 0-1.073-1.073L4.47 15.632a.759.759 0 1 0 1.074 1.074zm2.809 2.806a.759.759 0 1 0-1.073-1.073l-2.128 2.127a.76.76 0 0 0 1.074 1.074z' opacity='0.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--structure-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M8 5a3 3 0 1 1-6 0a3 3 0 0 1 6 0m14 0a3 3 0 1 1-6 0a3 3 0 0 1 6 0M8 19a3 3 0 1 1-6 0a3 3 0 0 1 6 0m14 0a3 3 0 1 1-6 0a3 3 0 0 1 6 0'/%3E%3Cpath fill='%23000' d='M16.093 4.256A1 1 0 0 0 16 4.25H8a1 1 0 0 0-.093.006a3 3 0 0 1 0 1.488q.045.006.093.006h8a1 1 0 0 0 .093-.006a3 3 0 0 1 0-1.488M19 8q.386 0 .744-.093q.006.045.006.093v8a1 1 0 0 1-.006.093a3 3 0 0 0-1.488 0A1 1 0 0 1 18.25 16V8q0-.048.006-.093q.358.091.744.093m-2.907 10.256A1 1 0 0 0 16 18.25H8a1 1 0 0 0-.093.006a3 3 0 0 1 0 1.488q.045.006.093.006h8a1 1 0 0 0 .093-.006a3 3 0 0 1 0-1.488M5 8q-.386 0-.744-.093A1 1 0 0 0 4.25 8v8q0 .048.006.093a3 3 0 0 1 1.488 0A1 1 0 0 0 5.75 16V8a1 1 0 0 0-.006-.093Q5.386 7.998 5 8' opacity='0.5'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--cup-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 16c-5.76 0-6.78-5.74-6.96-10.294c-.051-1.266-.076-1.9.4-2.485c.475-.586 1.044-.682 2.183-.874A26.4 26.4 0 0 1 12 2c1.784 0 3.253.157 4.377.347c1.139.192 1.708.288 2.184.874s.45 1.219.4 2.485C18.781 10.26 17.761 16 12.001 16' opacity='0.5'/%3E%3Cpath fill='%23000' d='m17.64 12.422l2.817-1.565c.752-.418 1.128-.627 1.336-.979C22 9.526 22 9.096 22 8.235v-.073c0-1.043 0-1.565-.283-1.958s-.778-.558-1.768-.888L19 5l-.017.085q-.008.283-.022.621c-.088 2.225-.377 4.733-1.32 6.716M5.04 5.706c.087 2.225.376 4.733 1.32 6.716l-2.817-1.565c-.752-.418-1.129-.627-1.336-.979S2 9.096 2 8.235v-.073c0-1.043 0-1.565.283-1.958s.778-.558 1.768-.888L5 5l.017.087q.008.281.022.62'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M5.25 22a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5H6a.75.75 0 0 1-.75-.75' clip-rule='evenodd'/%3E%3Cpath fill='%23000' d='M15.458 21.25H8.542l.297-1.75a1 1 0 0 1 .98-.804h4.361a1 1 0 0 1 .98.804z' opacity='0.5'/%3E%3Cpath fill='%23000' d='M12 16q-.39 0-.75-.034v2.73h1.5v-2.73A8 8 0 0 1 12 16'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--settings-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M14.279 2.152C13.909 2 13.439 2 12.5 2s-1.408 0-1.779.152a2 2 0 0 0-1.09 1.083c-.094.223-.13.484-.145.863a1.62 1.62 0 0 1-.796 1.353a1.64 1.64 0 0 1-1.579.008c-.338-.178-.583-.276-.825-.308a2.03 2.03 0 0 0-1.49.396c-.318.242-.553.646-1.022 1.453c-.47.807-.704 1.21-.757 1.605c-.07.526.074 1.058.4 1.479c.148.192.357.353.68.555c.477.297.783.803.783 1.361s-.306 1.064-.782 1.36c-.324.203-.533.364-.682.556a2 2 0 0 0-.399 1.479c.053.394.287.798.757 1.605s.704 1.21 1.022 1.453c.424.323.96.465 1.49.396c.242-.032.487-.13.825-.308a1.64 1.64 0 0 1 1.58.008c.486.28.774.795.795 1.353c.015.38.051.64.145.863c.204.49.596.88 1.09 1.083c.37.152.84.152 1.779.152s1.409 0 1.779-.152a2 2 0 0 0 1.09-1.083c.094-.223.13-.483.145-.863c.02-.558.309-1.074.796-1.353a1.64 1.64 0 0 1 1.579-.008c.338.178.583.276.825.308c.53.07 1.066-.073 1.49-.396c.318-.242.553-.646 1.022-1.453c.47-.807.704-1.21.757-1.605a2 2 0 0 0-.4-1.479c-.148-.192-.357-.353-.68-.555c-.477-.297-.783-.803-.783-1.361s.306-1.064.782-1.36c.324-.203.533-.364.682-.556a2 2 0 0 0 .399-1.479c-.053-.394-.287-.798-.757-1.605s-.704-1.21-1.022-1.453a2.03 2.03 0 0 0-1.49-.396c-.242.032-.487.13-.825.308a1.64 1.64 0 0 1-1.58-.008a1.62 1.62 0 0 1-.795-1.353c-.015-.38-.051-.64-.145-.863a2 2 0 0 0-1.09-1.083' clip-rule='evenodd' opacity='0.5'/%3E%3Cpath fill='%23000' d='M15.523 12c0 1.657-1.354 3-3.023 3s-3.023-1.343-3.023-3S10.83 9 12.5 9s3.023 1.343 3.023 3'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--download-minimalistic-linear {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M3 15c0 2.828 0 4.243.879 5.121C4.757 21 6.172 21 9 21h6c2.828 0 4.243 0 5.121-.879C21 19.243 21 17.828 21 15M12 3v13m0 0l4-4.375M12 16l-4-4.375'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.ic--round-plus {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M18 12.998h-5v5a1 1 0 0 1-2 0v-5H6a1 1 0 0 1 0-2h5v-5a1 1 0 0 1 2 0v5h5a1 1 0 0 1 0 2'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.mage--eye-off {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'%3E%3Cpath d='M5.45 16.92a10.8 10.8 0 0 1-2.55-3.71a1.85 1.85 0 0 1 0-1.46A10.6 10.6 0 0 1 6.62 7.1A9 9 0 0 1 12 5.48a8.8 8.8 0 0 1 4 .85m2.56 1.72a10.85 10.85 0 0 1 2.54 3.7a1.85 1.85 0 0 1 0 1.46a10.6 10.6 0 0 1-3.72 4.65A9 9 0 0 1 12 19.48a8.8 8.8 0 0 1-4-.85'/%3E%3Cpath d='M8.71 13.65a3.3 3.3 0 0 1-.21-1.17a3.5 3.5 0 0 1 3.5-3.5c.4-.002.796.07 1.17.21m2.12 2.12c.14.374.212.77.21 1.17a3.5 3.5 0 0 1-3.5 3.5a3.3 3.3 0 0 1-1.17-.21M3 20L19 4'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.mage--eye {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'%3E%3Cpath d='M2 12s3.818-7 10-7s10 7 10 7s-3.818 7-10 7s-10-7-10-7z'/%3E%3Ccircle cx='12' cy='12' r='3'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.f7--eye-fill {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56 56'%3E%3Cpath fill='%23000' d='M28.01 46.403C44.556 46.403 56 33.017 56 28.844c0-4.193-11.465-17.558-27.99-17.558C11.588 11.286 0 24.651 0 28.844c0 4.173 11.671 17.559 28.01 17.559m0-6.053c-6.445 0-11.526-5.226-11.567-11.506C16.422 22.4 21.565 17.34 28.01 17.34c6.404 0 11.547 5.06 11.547 11.505c0 6.28-5.143 11.506-11.547 11.506m0-7.354c2.293 0 4.194-1.88 4.194-4.152c0-2.293-1.9-4.172-4.194-4.172c-2.313 0-4.214 1.88-4.214 4.172c0 2.273 1.9 4.152 4.214 4.152'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.f7--eye-slash-fill {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 56 56'%3E%3Cpath fill='%23000' d='M43.95 47.323c.304.305.732.468 1.119.468c.814 0 1.567-.733 1.567-1.568c0-.407-.163-.814-.468-1.12L12.112 11.069a1.57 1.57 0 0 0-1.12-.448c-.814 0-1.567.733-1.567 1.547c0 .428.143.835.448 1.12Zm1.913-5.761C52.255 37.43 56 32.056 56 29.674c0-4.132-11.297-17.303-27.99-17.303c-3.46 0-6.697.59-9.669 1.567l5.313 5.293a11 11 0 0 1 4.356-.896c6.31 0 11.38 4.988 11.38 11.339c0 1.547-.326 3.033-.958 4.336ZM28.01 46.977c3.766 0 7.267-.672 10.402-1.771l-5.394-5.395a10.8 10.8 0 0 1-5.008 1.201c-6.35 0-11.379-5.15-11.4-11.338c0-1.832.428-3.562 1.202-5.09l-7.084-7.124C4.01 21.592 0 27.231 0 29.674c0 4.112 11.501 17.303 28.01 17.303m6.473-17.73a6.446 6.446 0 0 0-6.473-6.454c-.264 0-.53.02-.773.041l7.206 7.206c.02-.244.04-.529.04-.794m-12.966-.041c0 3.582 2.951 6.473 6.514 6.473c.285 0 .55-.02.834-.04l-7.308-7.309c-.02.285-.04.59-.04.876'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--check-circle-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M16.03 10.03a.75.75 0 1 0-1.06-1.06l-4.47 4.47l-1.47-1.47a.75.75 0 0 0-1.06 1.06l2 2a.75.75 0 0 0 1.06 0z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M12 1.25C6.063 1.25 1.25 6.063 1.25 12S6.063 22.75 12 22.75S22.75 17.937 22.75 12S17.937 1.25 12 1.25M2.75 12a9.25 9.25 0 1 1 18.5 0a9.25 9.25 0 0 1-18.5 0' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--close-circle-linear {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-width='1.5'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath stroke-linecap='round' d='m14.5 9.5l-5 5m0-5l5 5'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--calendar-minimalistic-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M22 14v-2c0-.839 0-1.585-.013-2.25H2.013C2 10.415 2 11.161 2 12v2c0 3.771 0 5.657 1.172 6.828S6.229 22 10 22h4c3.771 0 5.657 0 6.828-1.172S22 17.771 22 14M7.75 2.5a.75.75 0 0 0-1.5 0v1.58c-1.44.115-2.384.397-3.078 1.092c-.695.694-.977 1.639-1.093 3.078h19.842c-.116-1.44-.398-2.384-1.093-3.078c-.694-.695-1.639-.977-3.078-1.093V2.5a.75.75 0 0 0-1.5 0v1.513C15.585 4 14.839 4 14 4h-4c-.839 0-1.585 0-2.25.013z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.hugeicons--new-twitter {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m3 21l7.548-7.548M21 3l-7.548 7.548m0 0L8 3H3l7.548 10.452m2.904-2.904L21 21h-5l-5.452-7.548' color='%23000'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.hugeicons--linkedin-02 {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M4.5 9.5H4c-.943 0-1.414 0-1.707.293S2 10.557 2 11.5V20c0 .943 0 1.414.293 1.707S3.057 22 4 22h.5c.943 0 1.414 0 1.707-.293S6.5 20.943 6.5 20v-8.5c0-.943 0-1.414-.293-1.707S5.443 9.5 4.5 9.5m2-5.25a2.25 2.25 0 1 1-4.5 0a2.25 2.25 0 0 1 4.5 0m5.826 5.25H11.5c-.943 0-1.414 0-1.707.293S9.5 10.557 9.5 11.5V20c0 .943 0 1.414.293 1.707S10.557 22 11.5 22h.5c.943 0 1.414 0 1.707-.293S14 20.943 14 20v-3.5c0-1.657.528-3 2.088-3c.78 0 1.412.672 1.412 1.5v4.5c0 .943 0 1.414.293 1.707s.764.293 1.707.293h.499c.942 0 1.414 0 1.707-.293c.292-.293.293-.764.293-1.706L22 14c0-2.486-2.364-4.5-4.703-4.5c-1.332 0-2.52.652-3.297 1.673c0-.63 0-.945-.137-1.179a1 1 0 0 0-.358-.358c-.234-.137-.549-.137-1.179-.137' color='%23000'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.hugeicons--facebook-02 {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6.182 10.333c-.978 0-1.182.192-1.182 1.111v1.667c0 .92.204 1.111 1.182 1.111h2.363v6.667c0 .92.205 1.111 1.182 1.111h2.364c.978 0 1.182-.192 1.182-1.111v-6.667h2.654c.741 0 .932-.135 1.136-.806l.507-1.666c.349-1.149.133-1.417-1.137-1.417h-3.16V7.556c0-.614.529-1.112 1.181-1.112h3.364c.978 0 1.182-.191 1.182-1.11V3.11C19 2.191 18.796 2 17.818 2h-3.364c-3.263 0-5.909 2.487-5.909 5.556v2.777z' color='%23000'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.hugeicons--whatsapp {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' color='%23000'%3E%3Cpath d='M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22'/%3E%3Cpath d='m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87'/%3E%3C/g%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--menu-dots-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M7 12a2 2 0 1 1-4 0a2 2 0 0 1 4 0m7 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0m7 0a2 2 0 1 1-4 0a2 2 0 0 1 4 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-left-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M15.488 4.43a.75.75 0 0 1 .081 1.058L9.988 12l5.581 6.512a.75.75 0 1 1-1.138.976l-6-7a.75.75 0 0 1 0-.976l6-7a.75.75 0 0 1 1.057-.081' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--alt-arrow-right-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M8.512 4.43a.75.75 0 0 1 1.057.082l6 7a.75.75 0 0 1 0 .976l-6 7a.75.75 0 0 1-1.138-.976L14.012 12L8.431 5.488a.75.75 0 0 1 .08-1.057' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--chat-round-line-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.6.376 3.112 1.043 4.453c.178.356.237.763.134 1.148l-.595 2.226a1.3 1.3 0 0 0 1.591 1.592l2.226-.596a1.63 1.63 0 0 1 1.149.133A9.96 9.96 0 0 0 12 22' opacity='0.5'/%3E%3Cpath fill='%23000' d='M7.825 12.85a.825.825 0 0 0 0 1.65h6.05a.825.825 0 0 0 0-1.65zm0-3.85a.825.825 0 0 0 0 1.65h8.8a.825.825 0 0 0 0-1.65z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--copy-bold {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M15.24 2h-3.894c-1.764 0-3.162 0-4.255.148c-1.126.152-2.037.472-2.755 1.193c-.719.721-1.038 1.636-1.189 2.766C3 7.205 3 8.608 3 10.379v5.838c0 1.508.92 2.8 2.227 3.342c-.067-.91-.067-2.185-.067-3.247v-5.01c0-1.281 0-2.386.118-3.27c.127-.948.413-1.856 1.147-2.593s1.639-1.024 2.583-1.152c.88-.118 1.98-.118 3.257-.118h3.07c1.276 0 2.374 0 3.255.118A3.6 3.6 0 0 0 15.24 2'/%3E%3Cpath fill='%23000' d='M6.6 11.397c0-2.726 0-4.089.844-4.936c.843-.847 2.2-.847 4.916-.847h2.88c2.715 0 4.073 0 4.917.847S21 8.671 21 11.397v4.82c0 2.726 0 4.089-.843 4.936c-.844.847-2.202.847-4.917.847h-2.88c-2.715 0-4.073 0-4.916-.847c-.844-.847-.844-2.21-.844-4.936z'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--check-circle-bold-duotone {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12S6.477 2 12 2s10 4.477 10 10' opacity='0.5'/%3E%3Cpath fill='%23000' d='M16.03 8.97a.75.75 0 0 1 0 1.06l-5 5a.75.75 0 0 1-1.06 0l-2-2a.75.75 0 1 1 1.06-1.06l1.47 1.47l2.235-2.235L14.97 8.97a.75.75 0 0 1 1.06 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--pen-new-square-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M11.943 1.25H13.5a.75.75 0 0 1 0 1.5H12c-2.378 0-4.086.002-5.386.176c-1.279.172-2.05.5-2.62 1.069c-.569.57-.896 1.34-1.068 2.619c-.174 1.3-.176 3.008-.176 5.386s.002 4.086.176 5.386c.172 1.279.5 2.05 1.069 2.62c.57.569 1.34.896 2.619 1.068c1.3.174 3.008.176 5.386.176s4.086-.002 5.386-.176c1.279-.172 2.05-.5 2.62-1.069c.569-.57.896-1.34 1.068-2.619c.174-1.3.176-3.008.176-5.386v-1.5a.75.75 0 0 1 1.5 0v1.557c0 2.309 0 4.118-.19 5.53c-.194 1.444-.6 2.584-1.494 3.479c-.895.895-2.035 1.3-3.48 1.494c-1.411.19-3.22.19-5.529.19h-.114c-2.309 0-4.118 0-5.53-.19c-1.444-.194-2.584-.6-3.479-1.494c-.895-.895-1.3-2.035-1.494-3.48c-.19-1.411-.19-3.22-.19-5.529v-.114c0-2.309 0-4.118.19-5.53c.194-1.444.6-2.584 1.494-3.479c.895-.895 2.035-1.3 3.48-1.494c1.411-.19 3.22-.19 5.529-.19m4.827 1.026a3.503 3.503 0 0 1 4.954 4.953l-6.648 6.649c-.371.37-.604.604-.863.806a5.3 5.3 0 0 1-.987.61c-.297.141-.61.245-1.107.411l-2.905.968a1.492 1.492 0 0 1-1.887-1.887l.968-2.905c.166-.498.27-.81.411-1.107q.252-.526.61-.987c.202-.26.435-.492.806-.863zm3.893 1.06a2.003 2.003 0 0 0-2.832 0l-.376.377q.032.145.098.338c.143.413.415.957.927 1.469a3.9 3.9 0 0 0 1.807 1.025l.376-.376a2.003 2.003 0 0 0 0-2.832m-1.558 4.391a5.4 5.4 0 0 1-1.686-1.146a5.4 5.4 0 0 1-1.146-1.686L11.218 9.95c-.417.417-.58.582-.72.76a4 4 0 0 0-.437.71c-.098.203-.172.423-.359.982l-.431 1.295l1.032 1.033l1.295-.432c.56-.187.779-.261.983-.358q.378-.18.71-.439c.177-.139.342-.302.759-.718z' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--add-square-outline {
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12.75 9a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V15a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M12.057 1.25h-.114c-2.309 0-4.118 0-5.53.19c-1.444.194-2.584.6-3.479 1.494c-.895.895-1.3 2.035-1.494 3.48c-.19 1.411-.19 3.22-.19 5.529v.114c0 2.309 0 4.118.19 5.53c.194 1.444.6 2.584 1.494 3.479c.895.895 2.035 1.3 3.48 1.494c1.411.19 3.22.19 5.529.19h.114c2.309 0 4.118 0 5.53-.19c1.444-.194 2.584-.6 3.479-1.494c.895-.895 1.3-2.035 1.494-3.48c.19-1.411.19-3.22.19-5.529v-.114c0-2.309 0-4.118-.19-5.53c-.194-1.444-.6-2.584-1.494-3.479c-.895-.895-2.035-1.3-3.48-1.494c-1.411-.19-3.22-.19-5.529-.19M3.995 3.995c.57-.57 1.34-.897 2.619-1.069c1.3-.174 3.008-.176 5.386-.176s4.086.002 5.386.176c1.279.172 2.05.5 2.62 1.069c.569.57.896 1.34 1.068 2.619c.174 1.3.176 3.008.176 5.386s-.002 4.086-.176 5.386c-.172 1.279-.5 2.05-1.069 2.62c-.57.569-1.34.896-2.619 1.068c-1.3.174-3.008.176-5.386.176s-4.086-.002-5.386-.176c-1.279-.172-2.05-.5-2.62-1.069c-.569-.57-.896-1.34-1.068-2.619c-.174-1.3-.176-3.008-.176-5.386s.002-4.086.176-5.386c.172-1.279.5-2.05 1.069-2.62' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--close-circle-outline {
  display: inline-block;
  width: 24px;
  height: 24px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M10.03 8.97a.75.75 0 0 0-1.06 1.06L10.94 12l-1.97 1.97a.75.75 0 1 0 1.06 1.06L12 13.06l1.97 1.97a.75.75 0 0 0 1.06-1.06L13.06 12l1.97-1.97a.75.75 0 1 0-1.06-1.06L12 10.94z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M12 1.25C6.063 1.25 1.25 6.063 1.25 12S6.063 22.75 12 22.75S22.75 17.937 22.75 12S17.937 1.25 12 1.25M2.75 12a9.25 9.25 0 1 1 18.5 0a9.25 9.25 0 0 1-18.5 0' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

.solar--close-circle-outline {
  display: inline-block;
  width: 24px;
  height: 24px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M10.03 8.97a.75.75 0 0 0-1.06 1.06L10.94 12l-1.97 1.97a.75.75 0 1 0 1.06 1.06L12 13.06l1.97 1.97a.75.75 0 0 0 1.06-1.06L13.06 12l1.97-1.97a.75.75 0 1 0-1.06-1.06L12 10.94z'/%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M12 1.25C6.063 1.25 1.25 6.063 1.25 12S6.063 22.75 12 22.75S22.75 17.937 22.75 12S17.937 1.25 12 1.25M2.75 12a9.25 9.25 0 1 1 18.5 0a9.25 9.25 0 0 1-18.5 0' clip-rule='evenodd'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
}

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 1800px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 31px;
  font-weight: 200;
  inset-inline-end: -20px;
  margin-top: -25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
  max-width: 1040px;
  position: fixed;
  bottom: 30px;
  inset-inline-start: 30px;
  left: 50%;
  transform: translateX(-50%);
  row-gap: 12px;
  column-gap: 12px;
  border-radius: 12px;
  border: 1px solid #26456F;
  background: #0E1B2C;
  box-shadow: 0px 23px 100px 0px rgba(166, 239, 103, 0.16);
  padding: 12px 24px 12px 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  z-index: 111;
  transition: 0.3s;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .caches-privacy {
    flex-direction: column;
    align-items: self-start;
  }
}
@media (max-width: 480px) {
  .caches-privacy {
    padding: 12px 16px 12px 16px;
  }
  .caches-privacy .caches-btns .td-btn {
    font-size: 12px;
  }
}
.caches-privacy .caches-contents .title {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--td-white);
  font-weight: 600;
}
@media (max-width: 575px) {
  .caches-privacy .caches-contents .title {
    font-size: 18px;
  }
}
.caches-privacy .caches-contents p {
  font-size: 14px;
  margin-bottom: 0;
  color: var(--td-white);
}
.caches-privacy .caches-contents p a {
  color: #a6ef67;
}
.caches-privacy .caches-btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 0 0 auto;
  flex-wrap: wrap;
  gap: 12px;
}

/*----------------------------------------*/
/* FAQ styles
/*----------------------------------------*/
.td-faq-syle {
  margin-inline-start: 2.1875rem;
  padding-bottom: 50px;
  margin-bottom: -50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-faq-syle {
    margin-inline-start: 0;
  }
}
.td-faq-syle .accordion .accordion-button {
  padding: 0;
  font-size: 1.25rem;
  background: transparent;
  box-shadow: none;
  color: #999999;
  font-weight: 500;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-faq-syle .accordion .accordion-button {
    font-size: 1.125rem;
  }
}
@media (max-width: 480px) {
  .td-faq-syle .accordion .accordion-button {
    font-size: 1rem;
  }
}
.td-faq-syle .accordion .accordion-button:not(.collapsed) {
  border-radius: 0;
}
.td-faq-syle .accordion .accordion-button:not(.collapsed) span {
  color: var(--td-primary);
}
.td-faq-syle .accordion .accordion-button:not(.collapsed) .accordion-body {
  background: var(--td-white);
}
.td-faq-syle .accordion .accordion-button:not(.collapsed)::after {
  content: "\f068";
}
.td-faq-syle .accordion .accordion-button::after {
  position: absolute;
  inset-inline-end: 20px;
  content: "+";
  font-family: var(--td-ff-fontawesome);
  font-size: 1.125rem;
  font-weight: 400;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
  background-image: none;
}
.td-faq-syle .accordion .accordion-button span {
  padding-inline-end: 0.625rem;
  display: inline-block;
  transition: none;
}
.td-faq-syle .accordion .accordion-body {
  background: transparent;
  border-radius: 0rem;
  padding: 0px 30px 30px 30px;
}
@media (max-width: 480px) {
  .td-faq-syle .accordion .accordion-body {
    padding-inline-start: 1.25rem;
    padding-inline-end: 1.25rem;
  }
}
.td-faq-syle .accordion .accordion-body .description {
  font-size: 0.875rem;
  color: #999999;
}
.td-faq-syle .accordion .accordion-body .description strong {
  color: var(--td-heading);
}
.td-faq-syle .accordion .accordion-body .accordion-body-list {
  margin-top: 0.875rem;
}
.td-faq-syle .accordion .accordion-body .accordion-body-list ul li:not(:last-child) {
  margin-bottom: 0.4375rem;
}
.td-faq-syle .accordion .accordion-item {
  box-shadow: none;
  background: #0A1729;
  -webkit-border-radius: 1rem;
  -moz-border-radius: 1rem;
  -o-border-radius: 1rem;
  -ms-border-radius: 1rem;
  border-radius: 1rem;
  border: 0;
  z-index: 1;
  overflow: hidden;
}
.td-faq-syle .accordion .accordion-item .faq-shape-bg {
  position: absolute;
  top: -30px;
  left: 0;
  z-index: -1;
  opacity: 0;
}
.td-faq-syle .accordion .accordion-item:not(:last-of-type) {
  margin-bottom: 1.25rem;
}
.td-faq-syle .accordion .accordion-item:not(:first-of-type) {
  border-top: 0;
}
.td-faq-syle .accordion .accordion-item:last-of-type > .accordion-collapse {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.td-faq-syle .accordion .accordion-item:first-of-type .accordion-button {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.td-faq-syle .accordion .accordion-item.accordion-active {
  box-shadow: 0px 4px 11px rgba(166, 239, 103, 0.1);
  border: var(--bs-accordion-border-width) solid rgba(255, 255, 255, 0.16);
}
.td-faq-syle .accordion .accordion-item.accordion-active .accordion-button {
  color: var(--td-white);
}
.td-faq-syle .accordion .accordion-item.accordion-active:not(:first-of-type) {
  border-top: var(--bs-accordion-border-width) solid rgba(255, 255, 255, 0.16);
}
.td-faq-syle .accordion .accordion-item.accordion-active .faq-shape-bg {
  opacity: 1;
}
.td-faq-syle .accordion .accordion-header button {
  padding: 1.75rem 2.5rem 1.75rem 1.75rem;
  line-height: 1.5;
}
@media (max-width: 575px) {
  .td-faq-syle .accordion .accordion-header button {
    padding: 1.125rem 2.1875rem 1.125rem 1.125rem;
  }
}
.td-faq-syle.style-two .accordion .accordion-item {
  background: #181818;
}

/*----------------------------------------*/
/*  Date range picker
/*----------------------------------------*/
.drp-buttons .cancelBtn.btn.btn-sm.td-btn.btn-xs.danger-btn {
  padding: 0 16px;
  height: 30px;
  font-size: 12px;
  gap: 4px;
}
.drp-buttons .applyBtn.btn.btn-sm.td-btn.btn-xs.primary-btn {
  padding: 0 16px;
  height: 30px;
  font-size: 12px;
  gap: 4px;
}

.daterangepicker td.in-range {
  background-color: rgba(166, 239, 103, 0.2) !important;
  border-color: transparent;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: var(--td-primary);
  color: var(--td-heading);
}

/*----------------------------------------*/
/* Custom tooltip customize
/*----------------------------------------*/
.custom-tooltip .tooltip-inner {
  background-color: #10372F;
  font-weight: 500;
  font-size: 14px;
  max-width: 214px;
  text-align: left;
  padding: 10px;
  border-radius: 8px;
  color: #999999;
  border: 1px solid #104444;
}
.custom-tooltip .tooltip-arrow::before {
  border-bottom-color: #10372F !important;
  border-top-color: #10372F !important;
}

/*----------------------------------------*/
/*  Table styles
/*----------------------------------------*/
.table-container {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.06);
  padding: 16px 0px 16px;
  border-radius: 24px;
}
.table-container.has-eerie-black {
  background-color: var(--td-eerie-black);
}
.table-container .table-heading {
  margin-bottom: 20px;
  padding: 0 16px;
}
.table-container .table-heading .title {
  font-size: 20px;
}

.table-description {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}
.table-description .icon {
  width: 44px;
  height: 44px;
  background: rgba(3, 166, 109, 0.1);
  border-radius: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
}
.table-description .icon span {
  display: inline-flex;
  align-items: center;
}
.table-description .contents .title {
  font-size: 0.875rem;
}
.table-description .contents .date {
  font-size: 0.75rem;
}
.table-description.is-primary-10 .icon {
  background: rgba(166, 239, 103, 0.1);
  color: var(--td-danger);
}
.table-description.is-danger-10 .icon {
  background: rgba(233, 78, 91, 0.1);
  color: var(--td-danger);
}

.table-currency-info {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}
.table-currency-info .thumb {
  width: 20px;
}
.table-currency-info .name {
  font-size: 14px;
}

.td-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}
.td-table.recent-order-table {
  min-width: 900px;
}
.td-table.recent-table {
  min-width: 980px;
}
.td-table thead {
  background: rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  overflow: hidden;
}
.td-table thead tr th {
  border-inline-start: 0;
  border-inline-end: 0;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
}
.td-table th {
  text-align: left;
  padding: 14px 16px;
  font-weight: 600;
  color: #999999;
}
.td-table td {
  text-align: left;
  padding: 13px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  font-weight: 500;
}
.td-table tr:last-child td {
  border-bottom: none;
}
.td-table .no-data-found {
  text-align: center;
  padding: 3.75rem 2.5rem;
  margin: 0 auto;
}
.td-table .no-data-found img {
  width: 50px;
  margin-bottom: 5px;
}
.td-table .no-data-found span {
  font-size: 16px;
  letter-spacing: 0.03em;
  font-weight: 700;
  display: block;
}
.td-table.table-currency td {
  border-bottom: 0;
  padding: 10px 10px;
}
.td-table.table-currency td:last-child {
  text-align: end;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
.td-table.table-currency td:first-child {
  text-align: end;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}
.td-table.table-currency tr:hover {
  background: #142032;
}

.filter-bar {
  padding: 0 16px 16px;
  display: grid;
  gap: 16px;
  align-items: center;
  grid-template-columns: repeat(auto-fit, minmax(236px, 1fr));
}
.filter-bar .filter-bar-search {
  position: relative;
}
.filter-bar .filter-bar-search .input-box {
  background-color: transparent;
  padding: 0px 16px 0px 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 44px;
  padding-left: 40px;
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .input-box::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .input-box::-moz-placeholder {
  /* Firefox 19+ */
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .input-box:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .input-box:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .input-box::placeholder {
  /* MODERN BROWSER */
  color: #999;
  font-weight: 700;
  font-size: 14px;
}
.filter-bar .filter-bar-search .search-icon {
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-50%);
}
.filter-bar .td-form-group.has-right-icon .input-icon i {
  width: 18px;
  height: 18px;
}
.filter-bar .td-form-group .input-field .select2-container--default .select2-selection--single {
  height: 44px;
  line-height: 44px;
}
.filter-bar .td-form-group .input-field input {
  background-color: transparent;
  padding: 0px 16px 0px 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: 44px;
  color: #999;
  font-weight: 700;
  font-size: 14px;
}

.review-box {
  margin-top: 20px;
}
.review-box .review-btn {
  border: none;
  color: #fff;
  background: rgba(255, 255, 255, 0.04);
  border-radius: 12px;
  border-style: solid;
  border-color: transparent;
  border-width: 0px 0px 1px 0px;
  padding: 7px 20px 7px;
}
.review-box .review-table {
  width: 100%;
}
.review-box .review-table table {
  width: 100%;
  border-collapse: collapse;
}
.review-box .review-table th,
.review-box .review-table td {
  font-size: 0.875rem;
  padding: 0.75rem 0;
}
.review-box .review-table th {
  color: #999;
}
.review-box .review-table td {
  text-align: end;
}
.review-box .amount {
  color: var(--td-green);
}
.review-box .charge {
  color: var(--td-danger);
}
.review-box .method,
.review-box .rate {
  color: var(--td-white);
}
.review-box .email {
  color: #6780ff;
  font-weight: 700;
}
.review-box .email a {
  text-decoration: underline;
}
.review-box .email a:hover {
  text-decoration: none;
}
.review-box .total-box {
  margin-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 18px;
  display: flex;
  align-content: center;
  justify-content: space-between;
}
.review-box .total-box .total-text {
  font-size: 14px;
  display: block;
  font-weight: 700;
  margin-bottom: 0.25rem;
}
.review-box .buttons {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 1rem;
  margin-top: 2.5rem;
}

.table-bank-info {
  display: flex;
  align-items: center;
  gap: 12px;
}
.table-bank-info .thumb {
  width: 40px;
  height: 40px;
}
.table-bank-info .contents .designation {
  font-size: 14px;
  margin-bottom: 5px;
  display: block;
}
.table-bank-info .contents .title {
  font-size: 16px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.table-actions .icon {
  background: var(--td-primary);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  color: var(--td-white);
}
.table-actions .icon.is-green {
  background: var(--td-green);
}
.table-actions .icon.is-outline {
  border: 1px solid #999;
  background-color: transparent;
  color: #999;
}
.table-actions .text {
  background: var(--td-primary);
  border-radius: 8px;
  padding: 7px 16px 7px 16px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
  line-height: 1;
}
.table-actions .text.is-danger {
  background: #eb4e5c;
}
.table-actions .text.is-primary {
  color: var(--td-heading);
  background: var(--td-primary);
}

/*----------------------------------------*/
/* Progressbar styles
/*----------------------------------------*/
.ami-progress-bar {
  overflow: hidden;
}
.ami-progress-bar .progress {
  height: 12px;
  overflow: visible;
}
.ami-progress-bar .progress .progress-bar {
  background-color: #5EA7FD;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  position: relative;
}
.ami-progress-bar.lg-progress-bar .progress {
  height: 15px !important;
}
.ami-progress-bar.sm-progress-bar .progress {
  height: 5px !important;
}
.ami-progress-bar.multi-progress-bar .progress-bar {
  border-radius: 0;
}

.ami-progress-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--clr-text-label);
}
.ami-progress-title span {
  font-weight: 500;
  font-size: 14px;
  color: rgba(8, 8, 8, 0.7);
}

.ami-progress-head {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-bottom: 7px;
}

.ami-progress-percentage {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-heading);
  position: relative;
  padding-inline-start: 23px;
}
.ami-progress-percentage::after {
  position: absolute;
  content: "";
  width: 14px;
  height: 14px;
  background-color: #5EA7FD;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-radius: 50%;
}

.ami-progress-item:not(:last-child) {
  margin-bottom: 19px;
}
.ami-progress-item:nth-child(2) .progress .progress-bar {
  background-color: #3ECB5D;
}
.ami-progress-item:nth-child(2) .ami-progress-percentage::after {
  background-color: #3ECB5D;
}
.ami-progress-item:nth-child(3) .progress .progress-bar {
  background-color: #FD5E5E;
}
.ami-progress-item:nth-child(3) .ami-progress-percentage::after {
  background-color: #FD5E5E;
}
.ami-progress-item:nth-child(4) .progress .progress-bar {
  background-color: #ED6E3A;
}
.ami-progress-item:nth-child(4) .ami-progress-percentage::after {
  background-color: #ED6E3A;
}
.ami-progress-item:nth-child(5) .progress .progress-bar {
  background-color: #5E6FFD;
}
.ami-progress-item:nth-child(5) .ami-progress-percentage::after {
  background-color: #5E6FFD;
}

/*----------------------------------------*/
/*  Dropdown styles
/*----------------------------------------*/
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-list {
  display: none;
  position: absolute;
  top: 100%;
  inset-inline-end: 0;
  background-color: #1c2e27;
  padding: 5px 0;
  min-width: 130px;
  -webkit-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  z-index: 10;
}
.dropdown-list.show {
  display: block;
}

.dropdown-item {
  padding: 8px 16px;
  width: 100%;
  background: none;
  border: none;
  color: var(--td-white);
  text-align: left;
  cursor: pointer;
}
.dropdown-item:hover {
  background-color: #555;
}

.earnings-chard {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  padding: 30px 30px;
}

/*----------------------------------------*/
/*  Select2 customize
/*----------------------------------------*/
.select2-dropdown {
  background: #212A3D;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--td-primary) !important;
  color: var(--td-heading) !important;
}
.select2-container--default .select2-selection {
  background-color: transparent;
  border-color: #212a3d !important;
}
.select2-container--default .select2-selection:hover {
  border-color: #82808b;
}
.select2-container--default .select2-selection--single {
  height: 50px;
  line-height: 50px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 6px;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  height: 40px;
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.1);
  padding: 0 12px;
}

.select2-container--default.select2-container--focus .select2-selection {
  border-color: var(--td-primary) !important;
}

.select2-container--default.select2-container--open .select2-selection {
  border-color: var(--td-primary) !important;
}

.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: rgba(115, 103, 240, 0.16) !important;
  color: var(--td-primary) !important;
}

.select2-search__field {
  height: 40px;
}

.select2-container--default .select2-results__option {
  color: #999999;
  font-weight: 700;
  font-size: 14px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #999;
  font-weight: 700;
  font-size: 14px;
}

.defaults-select .select2-container--default.select2-container--focus .select2-selection {
  border-width: 0;
}
.defaults-select .select2-container--default .select2-selection {
  border: 0;
}
.defaults-select .select2-container--default .select2-selection--single {
  height: inherit;
}
.defaults-select .select2-dropdown {
  min-width: 200px;
}
.defaults-select .select2-results__options::-webkit-scrollbar {
  width: 5px;
}
.defaults-select .select2-results__options::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}
.defaults-select .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 0px;
}
.defaults-select .select2-container--default .select2-selection--single .select2-selection__arrow b {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="%232f2b3d" fill-opacity="0.9"/></svg>');
  background-size: 20px;
  right: 10px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  width: 20px !important;
  height: 100% !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  top: 0;
  right: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  position: absolute;
  background-repeat: no-repeat;
  background-size: 20px 19px;
  transform-origin: center;
  transition: transform 0.3s ease;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-right: 1.5px solid var(--td-text-primary);
  position: absolute;
  top: 45%;
  transform: translateY(-50%) rotate(45deg);
}

.select2-container--default.select2-container--above.select2-container--open .select2-selection__arrow b {
  transform: rotate(222deg) !important;
}

.select2-dropdown.select2-dropdown--above {
  box-shadow: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/
.alert-card {
  height: 54px;
  background: var(--td-white);
  border-radius: 40px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 30px;
}
.alert-card .alert-contents .description {
  color: var(--td-heading);
}

.alert-box {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--td-heading);
  color: var(--td-white);
  padding: 15px 25px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  transition: opacity 0.4s ease, transform 0.4s ease;
  position: fixed;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  bottom: 60px;
  z-index: 99;
}
.alert-box .alert-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 8px;
}
.alert-box .alert-icon {
  flex: 0 0 auto;
}
.alert-box .alert-message {
  margin: 0;
  color: var(--td-white);
}
.alert-box .close-btn {
  color: var(--td-white);
  font-size: 16px;
  border: none;
  border-radius: 50%;
  position: absolute;
  inset-inline-end: -4px;
  top: -7px;
  -webkit-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  -ms-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  -o-box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);
}
.alert-box .close-btn svg {
  width: 18px;
  height: 18px;
}

.alert-box.has-success {
  background-color: #3ECB5D;
}

.alert-box.has-warning {
  background-color: #FD7E14;
}

.alert-box.has-danger {
  background-color: var(--td-danger);
}

.alert-box.hidden {
  opacity: 0;
  transform: translate(-50%, 20px);
  pointer-events: none;
}

/*----------------------------------------*/
/*  Modal styles
/*----------------------------------------*/
.default-model .modal-dialog {
  max-width: 678px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .default-model .modal-dialog {
    max-width: 690px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .default-model .modal-dialog {
    max-width: 590px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .default-model .modal-dialog {
    max-width: 540px;
  }
}
@media (max-width: 575px) {
  .default-model .modal-dialog {
    max-width: inherit;
  }
}
.default-model.modal {
  background: rgba(0, 0, 0, 0.3);
}
.default-model .modal-content {
  background: #0d1c35;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
  padding: 30px 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .default-model .modal-content {
    padding: 36px 38px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .default-model .modal-content {
    padding: 30px 30px;
  }
}
@media (max-width: 575px) {
  .default-model .modal-content {
    padding: 24px 20px;
  }
}
.default-model .modal-content .modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0;
  padding-bottom: 12px;
}
.default-model .modal-content .modal-body {
  padding: 0;
}

.modal-btn-close {
  width: 50px;
  height: 50px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: start;
  justify-content: end;
  font-size: 20px;
  color: var(--td-danger);
  position: absolute;
  right: -10px;
  top: -10px;
}
.modal-btn-close i {
  width: 30px;
  height: 30px;
}

.cryptocurrency-purchase-modal {
  display: grid;
  grid-template-columns: 1fr 450px;
  column-gap: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .cryptocurrency-purchase-modal {
    grid-template-columns: 1fr 420px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .cryptocurrency-purchase-modal {
    grid-template-columns: 1fr;
  }
}

.cryptocurrency-modal .modal-dialog {
  max-width: 1085px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 960px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 690px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 490px;
  }
}
@media (max-width: 575px) {
  .cryptocurrency-modal .modal-dialog {
    max-width: inherit;
  }
}
.cryptocurrency-modal .modal-content {
  background-color: #0E1B2C;
  border-radius: 24px;
}
.cryptocurrency-modal .currency-contents {
  padding: 40px 40px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .cryptocurrency-modal .currency-contents {
    padding: 25px 25px;
  }
}
@media (max-width: 575px) {
  .cryptocurrency-modal .currency-contents {
    padding: 20px 20px;
  }
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper {
  display: flex;
  align-items: center;
  row-gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
  position: relative;
  padding-right: 40px;
  margin-right: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
    padding-right: 25px;
    margin-right: 25px;
  }
}
@media (max-width: 480px) {
  .cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
    padding-right: 15px;
    margin-right: 15px;
  }
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info::before {
  position: absolute;
  content: "";
  height: 26px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  top: 50%;
  transform: translateY(-50%);
  right: 0;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info .status {
  font-size: 13px;
  font-weight: 500;
  display: block;
  margin-bottom: 0.25rem;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info .status-title {
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents {
  margin-top: 45px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .heading {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
  margin-bottom: 18px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .heading h5 {
  font-size: 16px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul {
  list-style-type: disc;
  padding-left: 16px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul li {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul li:not(:last-child) {
  margin-bottom: 16px;
}
.cryptocurrency-modal .currency-forms {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px 40px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .cryptocurrency-modal .currency-forms {
    border-left: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .cryptocurrency-modal .currency-forms {
    padding: 25px 25px;
  }
}
@media (max-width: 575px) {
  .cryptocurrency-modal .currency-forms {
    padding: 20px 20px;
  }
}
.cryptocurrency-modal .currency-forms .forms-grid {
  display: grid;
  gap: 18px;
}
.cryptocurrency-modal .currency-forms .forms-grid .price {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 8px;
  padding: 5px 16px 5px 16px;
  display: inline-flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 16px;
  letter-spacing: 0.03em;
  font-weight: 700;
  color: var(--td-green);
  width: max-content;
}
.cryptocurrency-modal .currency-forms .forms-grid .price span {
  color: var(--td-white);
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box {
  border-radius: 16px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.1);
  border-width: 1px;
  padding: 12px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box .contents {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box .contents span {
  font-size: 20px;
  letter-spacing: 0.03em;
  font-weight: 700;
  color: var(--td-white);
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box .contents .balance {
  font-size: 14px;
  font-weight: 500;
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box .icon {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  padding-left: 10px;
}
.cryptocurrency-modal .currency-forms .forms-grid .input-box .icon img {
  width: 18px;
}
.cryptocurrency-modal .currency-forms .forms-grid .payment-method .td-form-group .input-field .select2-container--default .select2-selection {
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.cryptocurrency-modal .currency-forms .forms-grid .processing-fee {
  color: #999999;
  text-align: center;
  font-size: 14px;
  letter-spacing: 0.03em;
  font-weight: 500;
}
.cryptocurrency-modal .currency-forms .buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-top: 1.5625rem;
}
.cryptocurrency-modal .currency-forms .buttons .payment-btn {
  font-size: 14px;
  line-height: 18px;
  font-weight: 700;
  padding: 0px 24px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
.cryptocurrency-modal .currency-forms .buttons .payment-btn.buy {
  background-color: var(--td-green);
  color: var(--td-white);
}
.cryptocurrency-modal .currency-forms .buttons .payment-btn.sell {
  background-color: #EB4E5C;
  color: var(--td-white);
}
.cryptocurrency-modal .currency-forms .buttons .payment-btn.cancel {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.16);
  color: var(--td-white);
}
.cryptocurrency-modal.is-sell .currency-forms .forms-grid .price {
  color: var(--td-danger);
}

/*----------------------------------------*/
/* Breadcrumb styles
/*----------------------------------------*/
.td-breadcrumb-area {
  background-color: #091628;
  padding: 30px 0;
}
.td-breadcrumb-area .breadcrumb-contents .breadcrumb-title {
  font-size: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-breadcrumb-area .breadcrumb-contents .breadcrumb-title {
    font-size: 26px;
  }
}
@media (max-width: 480px) {
  .td-breadcrumb-area .breadcrumb-contents .breadcrumb-title {
    font-size: 24px;
  }
}

/*----------------------------------------*/
/* Badge styles
/*----------------------------------------*/
.td-badge {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 7px 15px;
  line-height: 1;
  font-size: 14px;
  background: rgba(94, 167, 253, 0.2);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  column-gap: 6px;
}
.td-badge.badge-success {
  background: rgba(3, 166, 109, 0.08);
  color: #03a66d;
}
.td-badge.badge-warning {
  background: rgba(255, 183, 3, 0.06);
  color: #FFB703;
}
.td-badge.badge-danger {
  background: rgba(233, 78, 91, 0.08);
  color: #e94e5b;
}
.td-badge.badge-green {
  background-color: rgba(3, 166, 109, 0.1);
  color: var(--td-green);
}
.td-badge.badge-primary {
  background-color: rgba(166, 239, 103, 0.1);
  color: var(--td-primary);
}
.td-badge.fill-badge-success {
  background-color: var(--td-green);
  color: var(--td-white);
}
.td-badge.fill-badge-warning {
  background: var(--td-warning);
  color: var(--td-white);
}
.td-badge.fill-badge-danger {
  background: var(--td-danger);
  color: var(--td-white);
}
.td-badge.fill-badge-green {
  background-color: var(--td-green);
  color: var(--td-white);
}
.td-badge.fill-badge-primary {
  background-color: var(--td-primary);
  color: var(--td-white);
}

.sell-badge {
  display: inline-block;
  background: rgba(235, 78, 92, 0.1);
  border-radius: 5px;
  border: 1px solid rgba(235, 78, 92, 0.4);
  padding: 5px 10px 5px 10px;
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
  line-height: 1;
}

/*----------------------------------------*/
/* Buttons styles
/*----------------------------------------*/
.btn-wrap {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 15px 15px;
  flex-wrap: wrap;
}

.td-btn {
  padding: 0 26px;
  background: var(--td-primary);
  position: relative;
  z-index: 1;
  transition: all 0.4s ease-in-out;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  color: var(--td-heading);
  font-size: 16px;
  font-weight: 600;
  gap: 8px;
}
.td-btn .btn-icon {
  display: flex;
  align-items: center;
}
.td-btn .btn-icon i {
  transition: 0.3s;
}
.td-btn:focus {
  color: var(--td-heading);
}
.td-btn:hover {
  background-color: #509415;
  color: var(--td-heading);
  transform: translate3d(0, -2px, 0);
}
.td-btn.btn-primary-outline {
  border: 2px solid rgba(166, 239, 103, 0.4);
  background-color: transparent;
  color: var(--td-white);
}
.td-btn.btn-primary-outline:hover {
  background-color: var(--td-heading);
  color: var(--td-white);
}
.td-btn.outline-white-btn {
  background: var(--td-white);
  border: 1px solid #DBDBDB;
  color: var(--td-heading);
  gap: 8px;
}
.td-btn.outline-white-btn span svg {
  width: 20px;
  height: 20px;
}
.td-btn.outline-white-btn:hover {
  background-color: var(--td-heading);
  border-color: var(--td-heading);
  color: var(--td-white);
}
.td-btn.outline-danger-btn {
  border: 1px solid var(--td-danger);
  background-color: transparent;
  color: var(--td-danger);
  font-weight: 500;
}
.td-btn.outline-black-btn {
  border: 1px solid rgba(8, 8, 8, 0.6);
  color: rgba(8, 8, 8, 0.6);
  background-color: transparent;
}
.td-btn.outline-black-fill-btn {
  background: #010c1a;
  border: 1px solid rgba(255, 255, 255, 0.16);
  color: var(--td-white);
}
.td-btn.outline-auth-btn {
  border: 1px solid #212a3d;
  color: rgba(255, 255, 255, 0.6);
  background-color: transparent;
}
.td-btn.outline-auth-btn:hover {
  background-color: var(--td-secondary);
  color: var(--td-white);
  border-color: var(--td-secondary);
}
.td-btn.btn-outline-dark-gunmetal {
  background-color: #142032;
  color: var(--td-primary);
}
.td-btn.btn-outline-white-10 {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: transparent;
  color: var(--td-text-primary);
}
.td-btn.btn-primary {
  background: var(--td-primary-alt);
  color: var(--td-white);
}
.td-btn.btn-primary span svg * {
  width: 16px;
  height: 16px;
  stroke: var(--td-white);
}
.td-btn.btn-primary:hover {
  background: var(--td-primary);
}
.td-btn.danger-btn {
  background: var(--td-danger);
  color: var(--td-white);
}
.td-btn.danger-btn span svg * {
  stroke: var(--td-white);
}
.td-btn.btn-white {
  background-color: var(--td-white);
  color: var(--td-heading);
}
.td-btn.btn-white:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
}
.td-btn.btn-gray {
  background: var(--td-alice-blue);
  border: 1px solid rgba(171, 178, 225, 0.3);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  color: var(--td-heading);
}
.td-btn.btn-gray .td-btn.btn-gray {
  background: rgba(255, 255, 255, 0.2);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.3);
  color: var(--td-white);
}
.td-btn.btn-dark {
  background: #010c1a;
  border: 1px solid rgba(255, 255, 255, 0.16);
  color: var(--td-white);
}
.td-btn.btn-secondary {
  background: linear-gradient(180deg, #73A8F8 0%, #3B57E7 100%);
  color: var(--td-white);
}
.td-btn.btn-secondary:hover {
  background: linear-gradient(180deg, #73A8F8 0%, #3B57E7 100%);
  color: var(--td-white);
}
.td-btn.has-underline {
  background-color: transparent;
  height: auto;
  padding: 0;
}
.td-btn.has-underline .btn-text {
  color: var(--td-heading);
}
.td-btn.has-underline .btn-text::after {
  position: absolute;
  content: "";
  inset-inline-start: auto;
  bottom: -2px;
  background: currentColor;
  width: 0;
  height: 2px;
  transition: 0.3s;
  inset-inline-end: 0;
}
.td-btn.has-underline .btn-icon {
  width: 22px;
  height: 22px;
  background-color: var(--td-primary);
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.td-btn.has-underline .btn-icon svg * {
  stroke: var(--td-white);
}
.td-btn.has-underline:hover .btn-text::after {
  width: 100%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}
.td-btn.btn-xs {
  padding: 0 16px;
  height: 30px;
  font-size: 12px;
  gap: 4px;
}
.td-btn.btn-xs .btn-icon i {
  width: 14px;
  height: 14px;
}
.td-btn.btn-h-40 {
  height: 40px;
  padding: 0 15px;
  font-weight: 500;
  font-size: 14px;
  gap: 4px;
}
.td-btn.btn-h-36 {
  height: 36px;
  padding: 0 15px;
  font-weight: 500;
  font-size: 14px;
  gap: 4px;
}
.td-btn.btn-sm {
  padding: 0 22px;
  height: 44px;
  gap: 6px;
  font-size: 14px;
}
.td-btn.btn-sm svg {
  width: 16px;
  height: 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-btn.btn-sm {
    padding: 0 12px;
    height: 36px;
    gap: 6px;
    font-size: 14px;
    gap: 2px;
  }
}
.td-btn.btn-md {
  padding: 0 25px;
  font-size: var(--font-size-b3);
  height: 50px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-btn.btn-md {
    padding: 0 15px;
  }
}
.td-btn.btn-lg {
  height: 70px;
  padding: 0 35px;
  font-size: 18px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-btn.btn-lg {
    padding: 0 17px;
  }
}
.td-btn.btn-xl {
  padding: 0 45px;
  font-size: 20px;
  height: 75px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-btn.btn-xl {
    padding: 0 20px;
    font-size: 16px;
    height: 55px;
  }
}
.td-btn.btn-xxl {
  padding: 0 60px;
  font-size: 22px;
  height: 100px;
  line-height: 100px;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-btn.btn-xxl {
    padding: 0 20px;
    font-size: 18px;
    height: 70px;
  }
}

.td-underline-btn {
  font-weight: 500;
  position: relative;
  color: var(--td-primary);
  font-size: 14px;
}
.td-underline-btn::after {
  content: "";
  position: absolute;
  height: 1px;
  transition: 0.3s;
  left: auto;
  bottom: -2px;
  background: var(--td-primary);
  width: 0;
  right: 0;
}
.td-underline-btn:hover {
  color: var(--td-primary);
}
.td-underline-btn:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}

.chat-button {
  background: #1d2939;
  border-radius: 6px;
  padding: 0px 6px 0px 6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.chat-button i {
  width: 14px;
  height: 14px;
}

/*----------------------------------------*/
/*  Offcanvas styles
/*----------------------------------------*/
.offcanvas-area {
  background: #171C35 none repeat scroll 0 0;
  position: fixed;
  right: 0;
  top: 0;
  width: 360px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
@media (max-width: 480px) {
  .offcanvas-area {
    width: 300px;
  }
}
.offcanvas-area ::-webkit-scrollbar {
  display: none;
}
.offcanvas-area.info-open {
  opacity: 1;
  transform: translateX(0);
}

.offcanvas-logo a img {
  height: 26px;
}

.offcanvas-content {
  padding-bottom: 45px;
}

.offcanva-wrapper {
  position: relative;
  height: 100%;
  padding: 28px 28px;
}

.offcanvas-top {
  margin-bottom: 25px;
}

.offcanvas-title {
  color: var(--td-white);
  font-size: 20px;
  margin-bottom: 20px;
}
@media (max-width: 480px) {
  .offcanvas-title {
    font-size: 20px;
  }
}

.offcanvas-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #000000;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  inset-inline-end: 0;
  transition: 0.3s;
}
.offcanvas-overlay.overlay-open {
  opacity: 0.6;
  visibility: visible;
}

.sidebar-toggle {
  cursor: pointer;
}

.offcanvas-contact-icon {
  margin-inline-end: 15px;
}

.offcanvas-btn {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 15px;
}

.offcanvas-close-icon {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  height: 40px;
  width: 40px;
  line-height: 40px;
  border: 2px solid rgba(255, 250, 250, 0.1);
  background-color: var(--td-primary);
  border-radius: 50%;
}
.offcanvas-close-icon svg * {
  color: var(--td-heading);
}
.offcanvas-close-icon:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}

/*----------------------------------------*/
/* Section Title  
/*----------------------------------------*/
.section-subtitle {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 100px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--td-white);
  padding: 7px 18px 7px 18px;
  display: inline-flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4.5px);
  z-index: 1;
  font-size: 14px;
}
.section-subtitle::before {
  position: absolute;
  content: "";
  background: #a6ef67;
  border-radius: 50%;
  opacity: 0.5;
  width: 151px;
  height: 94px;
  left: 50%;
  transform: translateX(-50%);
  top: 31px;
  filter: blur(3px);
  z-index: -1;
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .section-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .section-title {
    font-size: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .section-title {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  .section-title {
    font-size: 24px;
  }
}

.section-subtitle-two {
  color: var(--td-primary);
  display: inline-flex;
  gap: 6px;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4.5px);
  z-index: 1;
  font-size: 14px;
  text-transform: uppercase;
}

/*----------------------------------------*/
/* Tab customize
/*----------------------------------------*/
.td-tab .nav-tabs {
  padding: 0;
  margin: 0;
  border: 0;
}
.td-tab .nav-tabs .nav-link {
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
}

/*----------------------------------------*/
/*  Basic pagination styles
/*----------------------------------------*/
.td-pagination ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-pagination ul {
    justify-content: start;
  }
}
.td-pagination ul li {
  list-style: none;
}
.td-pagination ul li a {
  width: 32px;
  height: 32px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-size: 14px;
  line-height: 20px;
  font-weight: 900;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--td-text-primary);
}
.td-pagination ul li a:hover {
  background: #0e1b2c;
  color: var(--td-white);
}
.td-pagination ul li a i {
  font-size: 12px;
}
.td-pagination ul li .current {
  width: 32px;
  height: 32px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-weight: 500;
  font-size: 16px;
  background: #0e1b2c;
  color: var(--td-white);
}
.td-pagination ul li .disabled {
  background: #0a0e17;
  color: #1e2530;
}
.td-pagination ul li .disabled:hover {
  background: #0a0e17;
  color: #1e2530;
}

/*----------------------------------------*/
/* Back to top 
/*----------------------------------------*/
.back-to-top-wrap {
  position: fixed;
  bottom: 30px;
  inset-inline-end: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  background: var(--td-primary);
}
@media (max-width: 575px) {
  .back-to-top-wrap {
    height: 40px;
    width: 40px;
  }
}
@media (max-width: 480px) {
  .back-to-top-wrap {
    bottom: 20px;
    inset-inline-end: 20px;
  }
}
.back-to-top-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
}
.back-to-top-wrap::after {
  position: absolute;
  font-family: var(--td-ff-fontawesome);
  content: "\f062";
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: var(--td-black);
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}
@media (max-width: 575px) {
  .back-to-top-wrap::after {
    font-size: 14px;
  }
}
.back-to-top-wrap svg path {
  fill: none;
}
.back-to-top-wrap svg.backtotop-circle path {
  stroke: #ccc;
  stroke-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

/*----------------------------------------*/
/*  Forms styles
/*----------------------------------------*/
input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
textarea {
  outline: none;
  height: 50px;
  width: 100%;
  padding: 0 20px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  background: #091628;
  border: 1px solid #212a3d;
  color: var(--td-white);
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--td-primary);
}

textarea {
  padding: 14px 24px;
}
textarea:focus {
  border-color: var(--td-heading);
}

.custom-checkbox input {
  opacity: 0;
  position: absolute;
}
.custom-checkbox input + label {
  position: relative;
  font-size: 15px;
  line-height: 25px;
  color: var(--td-text-primary);
  font-weight: 400;
  padding-inline-start: 20px;
  cursor: pointer;
  margin-bottom: 0;
}
.custom-checkbox input + label::before {
  content: " ";
  position: absolute;
  top: 6px;
  inset-inline-start: 0;
  width: 14px;
  height: 14px;
  background-color: var(--td-white);
  border: 1px solid var(--td-border-primary);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transition: all 0.3s;
}
.custom-checkbox input + label::after {
  content: " ";
  position: absolute;
  top: 9px;
  inset-inline-start: 2px;
  width: 10px;
  height: 5px;
  background-color: transparent;
  border-bottom: 1px solid var(--td-white);
  border-inline-start: 1px solid var(--td-white);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
.custom-checkbox input:checked + label::before {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}
.custom-checkbox input:checked + label::after {
  opacity: 1;
}

.custom-radio input {
  opacity: 0;
  position: absolute;
}
.custom-radio input + label {
  position: relative;
  line-height: 25px;
  color: var(--td-text-primary);
  padding-inline-start: 22px;
  cursor: pointer;
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 500;
}
.custom-radio input + label::before {
  content: " ";
  position: absolute;
  top: 5px;
  inset-inline-start: 0;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: 1px solid #999999;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transition: all 0.3s;
}
.custom-radio input + label::after {
  content: " ";
  position: absolute;
  top: 8px;
  inset-inline-start: 2px;
  width: 10px;
  height: 5px;
  background-color: transparent;
  border-bottom: 1px solid var(--td-white);
  border-inline-start: 1px solid var(--td-white);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
.custom-radio input:checked + label {
  color: var(--td-primary);
}
.custom-radio input:checked + label::before {
  border-color: var(--td-primary);
}
.custom-radio input:checked + label::after {
  opacity: 1;
}
.custom-radio input + label::before {
  border-radius: 50%;
}
.custom-radio input + label::after {
  width: 10px;
  height: 10px;
  inset-inline-start: 3px;
  background: var(--td-primary);
  border-radius: 50%;
  border-color: var(--td-primary);
}

.form-switch {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}
.form-switch input[type=checkbox] {
  opacity: 1;
  position: relative;
  margin-inline-start: 0 !important;
  margin-top: 0;
  outline: none;
  margin-bottom: 0;
}
.form-switch input[type=checkbox]:checked {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}
.form-switch input[type=checkbox]:focus {
  outline: 0;
  box-shadow: none;
}
.form-switch input[type=checkbox] ~ label {
  padding-inline-start: 10px;
}
.form-switch input[type=checkbox] ~ label::before, .form-switch input[type=checkbox] ~ label::after {
  display: none;
}

.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx::before {
  display: none;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span a {
  color: var(--td-primary);
}
.animate-custom .cbx span a:hover {
  color: #000000;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: var(--td-heading);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-primary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inline-start: 5px;
  color: var(--td-text-primary);
  font-weight: 500;
  font-size: 14px;
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: var(--td-primary);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-primary);
  background: var(--td-primary);
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.was-not-validated .td-form-group .input-field {
  position: relative;
}
.was-not-validated .td-form-group .input-field input {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}
.was-not-validated .td-form-group .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
}
.was-not-validated .td-form-group .input-field .input-group-text {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}

[dir=rtl] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.td-form-group.has-currency {
  position: relative;
}
.td-form-group.has-currency .input-field input {
  border-radius: 4px;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  padding: 10px 1px 10px 16px;
  height: 40px;
  position: relative;
  background-color: rgba(0, 0, 0, 0);
  color: hsla(0, 0%, 100%, 0.6);
  text-align: left;
  font-family: "Satoshi-Medium", sans-serif;
  line-height: 20px;
  padding-right: 80px;
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:focus {
  border: 1px solid var(--td-primary);
}
.td-form-group.has-currency .input-field input::-webkit-inner-spin-button, .td-form-group.has-currency .input-field input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
.td-form-group.has-currency .input-field input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input::placeholder {
  /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field .lable-currency {
  position: absolute;
  right: 10px;
  top: 5px;
}
.td-form-group.has-currency .input-field .lable-currency {
  position: absolute;
  right: 42px;
  top: 50%;
  font-size: 12px;
  font-weight: 500;
  transform: translateY(-50%);
}
.td-form-group.has-currency .numeric-controls-panel {
  display: flex;
  flex-direction: column;
  width: 32px;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  border-left: 1px solid hsla(0, 0%, 100%, 0.1);
}
.td-form-group.has-currency .numeric-controls-panel button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50%;
}
.td-form-group.has-currency .numeric-controls-panel button:last-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.td-form-group.has-right-icon .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
}
.td-form-group.has-right-icon .input-icon i {
  font-size: 18px;
  width: 20px;
  height: 20px;
}
.td-form-group.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
  inset-inline-end: 20px !important;
  inset-inline-start: auto !important;
  line-height: 1;
}
.td-form-group.has-right-icon .input-icon.icon-selected svg * {
  stroke: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  fill: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  stroke-opacity: 1;
  /* Full opacity */
  transition: all 0.3s ease;
  /* Smooth animation */
}
.td-form-group.has-right-payment-method .form-control {
  padding-right: 75px;
}
.td-form-group.has-right-payment-method .input-payment-method-img {
  width: 50px;
  height: 25px;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.04);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.td-form-group.selected_icon .input-icon {
  inset-inline-end: 33px;
  cursor: pointer;
}
.td-form-group.has-left-icon .form-control {
  padding-inline-start: 42px;
}
.td-form-group.has-left-icon .input-icon {
  position: absolute;
  inset-inline-start: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  width: max-content;
}
.td-form-group.has-left-icon .input-icon.eyeicon {
  cursor: pointer;
}
.td-form-group .input-field {
  position: relative;
  width: 100%;
}
.td-form-group .input-field.input-group {
  flex-wrap: nowrap;
}
.td-form-group .input-field .input-group-text {
  background: transparent;
  mix-blend-mode: normal;
  border: 1px solid #212a3d;
  color: var(--td-text-primary);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
}
[dir=rtl] .td-form-group .input-field .input-group-text {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}
.td-form-group .input-field.disabled input,
.td-form-group .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.td-form-group .input-field.disabled input:focus,
.td-form-group .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.td-form-group .input-field input,
.td-form-group .input-field textarea {
  font-size: 14px;
}
.td-form-group .input-field input::-webkit-input-placeholder,
.td-form-group .input-field textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::-moz-placeholder,
.td-form-group .input-field textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-moz-placeholder,
.td-form-group .input-field textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-ms-input-placeholder,
.td-form-group .input-field textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::placeholder,
.td-form-group .input-field textarea::placeholder {
  /* MODERN BROWSER */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field textarea {
  padding: 12px 15px;
  height: 150px;
  resize: none;
  line-height: 1.5;
}
.td-form-group .input-field textarea:focus {
  border-color: var(--td-primary);
}
.td-form-group .input-field.height-large textarea {
  height: 237px;
}
.td-form-group .input-field .form-control {
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid #212a3d;
  font-size: 14px;
  background-color: transparent;
  box-shadow: none;
}
.td-form-group .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .input-field .form-control:focus {
  border-color: var(--td-primary);
}
.td-form-group .input-field .form-control:disabled {
  background-color: rgba(0, 0, 0, 0.25);
  color: rgba(8, 8, 8, 0.7);
}
.td-form-group .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.td-form-group .input-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
  font-weight: 700;
  margin-bottom: 0.5em;
}
.td-form-group .input-label span {
  padding-inline-start: 1px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: var(--td-danger);
}
.td-form-group .input-label-inner {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-form-group .input-label-inner > p {
  font-size: 12px;
}
.td-form-group .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.td-form-group .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.td-form-group .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.td-form-group .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  padding: 0 10px;
}
.td-form-group .input-select .nice-select .option.selected {
  font-weight: 500;
}
.td-form-group .input-select .nice-select .option:hover {
  background-color: #353535;
}
.td-form-group .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.td-form-group .input-select .nice-select.open, .td-form-group .input-select .nice-select:focus {
  background-color: #353535;
}
.td-form-group.input-fill .input-label {
  font-weight: 700;
}
.td-form-group.input-fill .form-select {
  padding: 0 15px;
}
.td-form-group.input-fill input,
.td-form-group.input-fill select,
.td-form-group.input-fill textarea {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input::-webkit-input-placeholder,
.td-form-group.input-fill select::-webkit-input-placeholder,
.td-form-group.input-fill textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input::-moz-placeholder,
.td-form-group.input-fill select::-moz-placeholder,
.td-form-group.input-fill textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input:-moz-placeholder,
.td-form-group.input-fill select:-moz-placeholder,
.td-form-group.input-fill textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input:-ms-input-placeholder,
.td-form-group.input-fill select:-ms-input-placeholder,
.td-form-group.input-fill textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input::placeholder,
.td-form-group.input-fill select::placeholder,
.td-form-group.input-fill textarea::placeholder {
  /* MODERN BROWSER */
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}
.td-form-group.input-fill input:focus,
.td-form-group.input-fill select:focus,
.td-form-group.input-fill textarea:focus {
  border-color: var(--td-primary);
}
.td-form-group .form-select {
  height: 50px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  font-size: 14px;
}
[dir=rtl] .td-form-group .form-select {
  background-position: left 0.75rem center;
}
.td-form-group .form-select:focus {
  font-size: 14px;
}
.td-form-group.has-multiple .input-field-inner {
  display: flex;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control {
  border: 0;
  height: 44px;
}
.td-form-group.has-multiple .input-field-inner .input-field .select2-container--default .select2-selection {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-radius: 0.375rem;
}
.td-form-group.has-multiple .input-field-inner .input-field .select2-container--default .select2-selection--single {
  height: 44px;
  line-height: 44px;
}
.td-form-group.has-multiple .input-field-inner .input-field:last-child {
  position: relative;
}
.td-form-group.has-multiple .input-field-inner .input-field:last-child::before {
  position: absolute;
  content: "";
  height: calc(100% - 20px);
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
.td-form-group .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px 10px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
.td-form-group .otp-verification .control-form {
  background: hsla(0, 0%, 100%, 0.04);
  height: 77px;
  width: 70px;
  text-align: center;
  font-size: 20px;
  line-height: 24px;
  font-weight: 700;
  border-radius: 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-form-group .otp-verification .control-form {
    height: 67px;
    width: 60px;
  }
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: var(--td-danger);
  display: none;
  font-weight: 500;
}

.input-attention {
  font-size: 12px;
  font-weight: 500;
}
.input-attention.xs {
  font-size: 10px;
}

.attachment-previews {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.attachment-previews .preview {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 10px;
  isolation: isolate;
  width: 105px;
  height: 105px;
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid var(--td-border-primary);
  backdrop-filter: blur(10px);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  flex: none;
  order: 0;
  padding: 5px;
}
.attachment-previews .preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}
.attachment-previews .preview span {
  font-size: 10px;
  word-wrap: break-word;
  position: absolute;
  bottom: 6px;
  inset-inline-start: 5px;
  width: calc(100% - 12px);
  color: var(--td-white);
  text-overflow: ellipsis;
  overflow: hidden;
  height: 1.2em;
  white-space: nowrap;
}
.attachment-previews .preview .remove {
  position: absolute;
  top: 5px;
  inset-inline-end: 5px;
  background: var(--td-danger);
  color: var(--td-white);
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.attachment-previews .preview .remove svg {
  background: var(--td-danger);
  fill: var(--td-white);
}

.attachment-actions .add-attachment {
  height: 120px;
  border: 1px solid var(--td-border-primary);
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  background-color: #FCFCFC;
  cursor: pointer;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

/*----------------------------------------
	Image Preview 
-----------------------------------------*/
.file-upload-wrap .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.file-upload-wrap .input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #080808;
}

.upload-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 155px;
  text-align: center;
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}
.upload-custom-file input[type=file] {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 2px;
  height: 2px;
  overflow: hidden;
  opacity: 0;
}
.upload-custom-file label {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  inset-inline-end: 0;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.4s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  -ms-transition: -ms-transform 0.4s;
  -o-transition: -o-transform 0.4s;
  transition: transform 0.4s;
  background-color: rgba(255, 255, 255, 0.01);
}
.upload-custom-file label span {
  display: block;
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
.upload-custom-file label span b {
  color: var(--td-text-primary);
  font-weight: 500;
}
.upload-custom-file label .type-file-text {
  margin-top: 5px;
  color: #E94E5B;
}
.upload-custom-file label .upload-icon {
  width: 40px;
  margin: 0 auto;
  margin-bottom: 15px;
}
.upload-custom-file label.file-ok {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
.upload-custom-file label.file-ok span {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 0.3rem;
  color: #ffffff;
  background-color: rgba(166, 239, 103, 0.1);
  font-weight: 500;
  font-size: 16px;
  margin: auto;
  text-decoration: none;
}
.upload-custom-file label.file-ok .upload-icon {
  display: none;
}
.upload-custom-file.without-image {
  height: 167px;
}
.upload-custom-file.without-image label {
  background-color: var(--td-text-primary);
}

.upload-thumb-close {
  position: absolute;
  inset-inline-end: 10px;
  top: 35px;
  z-index: 5;
  color: #E94E5B;
  display: none;
}

.file-upload-close {
  position: absolute;
  top: 10px;
  inset-inline-end: 10px;
  color: #F34141;
  font-size: 20px;
  z-index: 55;
}

/*----------------------------------------*/
/*  Animations styles
/*----------------------------------------*/
@keyframes popupBtn {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.4);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes sticky {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}
@keyframes tdSpinner {
  from {
    -webkit-transform: rotate(0turn);
    transform: rotate(0turn);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes banner-shape-anim {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes banner-shape-anim-2 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
@keyframes banner-shape-anim-3 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-40px);
  }
}
.upDown {
  animation: upDown 1s infinite alternate;
}

@keyframes upDown {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
@keyframes animationglob {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes icon-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }
  40% {
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  60% {
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}
@keyframes animation_scale {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.7);
  }
}
@keyframes iconltr {
  49% {
    transform: translateX(30%);
  }
  50% {
    opacity: 0;
    transform: translateX(-30%);
  }
  51% {
    opacity: 1;
  }
}
@keyframes tada {
  0% {
    transform: scaleX(1);
  }
  10% {
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
  }
  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
  }
  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  to {
    transform: scaleX(1);
  }
}
.tada {
  animation-name: tada;
}

/*----------------------------------------*/
/*  Shortcodes styles
/*----------------------------------------*/
.radius-4 {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}

.radius-6 {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.radius-8 {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}

.radius-10 {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
}

.radius-20 {
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
}

.radius-30 {
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}

.radius-40 {
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -o-border-radius: 40px;
  -ms-border-radius: 40px;
  border-radius: 40px;
}

.radius-45 {
  -webkit-border-radius: 45px;
  -moz-border-radius: 45px;
  -o-border-radius: 45px;
  -ms-border-radius: 45px;
  border-radius: 45px;
}

.radius-50 {
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -o-border-radius: 50px;
  -ms-border-radius: 50px;
  border-radius: 50px;
}

.radius-60 {
  -webkit-border-radius: 60px;
  -moz-border-radius: 60px;
  -o-border-radius: 60px;
  -ms-border-radius: 60px;
  border-radius: 60px;
}

.title-font {
  font-family: var(--td-ff-title);
}

.fs-12 {
  font-size: 12px;
}

.fs-14 {
  font-size: 12px;
}

.fs-16 {
  font-size: 16px;
}

.fw-3 {
  font-weight: var(--td-fw-light);
}

.fw-4 {
  font-weight: var(--td-fw-regular);
}

.fw-5 {
  font-weight: var(--td-fw-medium);
}

.fw-6 {
  font-weight: var(--td-fw-sbold);
}

.fw-7 {
  font-weight: var(--td-fw-bold);
}

.fw-8 {
  font-weight: var(--td-fw-ebold);
}

.fw-9 {
  font-weight: var(--td-fw-black);
}

.gap--5 {
  gap: 5px;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.gap-24 {
  gap: 24px;
}

.hide {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.font-xxs {
  font-size: 14px;
}

/*----------------------------------------*/
/*  Background Css
/*----------------------------------------*/
.white-bg {
  background-color: var(--td-white);
}

.black-bg {
  background-color: var(--td-black);
}

.primary-bg {
  background-color: var(--td-bg-primary);
}

.chinese-black-bg {
  background-color: var(--td-chinese-black);
}

.kuwanomi-purple-bg {
  background-color: var(--td-kuwanomi-purple);
}

.chaos-black-bg {
  background-color: var(--td-chaos-black);
}

.liberty-blue-bg {
  background-color: var(--td-liberty-blue);
}

.green-bg-16 {
  background-color: rgba(3, 166, 109, 0.16);
}

/*----------------------------------------*/
/*  Scrollbar styles
/*----------------------------------------*/
[data-simplebar] {
  position: unset;
}

.simplebar-track {
  inset-inline-end: -2px;
}
[dir=rtl] .simplebar-track {
  right: auto;
  left: -2px;
}
.simplebar-track.simplebar-vertical {
  top: 100px;
  width: 10px;
}
.simplebar-track.simplebar-horizontal {
  visibility: hidden !important;
}

.simplebar-scrollbar:before {
  background: rgba(99, 98, 231, 0.2);
}

[data-simplebar] {
  position: unset;
}

.simplebar-mask {
  top: 80px;
}

.main-sidebar {
  height: calc(100vh - 70px);
  -webkit-transition: color 1s ease;
  transition: color 1s ease;
  overflow: auto;
  margin-top: 20px;
  margin-bottom: 50px;
}

.simplebar-offset {
  height: calc(100vh - 70px);
}

/*----------------------------------------*/
/* Preloader styles
/*----------------------------------------*/
.preloader {
  background: var(--td-white);
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999999;
  top: 0;
  inset-inline-start: 0;
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  place-items: center;
}
.dark-theme .preloader {
  background: var(--td-heading);
}
.preloader .spinner {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 9px solid var(--td-primary);
  animation: spinner-bulqg1 0.8s infinite linear alternate, spinner-oaa3wk 1.6s infinite linear;
}
@keyframes spinner-bulqg1 {
  0% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%);
  }
  12.5% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%);
  }
  25% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%);
  }
  50% {
    clip-path: polygon(50% 50%, 0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
  }
  62.5% {
    clip-path: polygon(50% 50%, 100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%);
  }
  75% {
    clip-path: polygon(50% 50%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%);
  }
  100% {
    clip-path: polygon(50% 50%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%);
  }
}
@keyframes spinner-oaa3wk {
  0% {
    transform: scaleY(1) rotate(0deg);
  }
  49.99% {
    transform: scaleY(1) rotate(135deg);
  }
  50% {
    transform: scaleY(-1) rotate(0deg);
  }
  100% {
    transform: scaleY(-1) rotate(-135deg);
  }
}

#td-loadingDiv {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 999;
  background-color: rgb(29, 29, 29);
  display: none;
}

.td-loading-wrapper {
  width: 360px;
  height: 62px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  overflow: hidden;
  margin-top: -5px;
}

.td-loading {
  height: 8px;
  border-radius: 10px;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
}

.td-loading-overlay {
  width: 0;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgb(137, 86, 255) 0%, rgb(70, 98, 242) 100%);
  transition: width 3s;
  border-radius: 10px;
}

/*----------------------------------------*/
/*  Colors
/*----------------------------------------*/
.text-body {
  color: var(--td-body);
}

.white-text {
  color: var(--td-white) !important;
}

.black-text {
  color: var(--td-black);
}

.text-heading {
  color: var(--td-heading);
}

.primary-text {
  color: var(--td-primary);
}

.text-secondary {
  color: var(--td-secondary);
}

.text-success {
  color: var(--td-success);
}

.danger-text {
  color: var(--td-danger);
}

.text-warning {
  color: var(--td-warning);
}

.text-green {
  color: var(--td-green);
}

/*----------------------------------------*/
/*  Dark Themes
/*----------------------------------------*/
body.dark-theme {
  background-color: #010C1A;
}

/* Header css*/
/*----------------------------------------*/
/*  Header actions Styles
/*----------------------------------------*/
.currency-switcher {
  position: relative;
}
.currency-switcher .select2-container--default .select2-selection--single .select2-selection__arrow {
  right: -6px;
}
.currency-switcher .select2-container--default:not([dir=rtl]) .select2-selection--single .select2-selection__rendered {
  padding-left: 0;
}
.currency-switcher .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-right: 26px;
}
.currency-switcher .defaults-select .select2-dropdown {
  min-width: 100px;
}

.quick-action-item .action-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--td-white);
}
@media (max-width: 575px) {
  .quick-action-item .action-icon {
    width: 28px;
    height: 28px;
  }
  .quick-action-item .action-icon i {
    width: 16px;
    height: 16px;
  }
}
.quick-action-item .action-icon.notification-btn {
  -webkit-animation: tada 1.5s ease infinite;
  animation: tada 1.5s ease infinite;
}

.profile-card-box {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: end;
  justify-content: space-between;
  gap: 12px 12px;
  flex-wrap: wrap;
}

.profile-card .profile-title {
  font-size: clamp(2rem, 1.5rem + 2vw, 2rem);
  margin-bottom: 10px;
}

.create-project-container {
  position: relative;
}

.create-project-btn {
  padding: 10px 20px;
  background-color: #f5f7fa;
  border: 1px solid var(--td-border-primary);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
}

.buttons-dropdown-menu {
  display: none;
  position: absolute;
  top: calc(100% + 5px);
  inset-inline-end: 0;
  width: 220px;
  background: var(--td-white);
  border: 1px solid rgba(171, 178, 225, 0.3);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  z-index: 1;
}
.buttons-dropdown-menu ul {
  list-style: none;
  padding: 10px;
  margin: 0;
}
.buttons-dropdown-menu li {
  padding: 8px 15px;
  cursor: pointer;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
.buttons-dropdown-menu li svg {
  height: 16px;
  width: 16px;
}
.buttons-dropdown-menu li:hover {
  background-color: var(--td-alice-blue);
}

.notification-panel-box {
  position: relative;
}

.notification-panel {
  width: 415px;
  position: absolute;
  top: calc(100% + 12px);
  backdrop-filter: blur(30px);
  background: #171C35;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  visibility: hidden;
  display: block;
  inset-inline-end: 0;
  z-index: 9;
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
}
@media (max-width: 575px) {
  .notification-panel {
    width: 350px;
    inset-inline-end: -66px;
  }
}
.notification-panel.active {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.notification-panel .notification-item:not(:last-child) {
  margin-bottom: 12px;
}

.notifications-inner {
  padding: 20px 20px 20px;
}
@media (max-width: 575px) {
  .notifications-inner {
    padding: 12px 12px 12px;
  }
}

.notification-header {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  padding: 0px 0px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 18px 20px 18px;
}
@media (max-width: 575px) {
  .notification-header {
    padding: 12px 12px 12px;
  }
}
.notification-header h3 {
  font-size: 20px;
  font-weight: 500;
  color: var(--td-white);
}

.notification-btn-close {
  width: 32px;
  height: 32px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 2;
  font-size: 20px;
}

.notifications-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .notifications-top {
    margin-bottom: 12px;
  }
}
.notifications-top button {
  font-size: 14px;
  font-weight: 500;
}
.notifications-top button:hover {
  color: var(--td-white);
}

.notification-list {
  display: flex;
  background-color: rgba(255, 255, 255, 0.04);
  padding: 10px 10px;
  border-radius: 16px;
}
.notification-list .time {
  color: #a0a0a0;
  font-size: 14px;
  flex: 0 0 auto;
}
.notification-list .inner-item {
  display: flex;
  align-items: self-start;
  column-gap: 12px;
}
.notification-list .inner-item .icon {
  width: 24px;
  height: 24px;
  flex: 0 0 auto;
}
.notification-list .inner-item .contents .title {
  font-weight: 500;
  font-size: 14px;
}
.notification-list .inner-item .contents .message {
  margin-top: 5px;
  color: #7C7C7C;
  font-size: 14px;
}

.notifications-lists {
  display: flex;
  flex-direction: column;
  row-gap: 8px;
}

.notifications-box .notifications-drop-btn::after {
  display: none;
}
.notifications-box .dropdown-menu {
  background: #101016;
  backdrop-filter: blur(50px);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  width: 442px;
  top: 150% !important;
  padding: 24px 24px 24px;
  border: 0;
  inset-inline-end: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .notifications-box .dropdown-menu {
    transform: translateX(35%);
  }
}
@media (max-width: 480px) {
  .notifications-box .dropdown-menu {
    transform: translateX(45%);
  }
}
@media (max-width: 480px) {
  .notifications-box .dropdown-menu {
    width: 350px;
    padding: 15px 15px;
  }
}
@media (max-width: 360px) {
  .notifications-box .dropdown-menu {
    width: 300px;
    padding: 15px 15px;
    inset-inline-end: -15px;
  }
}
.notifications-box .dropdown-menu:before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  padding: 2px;
  background: linear-gradient(139.9deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.notifications-box .notifications-top-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 27px;
}
@media (max-width: 480px) {
  .notifications-box .notifications-top-content {
    margin-bottom: 17px;
  }
}
.notifications-box .notifications-top-content .title {
  color: var(--td-white);
  font-size: 16px;
  font-weight: 800;
}
@media (max-width: 480px) {
  .notifications-box .notifications-top-content .title {
    font-size: 14px;
  }
}
.notifications-box .notifications-top-content .link {
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 480px) {
  .notifications-box .notifications-top-content .link {
    font-size: 12px;
  }
}
.notifications-box .notifications-top-content .link:hover {
  color: var(--td-primary);
}
.notifications-box .notifications-info-wrapper {
  height: 280px;
  overflow-y: scroll;
  scrollbar-width: thin;
  padding-inline-end: 5px;
}
.notifications-box .notifications-info-list ul li {
  list-style: none;
}
.notifications-box .notifications-info-list ul li .list-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  padding: 10px 10px;
}
.notifications-box .notifications-info-list ul li .list-item .content .title {
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
}
@media (max-width: 480px) {
  .notifications-box .notifications-info-list ul li .list-item .content .title {
    font-size: 12px;
  }
}
.notifications-box .notifications-info-list ul li .list-item .content .info {
  font-size: 11px;
  color: var(--td-white);
}
.notifications-box .notifications-info-list ul li .list-item:hover {
  background: rgba(255, 255, 255, 0.1);
}
.notifications-box .notifications-info-list ul li:not(:last-child) {
  margin-bottom: 6px;
}
.notifications-box .notifications-bottom-content {
  margin-top: 24px;
}
@media (max-width: 480px) {
  .notifications-box .notifications-bottom-content {
    margin-top: 14px;
  }
}
.notifications-box .notifications-bottom-content .notifications-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  width: 100%;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 800;
}

.user-profile-drop {
  position: relative;
  cursor: pointer;
  /* Profile Header */
}
.user-profile-drop .dropdown-menu {
  top: calc(100% + 12px);
  inset-inline-end: 0;
  z-index: 9;
  background: var(--td-white);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  width: 220px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
  position: absolute;
  display: block;
  background-color: #171C35;
  width: 320px;
  border-radius: 16px;
  padding-top: 0 !important;
}
@media (max-width: 480px) {
  .user-profile-drop .dropdown-menu {
    width: 300px;
    inset-inline-end: -36px;
  }
}
.user-profile-drop .dropdown-menu.show {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.user-profile-drop .profile-header {
  display: flex;
  column-gap: 12px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  padding: 16px 20px 15px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .user-profile-drop .profile-header {
    padding: 12px 12px 12px;
  }
}
.user-profile-drop .profile-header .avatar {
  width: 44px;
  height: 44px;
  background: #7ed957;
  color: var(--td-black);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  border-radius: 50%;
}
.user-profile-drop .profile-header .user-info {
  /* Tooltip */
}
.user-profile-drop .profile-header .user-info .title {
  font-size: 20px;
  font-weight: 700;
}
@media (max-width: 575px) {
  .user-profile-drop .profile-header .user-info .title {
    font-size: 18px;
  }
}
.user-profile-drop .profile-header .user-info .uid {
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
  margin-top: 5px;
  margin-bottom: 10px;
  font-weight: 500;
  color: #aaa;
  font-size: 12px;
}
.user-profile-drop .profile-header .user-info .uid .uid-text {
  text-decoration: underline;
}
.user-profile-drop .profile-header .user-info .uid .uid-text i {
  cursor: pointer;
}
.user-profile-drop .profile-header .user-info .copy-btn {
  cursor: pointer;
  transition: color 0.3s ease-in-out;
  width: 14px;
}
.user-profile-drop .profile-header .user-info .copy-btn:hover {
  color: var(--td-primary);
}
.user-profile-drop .profile-header .user-info .tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 12px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
}
.user-profile-drop .profile-header .user-info .show-tooltip {
  opacity: 1;
}
.user-profile-drop .profile-header .user-info .unverified {
  background: rgba(255, 163, 54, 0.12);
  color: var(--td-warning);
  border-radius: 6px;
  padding: 6px 10px 6px 10px;
  font-size: 14px;
  font-weight: 500;
}
.user-profile-drop .profie-info-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 15px 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .user-profile-drop .profie-info-list {
    padding: 12px 12px;
  }
}
.user-profile-drop .profie-info-list .profie-info-item {
  border-radius: 8px;
  padding: 7px 10px 8px 10px;
  display: flex;
  gap: 8px;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 700;
}
.user-profile-drop .profie-info-list .profie-info-item .icon {
  display: inline-flex;
  align-items: center;
}
.user-profile-drop .profie-info-list .profie-info-item:hover {
  background: rgba(255, 255, 255, 0.06);
}
.user-profile-drop .profie-info-list .profie-info-item.profile-log-out {
  background-color: rgba(235, 78, 92, 0.16);
  color: var(--td-danger);
}
.user-profile-drop .profie-info-list .profie-info-item.profile-log-out .icon span {
  color: var(--td-danger);
}

.theme-switcher .dark-mode {
  display: none;
}

.dark-theme .light-mode {
  display: none;
}
.dark-theme .dark-mode {
  display: block;
}

/*----------------------------------------*/
/*  Header Styles
/*----------------------------------------*/
.header-transparent {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  z-index: 99;
}

.active-sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
  background-color: rgba(0, 0, 0, 0.7) !important;
  box-shadow: 0 2px 4px rgba(7, 37, 68, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.mode-switcher button {
  position: relative;
  top: -3px;
}

.header-style-one {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.header-style-one.has-landing {
  background-color: #010C1A;
  position: relative;
  z-index: 99;
}
.header-style-one.has-landing .td-main-menu nav > ul > li > a {
  padding: 27px 5px;
}
.header-style-one.has-landing.active-sticky {
  background-color: #010C1A;
}
.header-style-one.is-auth-header {
  padding: 15px 0;
  border-bottom: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .header-style-one {
    padding: 18px 0;
  }
}
.header-style-one .header-btns-wrap {
  display: flex;
  align-items: center;
  column-gap: 10px;
}
.header-style-one .header-quick-actions {
  column-gap: 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-style-one .header-quick-actions {
    column-gap: 8px;
  }
}
.header-style-one .header-left {
  display: flex;
  align-items: center;
  column-gap: 80px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-style-one .header-left {
    column-gap: 16px;
  }
}
.header-style-one .header-logo a {
  display: block;
}
.header-style-one .header-logo img {
  height: 30px;
}
@media (max-width: 575px) {
  .header-style-one .header-logo img {
    height: 26px;
    object-fit: cover;
  }
}
.header-style-one .header-inner {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.language-nav {
  background-color: transparent;
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.language-nav .translate_wrapper.active .more_lang {
  display: block;
  position: absolute;
  background-color: var(--td-white);
  top: 47px;
  inset-inline-end: 0;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
  padding: 6px 6px 6px 6px;
  width: 220px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  background: #171c35;
  padding: 10px;
  width: 200px;
  z-index: 31;
}
.language-nav .current_lang {
  cursor: pointer;
  text-transform: uppercase;
  overflow: hidden;
}
.language-nav .current_lang .lang .flag-icon {
  width: 24px;
  height: 24px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  background-size: cover;
  border-radius: 40px;
}
.language-nav .lang.selected {
  display: none;
}
.language-nav .lang span.lang-txt {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  margin-inline-start: 6px;
  margin-inline-end: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #999999;
}
.language-nav .lang span span {
  color: #999;
  font-weight: 400;
  margin-inline-start: 5px;
}
.language-nav .more_lang {
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  display: none;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
  z-index: 1;
}
.language-nav .more_lang .lang {
  padding: 6px 10px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}
.language-nav .more_lang .lang i {
  width: 24px;
  height: 24px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  background-size: cover;
  border-radius: 40px;
}
.language-nav .more_lang .lang:hover {
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
}
.language-nav .more_lang .lang:hover span {
  color: var(--td-white);
}
.language-nav .more_lang.active {
  opacity: 1;
  transform: translateY(0px);
}

/*----------------------------------------*/
/*  Header two styles
/*----------------------------------------*/
.header-style-two {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #0F0F0F;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .header-style-two {
    padding: 18px 0;
  }
}
.header-style-two .header-inner {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-style-two .header-logo a {
  display: block;
}
.header-style-two .header-logo img {
  height: 30px;
}
@media (max-width: 575px) {
  .header-style-two .header-logo img {
    height: 26px;
    object-fit: cover;
  }
}
.header-style-two .header-left {
  display: flex;
  align-items: center;
  column-gap: 80px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-style-two .header-left {
    column-gap: 16px;
  }
}
.header-style-two .header-right {
  display: flex;
  align-items: center;
  column-gap: 20px;
}
.header-style-two .header-btns-wrap {
  display: flex;
  align-items: center;
  column-gap: 10px;
}
.header-style-two .header-quick-actions {
  column-gap: 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .header-style-two .header-quick-actions {
    column-gap: 8px;
  }
}
@media (max-width: 480px) {
  .header-style-two .header-quick-actions {
    column-gap: 6px;
  }
}
.header-style-two .td-main-menu nav > ul > li > a {
  padding: 31px 5px;
}

/* Banner css*/
/*----------------------------------------*/
/*  Postbox styles
/*----------------------------------------*/
.td-banner-area.banner-style-one {
  padding-top: 155px;
  padding-bottom: 230px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .td-banner-area.banner-style-one {
    padding-top: 130px;
    padding-bottom: 200px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-one {
    padding-top: 130px;
    padding-bottom: 180px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one {
    padding-top: 110px;
    padding-bottom: 180px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .td-banner-area.banner-style-one {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
.td-banner-area.banner-style-one .banner-bg-thumb {
  position: absolute;
  top: 0;
  right: 0;
  max-width: 812px;
  z-index: -1;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one .banner-bg-thumb {
    max-width: 320px;
  }
}
@media (max-width: 575px) {
  .td-banner-area.banner-style-one .banner-bg-thumb {
    max-width: 200px;
  }
}
@media (max-width: 480px) {
  .td-banner-area.banner-style-one .banner-bg-thumb {
    max-width: 140px;
  }
}
.td-banner-area.banner-style-one .shape-one {
  position: absolute;
  top: 40px;
  left: 10%;
  width: 267px;
  z-index: -1;
}
.td-banner-area.banner-style-one .banner-noise {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
  height: 100%;
}
.td-banner-area.banner-style-one .banner-contents .banner-subtitle {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 100px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px 5px 16px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  line-height: 1;
}
.td-banner-area.banner-style-one .banner-contents .banner-subtitle img {
  width: 1.25rem;
  height: 1.25rem;
}
.td-banner-area.banner-style-one .banner-contents .banner-subtitle .text {
  color: #a6ef67;
  font-size: 12px;
  font-weight: 700;
}
.td-banner-area.banner-style-one .banner-contents .banner-title {
  font-size: 60px;
  line-height: 1.2;
  margin-bottom: 0.9375rem;
  font-weight: 900;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-one .banner-contents .banner-title {
    font-size: 54px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-contents .banner-title {
    font-size: 44px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one .banner-contents .banner-title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-banner-area.banner-style-one .banner-contents .banner-title {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  .td-banner-area.banner-style-one .banner-contents .banner-title {
    font-size: 24px;
  }
}
.td-banner-area.banner-style-one .banner-contents .description {
  max-width: 41.875rem;
  margin-bottom: 2.1875rem;
  font-size: 1.25rem;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-contents .description {
    font-size: 18px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one .banner-contents .description {
    font-size: 16px;
  }
}

/*----------------------------------------*/
/*  Postbox styles
/*----------------------------------------*/
.td-banner-area.banner-style-two {
  padding-top: 200px;
  padding-bottom: 165px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .td-banner-area.banner-style-two {
    padding-top: 180px;
    padding-bottom: 165px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-two {
    padding-top: 180px;
    padding-bottom: 180px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-two {
    padding-top: 160px;
    padding-bottom: 180px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-two {
    padding-top: 160px;
    padding-bottom: 120px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-banner-area.banner-style-two {
    padding-top: 140px;
    padding-bottom: 120px;
  }
}
@media (max-width: 480px) {
  .td-banner-area.banner-style-two {
    padding-top: 130px;
    padding-bottom: 120px;
  }
}
.td-banner-area.banner-style-two .shape-one {
  position: absolute;
  top: 80px;
  left: 9%;
  width: 267px;
  z-index: -1;
}
.td-banner-area.banner-style-two .banner-contents .banner-subtitle {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  line-height: 1;
}
.td-banner-area.banner-style-two .banner-contents .banner-subtitle img {
  width: 1.25rem;
  height: 1.25rem;
}
.td-banner-area.banner-style-two .banner-contents .banner-subtitle .text {
  color: #a6ef67;
  font-size: 12px;
  font-weight: 700;
}
.td-banner-area.banner-style-two .banner-contents .banner-title {
  font-size: 60px;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-weight: 900;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 54px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 44px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 22px;
    line-height: 1.3;
  }
}
.td-banner-area.banner-style-two .banner-contents .description {
  max-width: 41.875rem;
  margin-bottom: 2.5rem;
  font-size: 1.125rem;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-two .banner-contents .description {
    font-size: 16px;
  }
}
@media (max-width: 480px) {
  .td-banner-area.banner-style-two .banner-contents .description {
    font-size: 14px;
  }
}

/* Menu css*/
/*----------------------------------------*/
/*  Main menu css
/*----------------------------------------*/
.bar-icon {
  width: 24px;
  height: 24px;
  background: rgb(255, 255, 255);
  border-radius: 50%;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(7, 37, 68, 0.1);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .bar-icon {
    width: 32px;
    height: 32px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    background-color: var(--td-primary);
  }
}

.td-main-menu nav li {
  position: relative;
  list-style: none;
}
.td-main-menu nav > ul {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  gap: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu nav > ul {
    gap: 8px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-main-menu nav > ul {
    gap: 3px;
  }
}
.td-main-menu nav > ul > li > a {
  position: relative;
  font-size: 14px;
  line-height: 1;
  padding: 22px 5px;
  display: inline-block;
  font-weight: 700;
  color: var(--td-white);
}
.td-main-menu nav > ul > li:hover > a {
  color: var(--td-primary);
}
.td-main-menu nav > ul > li:hover > ul {
  opacity: 1;
  pointer-events: all;
}
.td-main-menu nav > ul > li:has(ul) > a::after {
  content: "\f107";
  margin-inline-start: 3px;
  position: relative;
  top: 0px;
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(255, 255, 255, 0.5);
}
.td-main-menu nav > ul > li:has(li.menu-has-children) .dp-menu {
  border-radius: 24px;
}
.td-main-menu nav > ul > li:has(li.menu-has-children) .dp-menu.active {
  border-radius: 24px 0 0px 24px;
}
.td-main-menu nav ul.dp-menu {
  background-color: #171c35;
  width: 320px;
  position: absolute;
  inset-inline-start: 0px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  z-index: 11;
}
.td-main-menu nav ul.dp-menu > li {
  padding: 0px 10px;
}
.td-main-menu nav ul.dp-menu > li:first-child {
  padding-top: 10px;
}
.td-main-menu nav ul.dp-menu > li:last-child {
  padding-bottom: 10px;
}
.td-main-menu nav ul.menu-sidebar {
  position: absolute;
  right: -100%;
  background: #141931;
  padding: 25px 10px 10px;
  width: 320px;
  inset-inline-start: calc(100% + 0px);
  top: 0;
  border-radius: 0px 24px 24px 0px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
}
.td-main-menu nav ul.menu-sidebar .box .pair-list {
  height: 412px;
  overflow-y: scroll;
  scrollbar-width: none;
}
.td-main-menu nav ul.menu-sidebar .box .pair-list::-webkit-scrollbar {
  display: none;
}
.td-main-menu nav ul.menu-sidebar .search-box {
  position: relative;
  margin-top: 15.5px;
  margin-bottom: 10px;
}
.td-main-menu nav ul.menu-sidebar .search-box .search-icon {
  position: absolute;
  top: 50%;
  left: 15px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}
.td-main-menu nav ul.menu-sidebar .search-box input {
  background: #091628;
  border-radius: 100px;
  border: 1px solid rgba(0, 0, 0, 0);
  padding: 5px 16px 5px 35px;
  height: 36px;
  color: #999;
  line-height: 16px;
  font-size: 14px;
}
.td-main-menu nav ul.menu-sidebar .pair-list {
  display: flex;
  row-gap: 6px;
  flex-direction: column;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a {
  border-radius: 8px;
  padding: 9px 10px;
  display: flex;
  column-gap: 12px;
  font-size: 14px;
  font-weight: 700;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .currency-icon img {
  width: 20px;
  height: 20px;
  background-size: cover;
  border-radius: 50%;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .text .eth-usdt-span {
  color: var(--td-white);
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .text .eth-usdt-span-2 {
  color: rgba(255, 255, 255, 0.6);
}
.td-main-menu nav ul.menu-sidebar .pair-list li a:hover {
  background: rgba(255, 255, 255, 0.04);
}

.td-main-menu nav > ul > li ul li:has(ul) > a ::after {
  position: absolute;
  content: "\f107";
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(255, 255, 255, 0.5);
}
.td-main-menu nav > ul > li ul li:hover > ul {
  opacity: 1;
  pointer-events: all;
}

.menu-tabs-wrapper {
  position: relative;
}
.menu-tabs-wrapper .menu-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
  scroll-behavior: smooth;
  width: 100%;
  padding: 5px 0;
  align-items: center;
  /* Scroll Buttons */
}
.menu-tabs-wrapper .menu-tabs::-webkit-scrollbar {
  display: none;
}
.menu-tabs-wrapper .menu-tabs:hover .scroll-left {
  opacity: 1;
}
.menu-tabs-wrapper .menu-tabs:hover .scroll-right {
  opacity: 1;
}
.menu-tabs-wrapper .menu-tabs .nav-tabs {
  display: inline-flex;
  flex: none;
  column-gap: 18px;
}
.menu-tabs-wrapper .menu-tabs .nav-tabs .nav-link {
  font-size: 12px;
  line-height: 16px;
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0);
  color: #999;
  display: flex;
  align-items: center;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  padding-bottom: 1px;
  text-transform: uppercase;
}
.menu-tabs-wrapper .menu-tabs .nav-tabs .nav-link.active {
  border-color: var(--td-primary);
  color: var(--td-white);
}
.menu-tabs-wrapper .menu-tabs .scroll-left {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 5px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  left: -5px;
}
.menu-tabs-wrapper .menu-tabs .scroll-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 5px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  right: -5px;
}

.menu-item-box {
  display: flex;
  align-items: center;
  column-gap: 16px;
  border-radius: 16px;
  padding: 12px 12px;
}
.menu-item-box:hover {
  background: rgba(255, 255, 255, 0.04);
}
.menu-item-box .icon {
  background: #2e3553;
  border-radius: 8px;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}
.menu-item-box .contents h6 {
  color: #fff;
  text-align: left;
  font-size: 14px;
  font-weight: 700;
}
.menu-item-box .contents p {
  font-size: 12px;
  line-height: 13px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 2px;
}

.menu-icon {
  width: 26px;
  height: 18px;
  position: relative;
  display: block;
}
.menu-icon::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  top: 0;
  inset-inline-start: 0;
  background: var(--td-white);
  transition: all 0.3s;
}
.menu-icon::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  bottom: 0;
  inset-inline-start: 0;
  background: var(--td-white);
  transition: all 0.3s;
}
.menu-icon span {
  position: absolute;
  content: "";
  width: 18px;
  height: 1px;
  top: 50%;
  inset-inline-end: 0;
  transition: all 0.3s;
  background-color: var(--td-white);
}

/*----------------------------------------*/
/*  4.3 Mobile menu css
/*----------------------------------------*/
.mobile-menu {
  margin-bottom: 30px;
}
.mobile-menu ul {
  list-style: none;
}
.mobile-menu ul li {
  position: relative;
}
.mobile-menu ul li > a {
  padding: 14px 0;
  font-size: 15px;
  font-weight: 700;
  color: var(--td-white);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mobile-menu ul li > a i {
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ddd;
}
.mobile-menu ul li ul {
  padding-left: 5%;
}
@media (max-width: 480px) {
  .mobile-menu ul li ul {
    padding-left: 3%;
  }
}
.mobile-menu ul li ul li .menu-item-box {
  background: hsla(0, 0%, 100%, 0.04);
}
.mobile-menu ul li:not(:last-child) > a {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}
.mobile-menu ul li.active > a {
  color: var(--td-primary);
}
.mobile-menu ul li.active > .tp-menu-close {
  color: var(--td-white);
  background: var(--td-black);
  border-color: var(--td-black);
}
.mobile-menu ul li.active > .tp-menu-close i {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.mobile-menu ul li .td-dp-menu {
  display: none;
  padding-inline-start: 20px;
}
.mobile-menu .td-mega-menu {
  padding: 0;
  padding-top: 30px;
  box-shadow: none;
  transform: inherit;
}

.tp-menu-close {
  position: absolute;
  inset-inline-end: 0;
  top: 6px;
  height: 30px;
  width: 30px;
  font-size: 12px;
  line-height: 29px;
  text-align: center;
  border: 1px solid rgba(1, 15, 28, 0.12);
}
.tp-menu-close i {
  transition: all 0.3s;
}

.td-mega-menu {
  position: absolute;
  top: 100%;
  inset-inline-start: 0;
  inset-inline-end: 0;
  opacity: 0;
  width: 100%;
  z-index: 99;
  margin: 0 auto;
  background: #fff;
  visibility: hidden;
  transform-origin: top;
  transition: 0.4s;
  transition-duration: 0.1s;
  padding: 30px 30px 10px 30px;
  transform: perspective(300px) rotateX(-18deg);
  box-shadow: 0px 10px 30px 0px rgba(25, 25, 26, 0.1);
}

/*----------------------------------------*/
/*  Blog styles
/*----------------------------------------*/
.single-blog-item {
  position: relative;
}
.single-blog-item:hover .blog-thumb img {
  transform: scale(1.1);
}
.single-blog-item .blog-thumb {
  overflow: hidden;
  border-radius: 16px;
}
.single-blog-item .blog-thumb img {
  width: 100%;
}
.single-blog-item .blog-contents {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 18px 20px;
  background: rgba(0, 0, 0, 0.13);
  backdrop-filter: blur(11px);
  border-radius: 0px 0px 16px 16px;
}
@media (max-width: 480px) {
  .single-blog-item .blog-contents {
    padding: 16px 16px;
  }
}
.single-blog-item .blog-contents .blog-title {
  font-size: 20px;
}
@media (max-width: 480px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-blog-item .blog-contents .blog-title {
    font-size: 18px;
  }
}
.single-blog-item .blog-contents .blog-tag {
  display: inline-flex;
  height: 30px;
  padding: 6px 16px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.13);
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
  margin-bottom: 8px;
}

.inner-pages-shapes .glow-two {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}
.inner-pages-shapes .glow-one {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.inner-pages-shapes .shape-one {
  position: absolute;
  top: 26%;
  left: 0;
  z-index: -1;
}

/*----------------------------------------*/
/*  Postbox styles
/*----------------------------------------*/
.postbox-main-wrapper {
  padding-right: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .postbox-main-wrapper {
    padding-right: 0;
  }
}

.postbox-details-contents p {
  margin-bottom: 30px;
}
.postbox-details-contents h3 {
  line-height: 1.27;
  margin-bottom: 25px;
}
.postbox-details-contents h5 {
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
}
.postbox-details-contents h6 {
  padding-bottom: 10px;
}
.postbox-details-contents ul {
  margin-bottom: 30px;
}
.postbox-details-contents ul li {
  list-style: none;
  padding-left: 18px;
  position: relative;
}
.postbox-details-contents ul li::before {
  position: absolute;
  content: "";
  top: 10px;
  left: 0;
  width: 8px;
  height: 8px;
  background: #A6EF67;
  border-radius: 50%;
}
.postbox-details-contents ul li:not(:last-child) {
  margin-bottom: 4px;
}

.postbox-img {
  margin-bottom: 25px;
}
.postbox-img img {
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}

.postbox-share {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 16px;
}
.postbox-share a {
  font-size: 16px;
  color: #BBBBBB;
  transition: all 0.3s ease-in-out;
  border-radius: 6px;
  border-style: solid;
  border: 1px solid #bbbbbb;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}
.postbox-share a:hover {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}

.tagcloud-items {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 8px;
  align-items: self-start;
}

.tagcloud-box {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px 8px;
}
.tagcloud-box a {
  font-size: 14px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  position: relative;
  text-transform: capitalize;
  background: rgba(255, 255, 255, 0.04);
  background-color: rgba(114, 128, 255, 0.1);
  border: 1px solid rgba(114, 128, 255, 0.2);
  color: rgba(8, 8, 8, 0.6);
  padding: 5px 14px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
}

.sidebar-sticky {
  position: sticky;
  top: 80px;
}

.sidebar-widgets-wrapper {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-widget {
  background: #091628;
  border-radius: 16px;
  padding: 25px 30px 30px;
}
@media (max-width: 480px) {
  .sidebar-widget {
    padding: 16px 16px 16px;
  }
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .sidebar-wrapper {
    padding-inline-start: 0;
  }
}

.sidebar-widget-title {
  position: relative;
  display: inline-block;
  font-weight: 500;
  font-size: 22px;
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .sidebar-widget-title {
    margin-bottom: 18px;
  }
}

/*----------------------------------------*/
/*  Recent post styles
/*----------------------------------------*/
.rc-post {
  padding: 12px;
  gap: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}
@media (max-width: 480px) {
  .rc-post {
    gap: 12px;
  }
}
.rc-post:hover .rc-post-thumb img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
.rc-post:last-child {
  margin-bottom: 0;
}

.rc-post-title {
  font-size: 16px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  display: box;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}
.rc-post-title a:hover {
  color: var(--td-primary);
}

.rc-post-thumb {
  overflow: hidden;
  flex: 0 0 auto;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}
.rc-post-thumb img {
  width: 90px;
  height: 90px;
  object-fit: cover;
}
@media (max-width: 480px) {
  .rc-post-thumb img {
    width: 80px;
    height: 80px;
  }
}

.rc-meta {
  margin-top: 3px;
}
.rc-meta span {
  font-size: 14px;
}
.rc-meta span svg, .rc-meta span i {
  margin-inline-end: 6px;
}
.rc-meta span svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}
.rc-meta span:hover a {
  color: var(--td-primary);
}

/* pages css*/
/*----------------------------------------*/
/*  Profile settings styles
/*----------------------------------------*/
.profile-sidebar-left {
  width: 280px;
  flex: 0 0 auto;
  background: #091628;
  height: 100vh;
  border-radius: 8px;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .profile-sidebar-left {
    width: 245px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .profile-sidebar-left {
    width: 100%;
    height: max-content;
  }
}
.profile-sidebar-left .heading-contents {
  padding: 18px 30px 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.profile-sidebar-left .heading-contents .title {
  font-size: 20px;
  font-weight: 700;
}
.profile-sidebar-left .sidebar {
  padding: 18px 18px 18px;
}
.profile-sidebar-left .sidebar nav ul {
  padding: 0;
  position: relative;
}
.profile-sidebar-left .sidebar nav ul::before {
  position: absolute;
  content: "";
  width: 2px;
  height: 100%;
  background: rgba(217, 217, 217, 0.1);
  border-radius: 30px;
}
.profile-sidebar-left .sidebar nav ul li {
  list-style: none;
}
.profile-sidebar-left .sidebar nav ul li.active a {
  color: var(--td-primary);
}
.profile-sidebar-left .sidebar nav ul li.has-danger .link {
  color: var(--td-danger);
  font-size: 14px;
  font-weight: 700;
}
.profile-sidebar-left .sidebar nav ul li.has-danger .link:hover {
  background-color: rgba(233, 78, 91, 0.2);
  color: var(--td-danger);
}
.profile-sidebar-left .sidebar nav ul li:not(:last-child) {
  margin-bottom: 12px;
}
.profile-sidebar-left .sidebar nav ul li .link {
  padding: 0rem 1rem;
  display: block;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  transition: 0.2s;
  text-align: left;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
}
.profile-sidebar-left .sidebar nav ul li .link:hover {
  color: var(--td-primary);
}
.profile-sidebar-left .sidebar nav ul li .link.active {
  color: var(--td-primary);
}

.profile-settings-area {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .profile-settings-area {
    flex-direction: column;
    gap: 18px;
  }
}
.profile-settings-area .profile-right-content {
  flex-grow: 1;
  margin-inline-start: 18px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .profile-settings-area .profile-right-content {
    margin-inline-start: 0;
  }
}
.profile-settings-area .profile-btns {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.profile-quick-action-item {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: 276px 1fr;
  justify-content: space-between;
  gap: 24px 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .profile-quick-action-item {
    grid-template-columns: 1fr;
  }
}
.profile-quick-action-item:not(:last-child) {
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(8, 8, 8, 0.1);
}

.toggle-group {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.toggle-group .toggle-item {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: start;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .toggle-group .toggle-item {
    grid-template-columns: 62px 1fr;
  }
}
.toggle-group .toggle-item .toggle-label h5 {
  margin: 0;
  font-size: 16px;
}
.toggle-group .toggle-item .toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}
.toggle-group .toggle-item .toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggle-group .toggle-item .toggle-switch input:checked + .slider {
  background-color: var(--td-green);
}
.toggle-group .toggle-item .toggle-switch input:checked + .slider:before {
  transform: translate(18px, -50%);
}
.toggle-group .toggle-item .toggle-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  transition: 0.4s;
  height: 22px;
  width: 40px;
}
.toggle-group .toggle-item .toggle-switch .slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 4px;
  top: 50%;
  background-color: var(--td-white);
  border-radius: 50%;
  transition: 0.4s;
  transform: translateY(-50%);
}

.two-fa-auth-wrapper .contents .description {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 26px;
}
.two-fa-auth-wrapper .qr-code p {
  color: var(--td-primary);
}
.two-fa-auth-wrapper .qr-code .thumb {
  max-width: 336px;
  margin-top: 35px;
}
@media (max-width: 575px) {
  .two-fa-auth-wrapper .qr-code .thumb {
    max-width: 236px;
  }
}
.two-fa-auth-wrapper .qr-code .thumb img {
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}

/*----------------------------------------*/
/*  Dashboard-widget styles
/*----------------------------------------*/
.dashboard-widget-wrapper {
  display: grid;
  grid-template-columns: auto 432px;
  gap: 1.25rem;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .dashboard-widget-wrapper {
    grid-template-columns: auto 350px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .dashboard-widget-wrapper {
    display: flex;
    flex-direction: column;
  }
}

.dashboard-widget-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 18px;
  height: 100%;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .dashboard-widget-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .dashboard-widget-grid {
    grid-template-columns: 1fr;
  }
}

.dashboard-widget-card {
  display: flex;
  align-items: center;
  background: #091628;
  padding: 18px 18px;
  border-radius: 16px;
  gap: 16px;
  background-repeat: no-repeat;
  background-size: cover;
}
@media (max-width: 575px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .dashboard-widget-card {
    padding: 12px 12px;
  }
}
.dashboard-widget-card .icon {
  background: #f26822;
  border-radius: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}
.dashboard-widget-card .icon img {
  width: 40px;
}
@media (max-width: 575px) {
  .dashboard-widget-card .icon {
    width: 44px;
    height: 44px;
  }
  .dashboard-widget-card .icon img {
    width: 26px;
  }
}
.dashboard-widget-card .contents .card-title {
  color: #999999;
  font-size: 16px;
  font-weight: 700;
}
@media (max-width: 575px) {
  .dashboard-widget-card .contents .card-title {
    font-size: 16px;
  }
}
.dashboard-widget-card .contents .card-value {
  color: var(--td-white);
  font-size: 20px;
  font-weight: 500;
  margin-top: 0.3125rem;
}
@media (max-width: 575px) {
  .dashboard-widget-card .contents .card-value {
    font-size: 18px;
  }
}
.dashboard-widget-card:nth-child(2) .icon {
  background-color: #3B57E7;
}
.dashboard-widget-card:nth-child(3) .icon {
  background-color: #45772F;
}
.dashboard-widget-card:nth-child(4) .icon {
  background-color: #00694D;
}
.dashboard-widget-card:nth-child(5) .icon {
  background-color: #574B90;
}
.dashboard-widget-card:nth-child(6) .icon {
  background-color: #00758F;
}
.dashboard-widget-card:nth-child(7) .icon {
  background-color: #4B6584;
}
.dashboard-widget-card:nth-child(8) .icon {
  background-color: #497D74;
}
.dashboard-widget-card:nth-child(9) .icon {
  background-color: #EB3B5A;
}

/*----------------------------------------*/
/*  Dashboard styles 
/*----------------------------------------*/
.app-page-header {
  max-width: 100vw;
  position: fixed;
  top: 0;
  z-index: 5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  margin-inline-start: 290px;
  width: calc(100% - 290px);
  background-image: #010C1A;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .app-page-header {
    margin-inline-start: 260px;
    width: calc(100% - 260px);
  }
}
@media (max-width: 575px) {
  .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.app-page-header.close_icon {
  margin-inline-start: 80px;
  width: calc(100% - 80px);
}
.app-page-header.dashboard-sticky {
  position: fixed;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
  top: 0;
  width: -webkit-fill-available;
  background: #131314;
}

.app-dashboard-header {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px 30px;
  padding: 6px 20px;
  height: 70px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #010C1A;
}
@media (max-width: 575px) {
  .app-dashboard-header {
    padding: 15px 15px;
    gap: 16px 16px;
  }
}
.app-dashboard-header .left-content .td-main-menu nav > ul > li > a {
  padding: 24px 5px;
}
.app-dashboard-header .right-content .header-quick-actions {
  gap: 16px;
}
.app-dashboard-header .right-content .header-btns-wrap {
  position: relative;
  padding-right: 16px;
  margin-right: 16px;
}
.app-dashboard-header .right-content .header-btns-wrap::before {
  position: absolute;
  content: "";
  right: 0;
  top: -14px;
  height: 70px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}
.app-dashboard-header .right-content .others-actions {
  display: flex;
  column-gap: 10px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-dashboard-header .right-content .others-actions {
    column-gap: 8px;
  }
}
@media (max-width: 480px) {
  .app-dashboard-header .right-content .others-actions {
    column-gap: 6px;
  }
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .page-wrapper.compact-wrapper .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.page-wrapper.compact-wrapper .app-page-body-wrapper div.app-sidebar-wrapper.close_icon ~ .app-page-body {
  margin-inline-start: 80px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.app-page-body {
  min-height: calc(100vh - 70px);
  margin-top: 70px;
  margin-inline-start: 290px;
  padding: 20px 20px 20px;
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .app-page-body {
    margin-inline-start: 260px;
  }
}
@media (max-width: 575px) {
  .app-page-body {
    min-height: calc(100vh - 70px);
    margin-top: 70px;
  }
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-page-body-wrapper .app-page-body {
    margin-inline-start: 0 !important;
  }
}

.bg-overlay.active {
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.2);
  position: fixed;
  z-index: 8;
  top: 0;
}

/*----------------------------------------*/
/* Dashboard Sidebar styles
/*----------------------------------------*/
.app-sidebar-wrapper {
  position: fixed;
  height: 100%;
  top: 0;
  z-index: 9;
  line-height: inherit;
  text-align: left;
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
}
.app-sidebar-wrapper.close_icon {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  margin-inline-start: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-sidebar-wrapper.close_icon {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    margin-inline-start: -300px;
  }
}
.app-sidebar-wrapper .sidebar-inner {
  -webkit-box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--td-heading);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .app-sidebar-wrapper .sidebar-inner {
    background-color: #11131A;
  }
}

.app-sidebar {
  width: 290px;
  height: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;
  background: #010C1A;
  border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(3px);
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .app-sidebar {
    width: 260px;
  }
}
.app-sidebar .main-sidebar-header {
  height: 70px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  transition: all 0.03s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.app-sidebar .main-sidebar-header img {
  transition: all 0.02s ease;
}
.app-sidebar .main-sidebar-header .sidebar-logo .main-logo img {
  height: 45px;
}
.app-sidebar .main-sidebar-header .sidebar-logo .main-logo.logo-white-mode {
  display: none;
}
.app-sidebar .main-sidebar-header .sidebar-logo .small-logo {
  display: none;
}
.app-sidebar .nav > ul {
  padding-inline-start: 0px;
}
.app-sidebar .nav ul li {
  list-style: none;
  margin: 0 14px;
  margin-bottom: 2px;
}
.app-sidebar .sidebar-menu {
  display: none;
}
.app-sidebar .sidebar-left,
.app-sidebar .sidebar-right {
  display: none;
}
.app-sidebar .main-menu > .slide.active .sidebar-menu .sidebar-menu-item:hover .side-menu-angle, .app-sidebar .main-menu > .slide:hover .sidebar-menu .sidebar-menu-item:hover .side-menu-angle {
  color: var(--td-primary) !important;
}
.app-sidebar .slide.has-sub .sidebar-menu {
  transform: translate(0, 0) !important;
  visibility: visible !important;
}
.app-sidebar .slide.has-sub {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide.has-sub {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide.active .sidebar-menu-item {
  background: rgba(91, 108, 253, 0.09);
}
.app-sidebar .slide.active .sidebar-menu-item .side-menu-icon svg * {
  stroke: var(--td-white);
}
.app-sidebar .slide.active .sidebar-menu-item .sidebar-menu-label {
  color: var(--td-white);
}
.app-sidebar .slide.logout .sidebar-menu-item .sidebar-menu-label {
  color: rgba(233, 78, 91, 0.65);
}
.app-sidebar .slide.logout .sidebar-menu-item .side-menu-icon svg * {
  stroke: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child2 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child3 .sidebar-menu-item:hover {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-category .category-name {
  color: var(--td-secondary);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  padding: 4px 10px;
  white-space: nowrap;
  position: relative;
  margin-top: 15px;
  display: block;
}
[dir=rtl] .app-sidebar .sidebar-menu-category .category-name {
  text-align: right;
}
.app-sidebar .sidebar-menu-item {
  padding: 12px 10px;
  position: relative;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid transparent;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}
.app-sidebar .sidebar-menu-item.active {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-item.active .sidebar-menu-label,
.app-sidebar .sidebar-menu-item.active .side-menu-angle {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-item.active .side-menu-icon {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-item:hover {
  background-color: rgba(91, 108, 253, 0.09);
}
.app-sidebar .sidebar-menu-item:hover .sidebar-menu-item .sidebar-menu-label {
  color: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu-item:hover .sidebar-menu-item .side-menu-icon svg * {
  stroke: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu-item:hover .logout:hover .sidebar-menu-item .sidebar-menu-label {
  color: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu-item:hover .logout:hover .sidebar-menu-item .side-menu-icon svg * {
  stroke: rgba(233, 78, 91, 0.65);
}
.app-sidebar .sidebar-menu {
  padding: 0;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item {
  padding: 6px 6px;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item:before {
  position: absolute;
  content: "\e404";
  font-family: "Font Awesome 6 Pro";
  font-size: 12px;
  inset-inline-start: -10px;
  opacity: 0.8;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item.active {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 li, .app-sidebar .sidebar-menu.child2 li, .app-sidebar .sidebar-menu.child3 li {
  padding: 0;
  position: relative;
}
.app-sidebar .sidebar-menu.child1 li {
  padding-inline-start: 56px;
}
.app-sidebar .sidebar-menu.child2 li {
  padding-inline-start: 12px;
}
.app-sidebar .sidebar-menu.child3 li {
  padding-inline-start: 16px;
}
.app-sidebar .sidebar-menu-label {
  white-space: nowrap;
  color: #999;
  position: relative;
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  font-weight: 600;
  transition: 0.3s;
}
.app-sidebar .side-menu-icon {
  line-height: 0;
  font-size: 14px;
  text-align: center;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}
.app-sidebar .side-menu-icon svg {
  width: 16px;
  height: 16px;
}
.app-sidebar .side-menu-icon svg * {
  transition: 0.3s;
  width: 16px;
  height: 16px;
  stroke: #999;
}
.app-sidebar .side-menu-angle {
  transform-origin: center;
  position: absolute;
  inset-inline-end: 20px;
  line-height: 1;
  font-size: 14px;
  transition: all 0.03s ease;
  opacity: 0.8;
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .close_sidebar.app-sidebar {
    inset-inline-start: 0px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .app-sidebar {
    inset-inline-start: -300px;
  }
}
.app-sidebar.collapsed {
  inset-inline-start: -300px;
}
.app-sidebar.nav-folded {
  width: 80px;
  transition: 0.2s;
}
.app-sidebar.nav-folded .nav ul li {
  margin: 0 10px;
}
.app-sidebar.nav-folded .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-menu-item {
  padding: 10px 16px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
}
.app-sidebar.nav-folded .sidebar-menu-item .sidebar-menu-label,
.app-sidebar.nav-folded .sidebar-menu-item .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .main-logo {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .small-logo {
  display: block;
}
.app-sidebar.nav-folded .invite-card-content {
  display: none;
}
.app-sidebar.nav-folded .invite-card-box {
  padding: 8px;
  margin: 10px 10px;
}
.app-sidebar .app-sidebar.nav-folded.side-nav-hover .sidebar-menu-category .category-name {
  display: block;
}
.app-sidebar.side-nav-hover {
  width: 290px;
  transition: all 0.3s ease;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-category .category-name {
  display: block !important;
}
.app-sidebar.side-nav-hover .sidebar-menu-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.app-sidebar.side-nav-hover .sidebar-logo .main-logo {
  display: block;
}
.app-sidebar.side-nav-hover .sidebar-logo .small-logo {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: block;
}
.app-sidebar.side-nav-hover .invite-card-box {
  padding: 8px 8px 8px 16px;
}
.app-sidebar.side-nav-hover .invite-card-content {
  display: block;
  transition: 0.2s ease;
  opacity: 1;
}

.toggle-sidebar {
  position: absolute;
  top: 60px;
  inset-inline-end: -10px;
  z-index: 5;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .toggle-sidebar {
    position: relative;
    top: inherit;
    inset-inline-end: inherit;
    z-index: 5;
  }
}
.toggle-sidebar.active .bar-icon {
  transform: rotate(-180deg);
}

/*----------------------------------------*/
/*  Dashboard default card styles
/*----------------------------------------*/
.default-card {
  background: #091628;
  border-radius: 24px;
}
.default-card .card-heading {
  padding: 18px 30px 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
@media (max-width: 575px) {
  .default-card .card-heading {
    padding: 18px 20px 18px;
  }
}
.default-card .card-inner {
  padding: 30px 30px 30px;
}
@media (max-width: 575px) {
  .default-card .card-inner {
    padding: 20px 20px 20px;
  }
}

/*----------------------------------------*/
/* Footer primary style
/*----------------------------------------*/
.footer-primary {
  background: #0E1B2C;
  position: relative;
  z-index: 5;
}
.footer-primary .footer-bg {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.footer-primary .footer-main {
  padding: 80px 0 50px;
}

.footer-line {
  position: relative;
  height: 10px;
  background-color: transparent;
}
.footer-line::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0;
  width: 30%;
  height: 10px;
  background: #649448;
  clip-path: polygon(0% 0%, 99.829% 0%, 98.695% 100%, 0% 100%, 0% 0%);
  z-index: 30;
}
.footer-line::after {
  content: "";
  position: absolute;
  border-top: 4px solid #293F9C;
  width: 100%;
  top: 6px;
  left: 0;
}

.footer-widget-grid {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 50px 64px;
  justify-content: space-between;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .footer-widget-grid {
    grid-template-columns: auto auto auto 270px;
    justify-content: space-between;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-widget-grid {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-widget-grid {
    grid-template-columns: 1fr 1fr;
    justify-content: space-between;
  }
}
@media (max-width: 480px) {
  .footer-widget-grid {
    grid-template-columns: 1fr;
    justify-content: space-between;
  }
}

.footer-wg-head .title {
  color: var(--td-white);
  margin-bottom: 20px;
  font-size: 18px;
}

.footer-links ul li {
  list-style: none;
}
.footer-links ul li:not(:last-child) {
  margin-bottom: 6px;
}
.footer-links ul li a {
  font-size: 14px;
  font-weight: 700;
}
.footer-links ul li a:hover {
  color: var(--td-primary);
  text-decoration: underline;
}

.footer-social {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 16px;
  flex-direction: column;
}
.footer-social ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 16px;
}
.footer-social ul li {
  list-style: none;
}
.footer-social ul li a {
  font-size: 16px;
  color: #BBBBBB;
  transition: all 0.3s ease-in-out;
  border-radius: 6px;
  border-style: solid;
  border: 1px solid #bbbbbb;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}
.footer-social ul li a:hover {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}

/*----------------------------------------*/
/* Footer primary style
/*----------------------------------------*/
.footer-secondary {
  position: relative;
  z-index: 5;
  border-top: 1px solid rgba(255, 255, 255, 0.16);
}
.footer-secondary .footer-bg {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.footer-secondary .footer-top {
  margin-bottom: 45px;
}
.footer-secondary .footer-main {
  padding: 80px 0 50px;
}

/*----------------------------------------*/
/* Footer copyright style
/*----------------------------------------*/
.footer-copyright {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-copyright {
    justify-content: center;
  }
}

.copyright-link ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.copyright-link ul li {
  list-style: none;
  font-size: 14px;
  font-weight: 500;
}
.copyright-link ul li a:hover {
  color: var(--td-primary);
}

.copyright-text p {
  font-size: 14px;
  font-weight: 500;
}

/* pages css*/
/*----------------------------------------*/
/*  Terms conditions styles
/*----------------------------------------*/
.td-page-contents h3 {
  font-size: 38px;
  line-height: 1.3;
  margin-bottom: 30px;
}
.td-page-contents h4 {
  font-size: 24px;
  margin-bottom: 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-page-contents h4 {
    font-size: 18px;
    line-height: 1.5;
  }
}
.td-page-contents p {
  font-size: 18px;
  line-height: 28px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-page-contents p {
    font-size: 16px;
  }
}
.td-page-contents p:not(:last-child) {
  margin-bottom: 30px;
}
.td-page-contents ul {
  margin-bottom: 40px;
}
.td-page-contents ul li {
  margin-bottom: 14px;
  list-style: none;
  color: var(--td-text-primary);
  display: flex;
  gap: 10px;
}
.td-page-contents ul li span {
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
}
.td-page-contents ul li span i {
  font-size: 5px;
  border-radius: 0;
  color: var(--td-text-primary);
  background-color: transparent;
}
.td-page-contents a {
  font-size: 17px;
  font-weight: 600;
  line-height: 27px;
  color: var(--td-primary);
  text-decoration: underline;
}

/*----------------------------------------*/
/*  Affiliate program styles
/*----------------------------------------*/
.affiliate-program-contents {
  padding-right: 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .affiliate-program-contents {
    padding-right: 0;
  }
}

.affiliate-benefit-contents .affiliate-benefit-title {
  margin-bottom: 16px;
}
.affiliate-benefit-contents .benefit-list ul li {
  list-style: none;
  font-weight: 500;
  display: flex;
  gap: 8px;
}
.affiliate-benefit-contents .benefit-list ul li:not(:last-child) {
  margin-bottom: 12px;
}

/*----------------------------------------*/
/* Our mission styles
/*----------------------------------------*/
.our-mission-left-contents {
  margin-right: 50px;
}
.our-mission-left-contents .des-contents {
  margin-block-end: 30px;
}

.mission-info-contents .info-list {
  display: flex;
  gap: 16px;
}

.mission-info-title {
  margin-bottom: 20px;
}

.our-mission-right-contents .abstract-shape {
  width: 240px;
  margin-left: auto;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .our-mission-right-contents .abstract-shape {
    width: 160px;
    margin-left: auto;
  }
}

/*----------------------------------------*/
/*  who we are styles
/*----------------------------------------*/
.who-are-left-contents {
  padding-right: 50px;
}

.who-are-right-contents {
  padding-left: 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .who-are-right-contents {
    padding-left: 0;
  }
}
.who-are-right-contents > .description {
  margin-bottom: 25px;
  font-size: 18px;
}
.who-are-right-contents .info-list {
  margin-top: 15px;
}
.who-are-right-contents .info-list ul li {
  list-style: none;
  padding-left: 18px;
  position: relative;
}
.who-are-right-contents .info-list ul li::before {
  position: absolute;
  content: "";
  top: 10px;
  left: 0;
  width: 8px;
  height: 8px;
  background: #A6EF67;
  border-radius: 50%;
}
.who-are-right-contents .info-list ul li:not(:last-child) {
  margin-bottom: 4px;
}

/*----------------------------------------*/
/*  Trends panel styles
/*----------------------------------------*/
.staking-single-card {
  background: #091628;
  border-radius: 16px;
  padding: 25px 30px 25px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: right bottom;
}
.staking-single-card .contents .title {
  font-size: 20px;
  margin-bottom: 6px;
}
.staking-single-card .contents .description {
  margin-bottom: 0.75rem;
}

.staking-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px 16px;
  flex-wrap: wrap;
  padding: 0 16px;
}
.staking-filter .right-side {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/*----------------------------------------*/
/*  Trends panel styles
/*----------------------------------------*/
.trends-panel-card {
  padding: 18px;
  background: #0A1729;
  border-radius: 30px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
}
@media (max-width: 480px) {
  .trends-panel-card {
    padding: 16px;
  }
}
.trends-panel-card::before {
  position: absolute;
  content: "";
  width: 101px;
  height: 102px;
  right: -30px;
  right: -30px;
  bottom: -28px;
  background-color: #1F4A55;
  filter: blur(70px);
}
.trends-panel-card .heading-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #142032;
  border-radius: 12px;
  padding: 10px 10px;
  height: 50px;
  margin-bottom: 16px;
}
.trends-panel-card .heading-top .heading-left {
  display: flex;
  gap: 6px;
}
.trends-panel-card .heading-top .heading-left img {
  width: 25px;
  height: 25px;
}
.trends-panel-card .market-trends-panel .td-table {
  min-width: 350px;
}

.timeframe-select {
  background-color: #27313d;
  color: white;
  padding: 3px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  appearance: none;
  outline: none;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}
.timeframe-select:hover {
  background-color: #374b70;
}

.market-overview-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

.filter-search-bar {
  position: relative;
  width: 237px;
}
.filter-search-bar input {
  border: none;
  color: var(--td-white);
  width: 100%;
  background: #142032;
  border-radius: 8px;
  padding: 5px 16px 5px 34px;
  height: 40px;
  font-size: 14px;
  padding-left: 36px;
}
.filter-search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.filter-search-bar .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15px;
  display: inline-flex;
}

.market-overview-tab.td-tab .nav-tabs {
  gap: 12px;
}
.market-overview-tab.td-tab .nav-tabs .nav-link {
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 18px 18px;
  border-radius: 30px;
  font-size: 18px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.6);
  background-color: transparent !important;
  position: relative;
  z-index: 1;
}
.market-overview-tab.td-tab .nav-tabs .nav-link::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 30px;
  z-index: -1;
}
.market-overview-tab.td-tab .nav-tabs .nav-link.active::before {
  background-color: rgba(0, 0, 0, 0);
}

/*----------------------------------------*/
/*  Welcome promo gift styles
/*----------------------------------------*/
.welcome-promo-gift-popup {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1099;
  background-color: rgba(0, 0, 0, 0.3);
  transition: all 0.3s;
  margin: 0 auto;
  text-align: center;
  visibility: visible;
  opacity: 1;
}
.welcome-promo-gift-popup.show {
  visibility: hidden;
  opacity: 0;
}

.welcome-promo-gift-box {
  width: 600px;
  margin: 0 auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 30px;
  background: #091628;
  padding: 35px 50px 30px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  border: 2px solid #142A49;
  z-index: 1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .welcome-promo-gift-box {
    width: 500px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box {
    width: 80%;
    padding: 25px 20px 20px;
  }
}
.welcome-promo-gift-box::before {
  position: absolute;
  content: "";
  top: 65px;
  background-color: var(--td-white);
  filter: blur(84.5px);
  width: 154px;
  height: 154px;
  left: 50%;
  transform: translateX(-50%);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box::before {
    width: 120px;
    height: 120px;
  }
}
.welcome-promo-gift-box .promo-gift-icon {
  width: 142px;
  margin: 0 auto;
  margin-bottom: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box .promo-gift-icon {
    width: 100px;
    margin-bottom: 20px;
  }
}
.welcome-promo-gift-box .promo-gift-icon img {
  animation: promoBounce 1.2s infinite alternate;
}
.welcome-promo-gift-box .promo-gift-contents .promo-amount {
  color: var(--2, #A6EF67);
  font-size: 30px;
  font-weight: 900;
}
.welcome-promo-gift-box .promo-gift-contents .promo-subtitle {
  font-weight: 500;
  margin-top: 2px;
}
.welcome-promo-gift-box .promo-gift-contents .promo-text {
  font-size: 18px;
  max-width: 378px;
  margin: 0 auto;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.8);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box .promo-gift-contents .promo-text {
    font-size: 16px;
  }
}
@media (max-width: 480px) {
  .welcome-promo-gift-box .promo-gift-contents .promo-text {
    font-size: 14px;
  }
}
.welcome-promo-gift-box .promo-close {
  position: absolute;
  top: -20px;
  right: -20px;
  border: none;
  font-size: 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box .promo-close {
    right: -10px;
    top: -10px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .welcome-promo-gift-box .promo-close svg {
    width: 28px;
    height: 28px;
  }
}

@keyframes promoBounce {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-10px);
  }
}
/*----------------------------------------*/
/*  Security styles
/*----------------------------------------*/
.security-card-items {
  margin-top: 84px;
}
@media (max-width: 575px) {
  .security-card-items {
    margin-top: 44px;
  }
}

.security-card-item {
  text-align: center;
  display: inline-flex;
  padding: 47px 30px 47px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  border-radius: 24px;
  border: 2px solid rgba(3, 166, 109, 0.1);
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(10px);
  width: 100%;
}
@media (max-width: 575px) {
  .security-card-item {
    padding: 20px 20px 20px;
  }
}
@media (max-width: 480px) {
  .security-card-item {
    padding: 20px 16px 20px;
  }
}
.security-card-item .security-card-icon {
  width: 70px;
  height: 70px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #03A66D;
  border-radius: 50%;
  margin-bottom: 20px;
  position: relative;
  border: 9px solid #0A242A;
}
@media (max-width: 480px) {
  .security-card-item .security-card-icon {
    margin-bottom: 0px;
  }
}
.security-card-item .security-card-contents .title {
  font-size: 16px;
}

.security-thumb {
  position: absolute;
  z-index: 5;
  right: 189px;
  top: 26%;
  width: 335px;
}
@media (max-width: 575px) {
  .security-thumb {
    width: 220px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .security-thumb {
    right: 0;
    top: 26%;
    width: 250px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .security-thumb {
    position: relative;
    right: 0;
    left: 0;
    margin: auto;
    text-align: center;
  }
}
.security-thumb::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 455px;
  height: 455px;
  border-radius: 455px;
  opacity: 0.1;
  background: #03A66D;
  filter: blur(100px);
  z-index: -1;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .security-thumb::before {
    width: 200px;
    height: 200px;
  }
}

.security-wrapper .row [class*=col-]:nth-child(2) {
  margin-top: -20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .security-wrapper .row [class*=col-]:nth-child(2) {
    margin-top: 30px;
  }
}
.security-wrapper .row [class*=col-]:nth-child(3) {
  margin-top: -70px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .security-wrapper .row [class*=col-]:nth-child(3) {
    margin-top: 30px;
  }
}

.security-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 24px;
  padding-left: 50px;
  padding-right: 80px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .security-cards-grid {
    padding-right: 0;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .security-cards-grid {
    padding-left: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .security-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
  }
}
.security-cards-grid .has_fade_anim:nth-child(2) {
  grid-row: span 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.security-cards-grid .security-card-item {
  background-color: #001B11;
}

/*----------------------------------------*/
/* Trading View styles
/*----------------------------------------*/
.trading-system-status-section {
  position: relative;
}
.trading-system-status-section::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 248px;
  top: 0;
  left: 0;
  background: linear-gradient(270deg, #010C1A 29.4%, rgba(1, 12, 26, 0) 94.11%);
  transform: matrix(-1, 0, 0, 1, 0, 0);
  z-index: 9;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .trading-system-status-section::before {
    width: 190px;
  }
}
.trading-system-status-section::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 248px;
  top: 0;
  right: 0;
  background: linear-gradient(270deg, #010C1A 29.4%, rgba(1, 12, 26, 0) 94.11%);
  z-index: 9;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .trading-system-status-section::after {
    width: 190px;
  }
}

.trading-status-sliders .trading-status-card {
  display: inline-flex;
  align-items: self-start;
  gap: 12px;
  background: #0A1729;
  border: 1px solid rgba(255, 255, 255, 0.16);
  box-shadow: 0px 4px 11px rgba(166, 239, 103, 0.1);
  border-radius: 16px;
  padding: 12px 16px;
}
.trading-status-sliders .trading-status-card .icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  flex: 0 0 auto;
  display: inline-flex;
  align-items: center;
}
.trading-status-sliders .trading-status-card .contents .title {
  font-size: 16px;
}
.trading-status-sliders .trading-status-card .contents .description {
  font-size: 14px;
  display: block;
  margin-top: 2px;
}
.trading-status-sliders .trading-status-card .contents .description .down {
  color: #EB4E5C;
}
.trading-status-sliders .swiper-wrapper {
  transition-timing-function: linear;
}
.trading-status-sliders .swiper-slide {
  width: max-content !important;
  /* Important for 'slidesPerView: 'auto'' */
}
.trading-status-sliders.style-two .trading-status-card {
  background: #0F0F0F;
  border: 1px solid rgba(255, 255, 255, 0.16);
  border-radius: 12px;
  gap: 6px;
  align-items: center;
}
.trading-status-sliders.style-two .trading-status-card .contents {
  display: flex;
  gap: 6px;
}
.trading-status-sliders.style-two .trading-status-card .contents .description {
  margin-top: 0;
}

/*----------------------------------------*/
/*  KYC Styles 
/*----------------------------------------*/
.verification-inner-contents {
  padding: 30px 30px;
}

.identity-alert-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.kyc-card {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-radius: 8px;
}
.kyc-card:not(:last-child) {
  margin-bottom: 15px;
}
@media (max-width: 575px) {
  .kyc-card {
    flex-direction: column;
    align-items: start;
    gap: 12px;
  }
}
.kyc-card .status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: var(--td-white);
  border-radius: 50%;
  margin-right: 15px;
  flex: 0 0 auto;
}
.kyc-card .status-icon .icon {
  font-size: 1.5rem;
}
.kyc-card .details .label {
  font-weight: bold;
  color: #ccc;
}
.kyc-card .details .status {
  display: inline-block;
  padding: 2px 16px;
  border-radius: 30px;
  font-weight: bold;
  font-size: 14px;
}
.kyc-card .details .view-details {
  color: #3a82e1;
  text-decoration: none;
  font-size: 0.9rem;
}
.kyc-card .details .view-details:hover {
  text-decoration: underline;
}
.kyc-card .details .submission-date {
  font-size: 0.85rem;
  color: #aaa;
  margin-top: 5px;
}
.kyc-card .details .details-info {
  display: flex;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
}
.kyc-card.rejected {
  background: rgba(255, 46, 55, 0.04);
  border: 2px dashed rgba(255, 46, 55, 0.2);
  border-radius: 16px;
}
.kyc-card.rejected .status-icon .icon {
  color: #b53039;
}
.kyc-card.rejected .details .status {
  background-color: #b53039;
  color: var(--td-white);
}
.kyc-card.success {
  background: rgba(0, 204, 0, 0.02);
  border: 2px dashed rgba(0, 204, 0, 0.2);
  border-radius: 16px;
}
.kyc-card.success .status-icon .icon {
  color: #2f7d4f;
}
.kyc-card.success .details .status {
  background-color: #2f7d4f;
  color: var(--td-white);
}

/*----------------------------------------*/
/*  Authentication styles
/*----------------------------------------*/
.td-authentication-section {
  padding: 30px 0 50px;
}

.auth-main-box {
  max-width: 575px;
  display: grid;
  margin: 0 auto;
  background: #071220;
  border-radius: 30px;
  border-image: linear-gradient(180deg, rgba(115, 168, 248, 0.6) 0%, rgba(59, 87, 231, 0) 100%);
  padding: 45px 50px;
  position: relative;
  z-index: 1;
}
@media (max-width: 575px) {
  .auth-main-box {
    padding: 35px 25px;
  }
}
.auth-main-box::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(115, 168, 248, 0.6) 0%, rgba(59, 87, 231, 0) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.auth-logo {
  margin-bottom: 40px;
}
.auth-logo img {
  height: 28px;
}

.auth-intro-contents {
  margin-bottom: 35px;
}
.auth-intro-contents .title {
  font-size: 20px;
  font-weight: 700;
}
.auth-intro-contents .description {
  color: #999;
  font-weight: 500;
  margin-top: 10px;
  font-size: 14px;
}

.auth-login-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 10px;
  margin-bottom: 20px;
}

.auth-divide {
  -moz-box-align: center;
  align-items: center;
  display: -moz-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  padding: 27px 0 27px;
}
.auth-divide .divider-line {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  -moz-box-flex: 1;
  flex-grow: 1;
  height: 0;
}
.auth-divide .or {
  cursor: default;
  flex-shrink: 0;
  font-size: 16px;
  margin-inline-start: 10px;
  margin-inline-end: 10px;
  font-weight: 500;
}

.auth-from-bottom-content {
  text-align: center;
}

.have-auth-account {
  margin-top: 12px;
}
.have-auth-account .description {
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}

/*----------------------------------------*/
/*  Promo card styles
/*----------------------------------------*/
.promo-card-box {
  background: #091628;
  border-radius: 16px;
  padding: 16px;
}

.promo-card-item {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 8px;
  padding: 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem 1.25rem;
}
.promo-card-item .inner {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}
.promo-card-item .card-icon {
  width: 40px;
  height: 40px;
  flex: 0 0 auto;
}
.promo-card-item .card-icon img {
  width: 100%;
}
.promo-card-item .card-contents .card-title {
  color: var(--td-white);
  font-size: 14px;
  font-weight: 700;
}
.promo-card-item .card-contents .card-description {
  color: #999999;
  font-size: 14px;
  font-weight: 500;
}

.promo-affiliate-card {
  background: #091628;
  border-radius: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1.875rem;
  padding: 18px 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
}
.promo-affiliate-card .card-icon {
  width: 66px;
  height: 66px;
}
.promo-affiliate-card .card-icon img {
  width: 100%;
}
.promo-affiliate-card .card-contents .card-title {
  color: var(--td-white);
  font-size: 1.25rem;
  font-weight: 700;
}
@media (max-width: 575px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .promo-affiliate-card .card-contents .card-title {
    font-size: 1.125rem;
  }
}
.promo-affiliate-card .card-contents .card-description {
  font-size: 14px;
  font-weight: 500;
}
.promo-affiliate-card .card-contents .card-link {
  display: inline-block;
}
.promo-affiliate-card .card-contents .card-link .td-btn {
  background: rgba(166, 239, 103, 0.07);
  border-radius: 8px;
  color: var(--td-primary);
  font-weight: 400;
}

.promo-feature-card {
  background: #091628;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 1.875rem;
  padding: 18px 18px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
}
.promo-feature-card .card-contents .card-title {
  color: var(--td-white);
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}
.promo-feature-card .card-contents .card-description {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
}
.promo-feature-card .card-contents .card-link {
  margin-top: 0.75rem;
}
.promo-feature-card .card-contents .card-link .td-btn {
  background: rgba(166, 239, 103, 0.07);
  border-radius: 8px;
  color: var(--td-primary);
  font-weight: 400;
}

/*----------------------------------------*/
/*  verify card styles
/*----------------------------------------*/
.verify-card {
  background: rgba(235, 78, 92, 0.08);
  border-radius: 16px;
  border: 1px solid rgba(235, 78, 92, 0.4);
  padding: 18px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 198px;
  position: relative;
}
@media (max-width: 480px) {
  .verify-card {
    height: inherit;
    position: relative;
    flex-direction: column;
    padding: 16px 16px;
  }
}

.verify-content {
  max-width: 280px;
  display: flex;
  flex-direction: column;
  align-items: self-start;
  justify-content: space-between;
  height: 100%;
  gap: 0.75rem 0.75rem;
}

.verify-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .verify-title {
    font-size: 18px;
  }
}

.verify-description {
  font-size: 14px;
  color: #999999;
  margin-bottom: 20px;
}

.verify-image {
  width: 187px;
  text-align: right;
  flex: 0 0 auto;
}
.verify-image img {
  max-width: 100%;
  height: auto;
}

/*----------------------------------------*/
/*  Financial card styles
/*----------------------------------------*/
.financial-card {
  background-color: #091628;
  border-radius: 16px;
  padding: 18px 18px;
  display: flex;
  justify-content: space-between;
  min-height: 198px;
  background-repeat: no-repeat;
  background-size: cover;
}
@media (max-width: 575px) {
  .financial-card {
    flex-direction: column;
  }
}
.financial-card .left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.financial-card .left-section .inner .greeting {
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 700;
  color: var(--td-primary);
}
.financial-card .left-section .inner .name {
  font-size: 20px;
  margin-bottom: 6px;
}
.financial-card .left-section .inner .last-login {
  font-size: 14px;
}
.financial-card .left-section .uid-value {
  color: #E2E8F0;
  font-size: 14px;
  font-weight: 500;
}
.financial-card .left-section .uid-title {
  background: hsla(0, 0%, 100%, 0.04);
  border-radius: 4px;
  padding: 4px 10px 4px 10px;
  display: inline-flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  height: 20px;
  position: relative;
  font-size: 12px;
  font-weight: 700;
}
.financial-card .left-section .uid-bottom {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
}
.financial-card .left-section .copy-icon {
  display: flex;
  align-items: center;
}
.financial-card .left-section .copy-icon i {
  width: 14px;
  height: 14px;
}
.financial-card .right-section {
  flex: 1;
  position: relative;
  display: flex;
  padding-left: 1.125rem;
  margin-left: 1.125rem;
}
@media (max-width: 575px) {
  .financial-card .right-section {
    padding-left: 0rem;
    margin-left: 0rem;
    padding-top: 1.125rem;
    margin-top: 1.125rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
    gap: 0.375rem;
  }
}
.financial-card .right-section::before {
  position: absolute;
  content: "";
  min-height: calc(100% + 36px);
  background: linear-gradient(241deg, rgba(9, 22, 40, 0.1) 0%, rgb(166, 239, 103) 50%, rgba(9, 22, 40, 0.1) 100%);
  top: -18px;
  left: 0;
  width: 1px;
}
@media (max-width: 575px) {
  .financial-card .right-section::before {
    display: none;
  }
}
.financial-card .right-section .assets-contents {
  text-align: left;
}
.financial-card .right-section .assets-contents .total-assets span {
  font-size: 20px;
  font-weight: 700;
  color: var(--td-white);
  display: block;
  margin-bottom: 0.75rem;
}
.financial-card .right-section .assets-contents .asset-value {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 5px;
  padding: 2px 14px 2px;
  display: flex;
  align-items: center;
  gap: 0.3125rem;
  position: relative;
  margin-bottom: 0.625rem;
}
.financial-card .right-section .assets-contents .asset-value .amount {
  font-size: 1rem;
  font-weight: 700;
  color: var(--td-white);
}
.financial-card .right-section .assets-contents .asset-value .asset-select .select2-container {
  min-width: 80px;
}
.financial-card .right-section .assets-contents .asset-value .asset-select .select2-container--default .select2-selection--single {
  height: 25px;
  line-height: 25px;
}
.financial-card .right-section .assets-contents .asset-value .asset-select .select2-container--default .select2-selection {
  border: 0;
}
.financial-card .right-section .others-actions {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: end;
  gap: 0.25rem;
}
.financial-card .right-section .time-zone {
  text-align: end;
}
.financial-card .right-section .time-zone span {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 4px;
  padding: 4px 10px 4px 10px;
  display: inline-flex;
  align-items: center;
  height: 24px;
  backdrop-filter: blur(15px);
  font-size: 12px;
  line-height: 17px;
  font-weight: 700;
  margin-bottom: 0.5rem;
}
.financial-card .right-section .time-zone-value .select2-container {
  min-width: 250px;
}
.financial-card .right-section .time-zone-value .select2-container--default .select2-selection--single {
  height: 25px;
  line-height: 25px;
}
.financial-card .right-section .time-zone-value .select2-container--default .select2-selection {
  border: 0;
}
.financial-card .right-section .hide-info {
  cursor: pointer;
  padding: 5px 10px;
  height: 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 4px;
  line-height: 1;
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--td-white);
  font-size: 12px;
  font-weight: 700;
}
.financial-card .right-section .hide-info .icon i {
  width: 16px;
  height: 16px;
}

/*----------------------------------------*/
/*  Asset card styles
/*----------------------------------------*/
.trade-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 8px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  min-height: 41px;
}
.trade-card-header .title {
  font-size: 14px;
  font-weight: 700;
}
.trade-card-header .close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #aaa;
  cursor: pointer;
  transition: color 0.3s;
}

.asset-card-inner .account-section {
  padding: 13px 8px 13px;
}
.asset-card-inner .account-section:last-child {
  border-bottom: 0;
}
.asset-card-inner .divider-line {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.asset-card-inner .account-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.asset-card-inner .account-header span {
  font-size: 14px;
  font-weight: 700;
  color: #999999;
}
.asset-card-inner .account-header i {
  cursor: pointer;
}
.asset-card-inner .account-details {
  display: grid;
  grid-template-columns: auto auto;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
  color: var(--td-white);
}
.asset-card-inner .balance {
  text-align: right;
  color: #aaa;
}
.asset-card-inner .button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  padding: 0 8px;
}
.asset-card-inner .deposit-btn {
  flex: 1;
  padding: 10px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: 0.3s;
  color: #ccc;
  margin-right: 8px;
  background: #010c1a;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.16);
}
.asset-card-inner .deposit-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}
.asset-card-inner .transfer-btn {
  flex: 1;
  padding: 10px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 6px;
  cursor: pointer;
  transition: 0.3s;
  background: #A6EF67;
  border: none;
  color: #1D1D1D;
}
.asset-card-inner .transfer-btn:hover {
  background: #83d93e;
}

/*----------------------------------------*/
/*  Impact card styles
/*----------------------------------------*/
.impact-card-area {
  position: relative;
  z-index: 1;
  border-radius: 8px;
  padding: 8px;
}
.impact-card-area::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.impact-tab .nav-tabs {
  background: #071220;
  border-radius: 100px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 6px;
  display: flex;
  flex-direction: row;
  gap: 14px;
  align-items: center;
  justify-content: flex-start;
  align-self: stretch;
  position: relative;
  margin-bottom: 10px;
}
.impact-tab .nav-tabs .nav-link {
  font-weight: 700;
  background-color: transparent;
  position: relative;
  text-transform: uppercase;
  border: 0;
  gap: 5px;
  padding: 6px 6px;
  text-decoration: none;
  border-radius: 30px;
  font-size: 9px;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  color: var(--td-white);
}
.impact-tab .nav-tabs .nav-link.active {
  background-color: var(--td-primary);
  color: #1D1D1D;
}

.impact-box .item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 6px;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  background: #071220;
  border-radius: 4px;
}
.impact-box .item-row:not(:last-child) {
  margin-bottom: 8px;
}
.impact-box .item-row .flag {
  margin-right: 4px;
}
.impact-box .item-row .flag i {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-size: cover;
}
.impact-box .item-details {
  display: flex;
  align-items: center;
}
.impact-box .time {
  color: var(--td-white);
  margin-right: 8px;
  background: rgba(255, 255, 255, 0.06);
  border-radius: 4px;
  display: block;
  padding: 5px 7px;
  font-size: 10px;
  font-weight: 700;
}
.impact-box .description {
  font-size: 10px;
  font-weight: 700;
}
.impact-box .impact {
  display: flex;
  align-items: center;
}
.impact-box .impact .title {
  margin-right: 8px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
}
.impact-box .impact-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}
.impact-box .impact-circle:last-child {
  margin-right: 0;
}
.impact-box .impact-circle.impact-down {
  background-color: #606060;
}
.impact-box .impact-low {
  background-color: #5db18b;
}
.impact-box .impact-medium {
  background-color: #f39c12;
}
.impact-box .impact-high {
  background-color: #e74c3c;
}

/*----------------------------------------*/
/*  Trading platform styles
/*----------------------------------------*/
.trading-workspace-tab .nav-tabs {
  display: inline-flex;
  column-gap: 12px;
  row-gap: 12px;
  padding: 10px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  border-radius: 0;
}
.trading-workspace-tab .nav-tabs .nav-link {
  font-size: 14px;
  line-height: 16px;
  font-weight: 700;
  background-color: transparent;
  color: #999;
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid transparent;
}
.trading-workspace-tab .nav-tabs .nav-link.active {
  border-color: var(--td-primary);
  color: var(--td-white);
}

.trading-platform {
  position: relative;
  padding: 12px 24px 16px;
  background: #071220;
  border-radius: 8px;
  z-index: 1;
}
@media (max-width: 575px) {
  .trading-platform {
    padding: 12px 12px 12px;
  }
}
.trading-platform .workspace-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px 20px;
}
.trading-platform::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.trading-platform .trades-table {
  width: 100%;
  border-collapse: collapse;
}
.trading-platform .trades-table thead .table-sorting-arrow {
  position: relative;
  top: 3px;
}
.trading-platform .trades-table thead .table-sorting-arrow i {
  width: 16px;
  height: 16px;
}
.trading-platform .trades-table th {
  font-weight: 700;
  text-align: left;
  padding: 4px 6px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  font-size: 12px;
}
.trading-platform .trades-table td {
  padding: 6px 6px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  font-size: 12px;
  font-weight: 500;
}
.trading-platform .trades-table td:first-child {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
.trading-platform .trades-table td:last-child {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.trading-platform .trades-table .trade-id {
  color: var(--td-white);
}
.trading-platform .trades-table .profit-negative {
  color: var(--td-danger);
}
.trading-platform .trades-table .close-button {
  background: none;
  color: var(--td-white);
  cursor: pointer;
  padding: 4px;
  font-size: 20px;
  width: 28px;
  height: 28px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trading-platform .trades-table .value-zero {
  position: relative;
}
.trading-platform .trades-table .value-zero .btn-inner {
  display: flex;
  align-items: center;
  column-gap: 6px;
}
.trading-platform .trades-table .value-zero .btn-inner .text {
  color: var(--td-white);
}
.trading-platform .trades-table .value-zero .btn-inner button {
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trading-platform .trades-table .value-zero .security-order-container {
  width: 280px;
  background-color: #091628;
  border-radius: 8px;
  padding: 10px 10px;
  color: var(--td-white);
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 100%;
  display: none;
}
.trading-platform .trades-table .value-zero .security-order-container .header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
}
.trading-platform .trades-table .value-zero .security-order-container .header .close-btn {
  cursor: pointer;
  font-size: 20px;
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trading-platform .trades-table .value-zero .security-order-container .content {
  margin-top: 15px;
}
.trading-platform .trades-table .value-zero .security-order-container .input-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 6px 6px;
}
.trading-platform .trades-table .value-zero .security-order-container .input-row button {
  background-color: #3d404e;
  border: none;
  color: var(--td-white);
  font-size: 18px;
  padding: 5px;
  cursor: pointer;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.trading-platform .trades-table .value-zero .security-order-container .input-value {
  background-color: #252734;
  color: var(--td-white);
  border: none;
  text-align: center;
  width: 130px;
  padding: 8px;
  border-radius: 4px;
  flex-grow: 1;
  height: 30px;
  font-size: 0.75rem;
}
.trading-platform .trades-table .value-zero .security-order-container .label {
  font-size: 12px;
  color: #a0a3b1;
  margin-bottom: 10px;
  display: block;
}
.trading-platform .trades-table .value-zero .security-order-container .percent-row {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #c0c4d3;
  padding: 10px 0;
  border-top: 1px solid #3d404e;
  margin-top: 10px;
}
.trading-platform .trades-table .value-zero .security-order-container .footer {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
.trading-platform .trades-table .value-zero .security-order-container .back-btn {
  background-color: #3d404e;
  border-radius: 4px;
  padding: 0 18px;
  font-size: 12px;
  height: 32px;
}
.trading-platform .trades-table .value-zero .security-order-container .back-btn:focus {
  color: var(--td-white);
}
.trading-platform .trades-table .value-zero .security-order-container .save-btn {
  background-color: var(--td-success);
  color: var(--td-white);
  border-radius: 4px;
  padding: 0 18px;
  font-size: 12px;
  height: 32px;
}
.trading-platform .trades-table .value-zero .security-order-container .save-btn:focus {
  color: var(--td-white);
}

.expand-switch {
  display: flex;
  align-items: center;
  column-gap: 6px;
}
.expand-switch .text {
  font-size: 12px;
  font-weight: 600;
}
.expand-switch .icon {
  display: flex;
  align-items: center;
}
.expand-switch .icon i {
  width: 18px;
  height: 18px;
}

.user-not-sign {
  display: grid;
  place-items: center;
  padding: 70px 0;
}

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/
.trade-order-panel {
  position: relative;
  z-index: 1;
  border-radius: 8px;
  padding: 8px;
}
.trade-order-panel::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.order-type-tab .nav-tabs {
  display: flex;
  column-gap: 12px;
  background: #091628;
  border-radius: 4px;
  padding: 10px;
}
.order-type-tab .nav-tabs .nav-link {
  font-size: 14px;
  line-height: 16px;
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0);
  color: #999;
  display: flex;
  align-items: center;
}
.order-type-tab .nav-tabs .nav-link.active {
  border-bottom: 1px solid var(--td-primary);
  padding-bottom: 2px;
  color: var(--td-white);
}

.order-tabs .nav-tabs {
  display: flex;
  column-gap: 12px;
  margin-top: 2px;
  padding: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  border-radius: 0;
}
.order-tabs .nav-tabs .nav-link {
  font-size: 14px;
  line-height: 16px;
  font-weight: 700;
  background-color: rgba(0, 0, 0, 0);
  color: #999;
  display: flex;
  align-items: center;
}
.order-tabs .nav-tabs .nav-link.active {
  border-bottom: 1px solid var(--td-primary);
  padding-bottom: 2px;
  color: var(--td-white);
}

.order-form {
  display: flex;
  gap: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .order-form {
    gap: 16px;
  }
}
@media (max-width: 575px) {
  .order-form {
    flex-direction: column;
  }
}
.order-form .divider-line {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.order-box {
  flex: 1;
}

.order-info {
  margin-bottom: 16px;
}
.order-info p {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 700;
}
.order-info p:not(:last-child) {
  margin-bottom: 10px;
}
.order-info p span {
  color: var(--td-white);
}

.percentage-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 25px;
  margin-top: 20px;
}
.percentage-buttons.has-danger button.active {
  background: #eb4e5c;
}
.percentage-buttons button {
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
  border-radius: 4px;
  padding: 0 16px;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  height: 24px;
  position: relative;
  font-size: 12px;
  font-weight: 700;
  min-width: 74px;
}
.percentage-buttons button.active {
  background: #10b981;
}

.buy-btn {
  background: #03a66d;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  flex-shrink: 0;
  height: 44px;
  width: 100%;
  color: var(--td-white);
}

.sell-btn {
  background: #eb4e5c;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  height: 44px;
  color: var(--td-white);
}

/*----------------------------------------*/
/*  Trade card styles
/*----------------------------------------*/
.trade-card {
  position: relative;
  z-index: 1;
  background: #071220;
  border-radius: 8px;
  padding-bottom: 8px;
}
.trade-card::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.trade-card-heading {
  padding: 8px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 41px;
  display: flex;
  align-items: center;
}

/*----------------------------------------*/
/*  Balance styles
/*----------------------------------------*/
.stat-box-wrapper {
  margin-bottom: 10px;
}
.stat-box-wrapper .currency-box {
  min-width: 156px;
}
.stat-box-wrapper .currency-box,
.stat-box-wrapper .stat-box {
  flex-direction: column;
  align-items: flex-start;
  padding: 8px 10px;
  width: 165px;
  background: #091628;
  border-radius: 4px;
  height: 58px;
  display: inline-flex;
  justify-content: center;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .stat-box-wrapper .currency-box,
  .stat-box-wrapper .stat-box {
    width: 136px;
  }
}
.stat-box-wrapper .currency-box .stat-label,
.stat-box-wrapper .stat-box .stat-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
  display: block;
  margin-bottom: 6px;
}
.stat-box-wrapper .currency-box .stat-value,
.stat-box-wrapper .stat-box .stat-value {
  color: var(--td-white);
  font-size: 12px;
  font-weight: 700;
}
.stat-box-wrapper .currency-box .current-price,
.stat-box-wrapper .stat-box .current-price {
  color: #eb4e5c;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
}
.stat-box-wrapper .currency-box .change-value,
.stat-box-wrapper .stat-box .change-value {
  color: #eb4e5c;
  text-align: left;
  font-size: 12px;
  font-weight: 700;
  margin-top: 6px;
}
.stat-box-wrapper .stat-grid {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  column-gap: 20px;
  row-gap: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .stat-box-wrapper .stat-grid {
    column-gap: 12px;
    row-gap: 12px;
  }
}
.stat-box-wrapper .currency-select {
  position: relative;
  display: flex;
  align-items: center;
}
.stat-box-wrapper .currency-select .inner {
  background: rgba(59, 87, 231, 0.11);
  border-radius: 4px;
  padding: 4px 10px 4px 10px;
}
.stat-box-wrapper .currency-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}
.stat-box-wrapper select {
  appearance: none;
  background: hsl(219.57, 62.67%, 14.71%);
  border: none;
  color: var(--td-white);
  font-size: 12px;
  cursor: pointer;
  width: max-content;
  padding-right: 20px;
  font-weight: 500;
}
.stat-box-wrapper .dropdown-arrow {
  position: absolute;
  right: 15px;
  width: 12px;
  height: 12px;
  pointer-events: none;
  top: 3px;
}
.stat-box-wrapper select:focus {
  outline: none;
}

.market-table-inner {
  padding: 12px 8px 8px;
  min-height: 568px;
  overflow-y: auto;
  margin-right: 8px;
  padding-right: 8px;
}
.market-table-inner::-webkit-scrollbar {
  width: 5px;
}
.market-table-inner::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.trade-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  margin-right: 10px;
}
.trade-table thead th {
  font-size: 12px;
  font-weight: 700;
  padding-bottom: 12px;
  padding-top: 0;
}
.trade-table thead tr {
  display: flex;
  justify-content: space-between;
}
.trade-table tbody {
  max-height: 400px;
  display: block;
}
.trade-table tbody tr {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.trade-table tbody tr:hover {
  background-color: #1E2A3C;
}
.trade-table .price-green {
  color: #4AFF80;
}
.trade-table .price-red {
  color: #EB4E5C;
}
.trade-table th,
.trade-table td {
  padding: 0.4rem 0.3rem;
  font-size: 12px;
  font-weight: 500;
}
.trade-table th:last-child,
.trade-table td:last-child {
  text-align: right;
}

.trading-nav-list ul li {
  list-style: none;
  color: var(--td-white);
  text-align: left;
  font-family: "Satoshi-Bold", sans-serif;
  font-size: 14px;
  font-weight: 700;
}

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/
/* Market Card */
.market-card {
  position: relative;
}
.market-card::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.market-list-box {
  padding: 8px;
}

/* Search Bar */
.market-search-bar {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px;
  position: relative;
}
.market-search-bar input {
  border: none;
  color: var(--td-white);
  width: 100%;
  background: #091628;
  border-radius: 100px;
  padding: 5px 16px 5px 34px;
  height: 40px;
  font-size: 14px;
}
.market-search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}
.market-search-bar .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 22px;
}

/* Tabs Wrapper */
.market-tabs-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  margin: 0 8px;
  /* Scroll Buttons */
}
.market-tabs-wrapper:hover .scroll-left {
  opacity: 1;
}
.market-tabs-wrapper:hover .scroll-right {
  opacity: 1;
}
.market-tabs-wrapper .market-tabs {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none;
  scroll-behavior: smooth;
  width: 100%;
  padding: 5px 0;
  align-items: center;
}
.market-tabs-wrapper .market-tabs::-webkit-scrollbar {
  display: none;
}
.market-tabs-wrapper .market-tabs .nav-tabs {
  display: inline-flex;
  flex: none;
  gap: 14px;
}
.market-tabs-wrapper .market-tabs .nav-tabs .nav-link {
  font-size: 12px;
  line-height: 16px;
  font-weight: 700;
  background-color: transparent;
  color: #999999;
  display: flex;
  align-items: center;
  column-gap: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 1px;
}
.market-tabs-wrapper .market-tabs .nav-tabs .nav-link.active {
  border-color: var(--td-primary);
  color: var(--td-white);
}
.market-tabs-wrapper .scroll-left {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 5px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  left: -5px;
}
.market-tabs-wrapper .scroll-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 5px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  right: -5px;
}

/* Sticky Header */
.market-header {
  display: flex;
  justify-content: space-between;
  font-weight: 500;
  color: hsla(0, 0%, 100%, 0.8);
  border-radius: 8px;
  font-size: 12px;
  margin-bottom: 8px;
  margin-top: 2px;
  padding: 0 16px;
}

/* Market List */
.market-list {
  max-height: 403px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-right: 8px;
}
.market-list:not(:last-child) {
  margin-bottom: 8px;
}
.market-list::-webkit-scrollbar {
  width: 5px;
}
.market-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

/* Market Item */
.market-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 6px;
  background: #071220;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}
.market-item .symbol {
  display: flex;
  align-items: center;
}
.market-item .symbol .favorite {
  color: var(--td-white);
  font-family: "Satoshi-Bold", sans-serif;
  font-size: 11px;
  line-height: 16px;
  font-weight: 700;
  padding-right: 4px;
  display: inline-flex;
}
.market-item .symbol .favorite i {
  width: 14px;
  height: 14px;
  color: var(--td-text-primary);
}
.market-item .symbol .flag {
  font-size: 18px;
  margin-right: 8px;
}
.market-item .symbol .flag i {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-size: cover;
}
.market-item .symbol .currency {
  font-size: 11px;
  line-height: 16px;
  font-weight: 700;
  color: var(--td-white);
}
.market-item .price {
  display: flex;
  column-gap: 26px;
  align-items: center;
}
.market-item .price .price-text {
  font-family: "Satoshi-Bold", sans-serif;
  font-size: 11px;
  line-height: 16px;
  font-weight: 700;
  padding-right: 4px;
  display: flex;
  align-items: center;
}
.market-item .change .change-text {
  color: #eb4e5c;
  text-align: right;
  font-family: "Satoshi-Medium", sans-serif;
  font-size: 11px;
  font-weight: 500;
}
.market-item .change .change-text.positive {
  color: #00c58e;
}
.market-item .change .change-text.negative {
  color: #ff4d4d;
}
.market-item .mini-chart {
  width: 50px;
  height: 20px;
}

.tradingview-main-chart {
  padding: 10px;
  border-radius: 8px;
  position: relative;
}
.tradingview-main-chart::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  padding: 1px;
  background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.5000004768%, rgba(6, 17, 20, 0.25) 99.5000004768%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

/*----------------------------------------*/
/*  Trade layout styles
/*----------------------------------------*/
.trade-main-inner {
  padding: 16px;
}

.trade-main-grid {
  display: grid;
  grid-template-columns: 396px auto 346px;
  row-gap: 16px;
  column-gap: 16px;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px) {
  .trade-main-grid {
    grid-template-columns: 315px auto 300px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .trade-main-grid {
    grid-template-columns: auto 610px auto;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .trade-main-grid {
    grid-template-columns: 1fr;
  }
}

/*----------------------------------------*/
/*  Ticker styles
/*----------------------------------------*/
.ticker-area {
  width: 100%;
  overflow: hidden;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.1);
  padding: 8px 0;
  white-space: nowrap;
  position: relative;
}

.ticker-wrap {
  display: flex;
  width: 100%;
  white-space: nowrap;
  position: relative;
}

.ticker-content {
  display: flex;
  animation: ticker-scroll 60s linear infinite;
}

.ticker-content span {
  font-size: 12px;
  color: #cfd3dc;
  font-weight: 500;
}
.ticker-content span span {
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}
.ticker-content span span::before {
  position: absolute;
  content: "";
  height: 10px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.ticker-content .green {
  color: #03A66D;
}
.ticker-content .red {
  color: var(--td-danger);
}

@keyframes ticker-scroll {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(-50%);
  }
}
/*----------------------------------------*/
/*  Steps Gateway styles
/*----------------------------------------*/
.pages-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px 12px;
}
.pages-heading .title-inner .title {
  font-size: 1.5rem;
  font-weight: 700;
}
.pages-heading .page-links ul li {
  position: relative;
  list-style: none;
}
.pages-heading .page-links ul li .dp-menu {
  width: 170px;
  position: absolute;
  right: 0px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
  display: flex;
  flex-direction: column;
  z-index: 11;
  padding: 10px 10px;
  background: #171c35;
  border-radius: 12px;
  gap: 4px;
}
.pages-heading .page-links ul li .dp-menu li {
  width: 100%;
}
.pages-heading .page-links ul li .dp-menu li.active a {
  background-color: var(--td-primary);
  color: var(--td-heading);
}
.pages-heading .page-links ul li .dp-menu li:hover a {
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
}
.pages-heading .page-links ul li .dp-menu li a {
  font-size: 13px;
  font-weight: 700;
  border-radius: 8px;
  padding: 8px 10px;
  display: block;
}
.pages-heading .page-links > ul {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}
.pages-heading .page-links > ul > li.active > a,
.pages-heading .page-links > ul > li.active .link {
  color: var(--td-primary);
  text-decoration: underline;
}
.pages-heading .page-links > ul > li > a,
.pages-heading .page-links > ul > li .link {
  font-size: 14px;
  font-weight: 700;
  position: relative;
  padding: 8px 0;
  display: block;
}
.pages-heading .page-links > ul > li > a:hover,
.pages-heading .page-links > ul > li .link:hover {
  color: var(--td-primary);
  text-decoration: underline;
}
.pages-heading .page-links > ul > li:hover > ul {
  opacity: 1;
  pointer-events: all;
}
.pages-heading .page-links > ul > li:has(ul) > a::after {
  content: "";
  margin-inline-start: 6px;
  position: relative;
  display: inline-block;
  width: 7px;
  height: 7px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-right: 1.5px solid var(--td-text-primary);
  color: rgba(255, 255, 255, 0.5);
  top: calc(50% - 9px);
  transform: translateY(-50%) rotate(45deg);
}

.dashboard-middle-box {
  max-width: 606px;
  margin: 0 auto;
  background: #091628;
  border-radius: 16px;
  padding: 1.875rem 1.875rem;
  position: relative;
  z-index: 1;
}
@media (max-width: 575px) {
  .dashboard-middle-box {
    padding: 1.25rem 1.25rem;
  }
}

.gateway-steps-wrapper {
  margin-bottom: 30px;
}
.gateway-steps-wrapper .multi-steps {
  display: table;
  table-layout: fixed;
  width: 100%;
}
.gateway-steps-wrapper .multi-steps > li.is-active ~ li {
  color: #808080;
}
.gateway-steps-wrapper .multi-steps > li.is-active ~ li:before {
  content: counter(stepNum);
  font-family: inherit;
  font-weight: 700;
  background-color: #e1e1e1;
  border-color: #e1e1e1;
  color: #808080;
}
.gateway-steps-wrapper .multi-steps > li.is-active ~ li:after {
  background-color: #e1e1e1;
}
.gateway-steps-wrapper .multi-steps > li.is-active:before {
  content: counter(stepNum);
  font-family: inherit;
  font-weight: 700;
  background-color: #03A66D;
  border-color: #03A66D;
  color: var(--td-white);
  animation: pulse 2s infinite;
}
.gateway-steps-wrapper .multi-steps > li.is-active:after {
  background-color: #e1e1e1;
}
.gateway-steps-wrapper .multi-steps > li {
  counter-increment: stepNum;
  text-align: center;
  display: table-cell;
  position: relative;
  color: #03A66D;
  font-size: 16px;
  font-weight: 500;
}
@media (max-width: 575px) {
  .gateway-steps-wrapper .multi-steps > li {
    font-size: 14px;
  }
}
.gateway-steps-wrapper .multi-steps > li:before {
  content: "\f00c";
  content: "✓;";
  content: "𐀃";
  content: "𐀄";
  content: "✓";
  display: block;
  margin: 0 auto 4px;
  background-color: #03A66D;
  width: 24px;
  height: 24px;
  border-width: 2px;
  border-style: solid;
  border-color: #03A66D;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.625rem;
}
.gateway-steps-wrapper .multi-steps > li:last-child:after {
  display: none;
}
.gateway-steps-wrapper .is-complete {
  background: linear-gradient(to right, #03A66D 50%, #e1e1e1 50%);
  background-size: 200% 100%;
  background-position: right bottom;
  transition: all 0.5s ease-out;
}
.gateway-steps-wrapper .progress-bar {
  cursor: pointer;
  user-select: none;
  background-color: #e1e1e1;
  height: 2px;
  overflow: hidden;
  position: absolute;
  left: 50%;
  bottom: calc(50% + 15px);
  width: 100%;
  z-index: -1;
}
.gateway-steps-wrapper .progress-bar--success {
  background-color: #03A66D;
}
.gateway-steps-wrapper .progress-bar__bar {
  background-color: #1A2431;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: all 500ms ease-out;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(3, 166, 109, 0.4392156863);
  }
  100% {
    box-shadow: 0 0 0 10px rgba(3, 166, 109, 0);
  }
}
@keyframes nextStep {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
.payment-status-box {
  text-align: center;
  padding-top: 10px;
  margin-bottom: 2.1875rem;
}
.payment-status-box .payment-title {
  font-size: 1.875rem;
  margin-top: 0.625rem;
  margin-bottom: 0.375rem;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .payment-status-box .payment-title {
    font-size: 1.5rem;
  }
}
.payment-status-box .payment-description {
  font-size: 14px;
  font-weight: 500;
}
.payment-status-box .payment-description .btn-copy i {
  width: 16px;
  height: 17px;
  position: relative;
  top: 2px;
}

/*----------------------------------------*/
/*  Referral program styles
/*----------------------------------------*/
.referral-card {
  padding: 30px 30px;
  border-radius: 12px;
  justify-content: space-between;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  display: grid;
  align-items: end;
  grid-template-columns: 1fr 1px 490px;
  gap: 20px 60px;
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .referral-card {
    grid-template-columns: 1fr 1px 290px;
    gap: 20px 30px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .referral-card {
    grid-template-columns: 1fr;
    padding: 20px 20px;
  }
}
.referral-card .left-contents .title {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 3.75rem;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .referral-card .left-contents .title {
    margin-bottom: 1.875rem;
    font-size: 24px;
  }
}
.referral-card .left-contents .title span {
  color: var(--td-primary);
}
.referral-card .left-contents .ref-link-contents .ref-link {
  margin-bottom: 25px;
  font-size: 14px;
  font-weight: 700;
}
.referral-card .left-contents .ref-link-contents .link-box {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0px 0px 16px 0px;
}
.referral-card .left-contents .ref-link-contents .link-box .lint-text {
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 700;
  color: var(--td-white);
}
.referral-card .left-contents .ref-link-contents .link-box .copy-btn {
  cursor: pointer;
}
.referral-card .separator {
  height: 100%;
  width: 1px;
  background: linear-gradient(37deg, rgba(9, 22, 40, 0.1) 0%, rgb(166, 239, 103) 50%, rgba(9, 22, 40, 0.1) 100%);
  border-image-slice: 1;
  opacity: 0.5;
}
.referral-card .right-contents .share-text {
  font-size: 14px;
  font-weight: 700;
}
.referral-card .right-contents .social-icons {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem 0.5rem;
}
.referral-card .right-contents .social-icons a {
  width: 32px;
  height: 32px;
  text-align: center;
  font-size: 14px;
  border-radius: 6px;
  border-style: solid;
  border: 1px solid #bbbbbb;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.referral-card .right-contents .social-icons a:hover {
  background-color: var(--td-primary);
  color: var(--td-heading);
  border-color: var(--td-primary);
}
.referral-card .right-contents .social-icons a:hover svg * {
  fill: #1D1D1D;
}
.referral-card .illustration {
  display: inline-block;
  width: 203px;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: -1;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .referral-card .illustration {
    width: 100px;
  }
}

.referral-info-box {
  background: #091628;
  border-radius: 24px;
  padding: 30px 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .referral-info-box {
    padding: 20px 20px;
  }
}
.referral-info-box .heading .title {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}
.referral-info-box ul {
  margin-top: 16px;
}
.referral-info-box ul li {
  list-style: none;
}
.referral-info-box ul li:not(:last-child) {
  margin-bottom: 8px;
}
.referral-info-box ul li .list {
  display: flex;
  gap: 0.5rem;
}
.referral-info-box ul li .list .list-text {
  font-size: 16px;
  font-weight: 700;
}
.referral-info-box ul li .list .list-icon.success {
  color: var(--td-green);
}
.referral-info-box ul li .list .list-icon.danger {
  color: var(--td-danger);
}

/*----------------------------------------*/
/*  Wallets styles
/*----------------------------------------*/
.wallet-card-grid {
  display: grid;
  gap: 18px;
  align-items: center;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .wallet-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(256px, 1fr));
  }
}
@media (max-width: 575px) {
  .wallet-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
  }
}

.wallet-card {
  padding: 24px 24px 24px;
  border-radius: 15px;
  background: linear-gradient(98.99deg, rgba(59, 87, 231, 0.1) 0%, rgba(112, 135, 255, 0.1) 100%);
  position: relative;
  overflow: hidden;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1;
}
.wallet-card.active {
  background: linear-gradient(98.99deg, rgba(52, 97, 111, 0.98) 0%, rgb(166, 239, 103) 100%);
}
.wallet-card.active .wave {
  opacity: 80%;
}
.wallet-card .card-info {
  margin-bottom: 12px;
}
.wallet-card .card-info .currency-name {
  font-size: 20px;
  font-weight: bold;
}
.wallet-card p {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 2px;
  margin-bottom: 0;
  color: var(--td-white);
}
.wallet-card .balance {
  font-size: 20px;
  font-weight: 700;
  margin-top: 2px;
  color: var(--td-white);
}
.wallet-card .usd {
  font-size: 16px;
  font-weight: 700;
  color: var(--td-primary);
  margin-top: 8px;
}
.wallet-card .icon {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px;
  border-radius: 10px;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.04);
}
.wallet-card .icon img {
  width: 24px;
  height: 24px;
}
.wallet-card .wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  background: url(../images/bg/wallet-wave-bg.png);
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -1;
  opacity: 15%;
}

/*----------------------------------------*/
/*  P2p order styles
/*----------------------------------------*/
.p2p-order-area {
  background: #091628;
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 0px 0px 0px;
  position: relative;
}
@media (max-width: 575px) {
  .p2p-order-area {
    border-radius: 12px;
  }
}

.p2p-order-header {
  padding: 10px;
}
.p2p-order-header .top-contents {
  display: flex;
  gap: 16px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .p2p-order-header .top-contents {
    flex-direction: column;
  }
}

.buy-sell-box-tab {
  background: rgba(3, 166, 109, 0.06);
  border-radius: 12px;
  border: 1px solid #03a66d;
  padding: 6px;
  display: inline-flex;
  flex-direction: row;
  gap: 9px;
  align-items: center;
  justify-content: flex-start;
  flex: 0 0 auto;
  height: max-content;
  width: max-content;
}
.buy-sell-box-tab .nav-tabs .nav-item .nav-link {
  border: none;
  padding: 0px 30px;
  color: white;
  border-radius: 5px;
  cursor: pointer;
  height: 30px;
  background-color: transparent;
}
.buy-sell-box-tab .nav-tabs .nav-item .nav-link.active {
  background: var(--td-green);
}

.p2p-currency-options-tab .nav-tabs {
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 5px 8px 5px 8px;
  display: inline-flex;
  gap: 10px;
  align-items: flex-start;
  justify-content: start;
  position: relative;
}
.p2p-currency-options-tab .nav-tabs .nav-item .nav-link {
  border-radius: 5px;
  border: 1px solid #0e1f36;
  padding: 10px 16px 10px 16px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  height: 28px;
  font-size: 12px;
  font-weight: 700;
  color: #999;
}
.p2p-currency-options-tab .nav-tabs .nav-item .nav-link:hover {
  color: var(--td-primary);
  background: #142032;
}
.p2p-currency-options-tab .nav-tabs .nav-item .nav-link.active {
  color: var(--td-primary);
  background: #142032;
}

.p2p-order-filters {
  margin: 20px 0;
  display: grid;
  gap: 16px;
  align-items: center;
  grid-template-columns: repeat(auto-fit, minmax(236px, 1fr));
}

.filter-button {
  background: #091628;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-width: 1px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-button:hover {
  background-color: #1e2535;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.filter-options {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  background-color: #181f2f;
  border: 1px solid #1e2535;
  border-radius: 8px;
  width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 100;
  display: none;
}
.filter-options.active {
  display: block;
}
.filter-options .filter-header {
  padding: 12px 16px;
  border-bottom: 1px solid #1e2535;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.filter-options .close-button {
  background: none;
  border: none;
  color: #8b93a7;
  cursor: pointer;
  font-size: 18px;
}
.filter-options .filter-section {
  padding: 12px 16px;
  border-bottom: 1px solid #1e2535;
}
.filter-options .filter-section:last-child {
  border-bottom: none;
}
.filter-options .section-title {
  font-size: 14px;
  color: #8b93a7;
  margin-bottom: 12px;
}
.filter-options .option-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.filter-options .checkbox {
  width: 20px;
  height: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.filter-options .checkbox.selected {
  background-color: #3a6df0;
  border-color: #3a6df0;
}
.filter-options .checkbox.selected:after {
  content: "✓";
  color: white;
  font-size: 14px;
  width: 20px;
  height: 20px;
  left: 6px;
  display: inline-block;
  position: absolute;
  top: 0px;
}
.filter-options .range-slider {
  width: 100%;
  margin: 10px 0;
}
.filter-options .apply-button {
  background-color: #3a6df0;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  margin-top: 10px;
}
.filter-options .apply-button:hover {
  background-color: #2d5cd7;
}

.custom-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #ffffff;
  position: relative;
}
.custom-checkbox input {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #4A5568;
  border-radius: 4px;
  background: #1a1f2d;
  cursor: pointer;
  transition: all 0.3s;
}
.custom-checkbox input:checked {
  background: #22c55e;
  border-color: #22c55e;
}
.custom-checkbox input:checked::after {
  content: "✔";
  font-size: 14px;
  color: #ffffff;
  font-weight: bold;
  position: absolute;
  left: 4px;
  top: 1px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  color: #6d6b77;
  background-color: rgba(255, 255, 255, 0.08);
}

.select2-selection__choice {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  /* Adjust the width as needed */
  display: inline-block;
}

.select2-container .select2-search--inline .select2-search__field {
  height: 44px;
  line-height: 44px;
  margin-top: 0 !important;
}

.table-advertiser {
  display: flex;
  gap: 10px;
}
.table-advertiser .avatar {
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1C2C44;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
  color: var(--td-white);
}
.table-advertiser.is-online .avatar, .table-advertiser.is-offline .avatar {
  position: relative;
}
.table-advertiser.is-online .avatar::before, .table-advertiser.is-offline .avatar::before {
  position: absolute;
  content: "";
  width: 10px;
  height: 10px;
  background-color: var(--td-green);
  border-radius: 50%;
  bottom: 0;
  right: 0;
}
.table-advertiser.is-offline .avatar::before {
  background-color: #303d52;
}
.table-advertiser .name {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--td-white);
}
.table-advertiser .details {
  font-size: 13px;
  font-weight: 500;
  color: #bdbdbd;
  margin-bottom: 8px;
}
.table-advertiser .status {
  font-size: 13px;
  font-weight: 500;
}
.table-advertiser .online {
  color: #4caf50;
}
.table-advertiser .devider {
  color: #999999;
  opacity: 0.5;
}

.oder-status-wrapper {
  max-width: 606px;
  margin: 0 auto;
  border-radius: 24px;
  padding: 30px 30px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
@media (max-width: 575px) {
  .oder-status-wrapper {
    padding: 20px 20px;
  }
}
.oder-status-wrapper .review-box {
  margin-top: 0;
}
.oder-status-wrapper .review-box .review-table {
  background: #0e1b2c;
  border-radius: 30px;
  padding: 18px;
}
.oder-status-wrapper .review-box .review-table .review-btn {
  font-size: 16px;
  font-weight: 700;
}
.oder-status-wrapper .review-box .review-table .has-border {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px;
}

/*----------------------------------------*/
/*  P2p works styles
/*----------------------------------------*/
.p2p-works-area {
  background: linear-gradient(90deg, rgba(14, 27, 44, 0.5) 0%, rgba(14, 27, 44, 0) 50.9469032288%, rgba(14, 27, 44, 0.5) 100%);
  border-radius: 24px;
  position: relative;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  padding: 100px 30px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .p2p-works-area {
    padding: 60px 30px;
  }
}
@media (max-width: 575px) {
  .p2p-works-area {
    padding: 60px 15px;
  }
}

.p2p-works-wrapper {
  max-width: 1320px;
  margin: 0 auto;
}

.p2p-works-grid {
  display: flex;
  grid-template-columns: repeat(2, 1fr);
  justify-content: space-between;
  border-radius: 40px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.1);
  border-width: 1px;
  padding: 50px;
  row-gap: 30px;
  column-gap: 30px;
}
@media (max-width: 575px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .p2p-works-grid {
    padding: 30px 30px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .p2p-works-grid {
    display: grid;
  }
}
@media (max-width: 575px) {
  .p2p-works-grid {
    grid-template-columns: 1fr;
  }
}

.p2p-works-item {
  text-align: center;
  max-width: 260px;
}
@media (max-width: 575px) {
  .p2p-works-item {
    margin: auto;
    width: 100%;
  }
}
.p2p-works-item:not(:last-child) {
  position: relative;
}
.p2p-works-item:not(:last-child)::before {
  position: absolute;
  content: "";
  height: 75px;
  width: 1px;
  right: -40%;
  background: rgba(255, 255, 255, 0.1);
  top: 50%;
  transform: translateY(-50%);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .p2p-works-item:not(:last-child)::before {
    right: -20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .p2p-works-item:not(:last-child)::before {
    right: -40px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .p2p-works-item:not(:last-child)::before {
    display: none;
  }
}
.p2p-works-item .icon {
  margin-bottom: 1.25rem;
}
.p2p-works-item .icon img {
  width: 3.75rem;
  height: 3.75rem;
}
.p2p-works-item .contents .title {
  font-size: 20px;
  font-weight: 700;
}
.p2p-works-item .contents .description {
  margin-top: 0.5rem;
}

/*----------------------------------------*/
/*  Payment method styles
/*----------------------------------------*/
.payment-method-info {
  display: inline-grid;
  align-items: center;
  grid-template-columns: auto 1px 1fr;
  gap: 30px;
  background: #091628;
  padding: 25px 320px 25px 18px;
  border-radius: 16px;
  overflow: hidden;
  background-image: url(../images/bg/payment-info-bg.png);
  background-repeat: no-repeat;
  background-position: right bottom;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .payment-method-info {
    padding: 25px 200px 25px 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .payment-method-info {
    display: grid;
    padding: 25px 100px 25px 18px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .payment-method-info {
    display: grid;
    padding: 25px 18px 25px 18px;
    grid-template-columns: 1fr;
  }
}
.payment-method-info .user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  width: max-content;
}
.payment-method-info .user-info .avatar {
  background: #1c2c44;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  font-size: 20px;
  font-weight: 700;
  color: var(--td-white);
}
.payment-method-info .user-info .avatar .contents .name {
  font-size: 20px;
  font-weight: 700;
}
.payment-method-info .devider {
  position: relative;
  height: 100%;
}
@media (max-width: 575px) {
  .payment-method-info .devider {
    height: 1px;
    width: 100%;
  }
}
.payment-method-info .devider:before {
  position: absolute;
  content: "";
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(90deg, rgba(9, 22, 40, 0.1) 0%, rgb(166, 239, 103) 50%, rgba(9, 22, 40, 0.1) 100%);
  border-image-slice: 1;
  opacity: 0.5;
  width: 100px;
  height: 0px;
  transform-origin: 0 0;
  transform: rotate(90deg);
  top: -25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .payment-method-info .devider:before {
    width: 100%;
    height: 0px;
    transform-origin: 0 0;
    transform: rotate(90deg);
    top: 50%;
    transform: translateY(-50%);
  }
}
.payment-method-info .payment-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}
.payment-method-info .payment-status span {
  background: rgba(153, 153, 153, 0.11);
  border-radius: 12px;
  padding: 0px 16px 0px 16px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 700;
}
.payment-method-info .payment-status span.active {
  color: var(--td-white);
}
.payment-method-info .payment-status span.active i {
  color: var(--td-green);
}

.payment-method-stats {
  display: grid;
  align-items: center;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
}
.payment-method-stats .stat-box {
  background: #091628;
  border-radius: 12px;
  padding: 18px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-image: url(../images/bg/payment-stats-bg.png);
  background-repeat: no-repeat;
  background-size: cover;
}
.payment-method-stats .stat-box::before {
  position: absolute;
  content: "";
  background: #1f4a55;
  border-radius: 50%;
  flex-shrink: 0;
  width: 127px;
  height: 128px;
  filter: blur(70px);
  right: -30px;
  top: 20px;
}
.payment-method-stats .stat-box .icon {
  display: flex;
  align-items: center;
  border-radius: 50%;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: #131F31;
  border-radius: 16px;
}
.payment-method-stats .stat-box .contents span {
  font-weight: 700;
  display: inline-block;
  margin-bottom: 0.875rem;
}
.payment-method-stats .stat-box .contents h4 {
  font-size: 1.5rem;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .payment-method-stats .stat-box .contents h4 {
    font-size: 20px;
  }
}

.add-payment-method-card {
  background: #0e1b2c;
  border-radius: 30px;
  padding: 30px 30px;
  width: 513px;
  margin: 0 auto;
}
@media (max-width: 575px) {
  .add-payment-method-card {
    width: 100%;
    padding: 20px 20px;
  }
}
.add-payment-method-card .heading {
  margin-bottom: 16px;
}
.add-payment-method-card .heading .title {
  font-size: 16px;
  line-height: 12px;
  font-weight: 700;
}
.add-payment-method-card .method {
  display: flex;
  gap: 8px 20px;
  margin-bottom: 18px;
  flex-wrap: wrap;
}
.add-payment-method-card .input-group {
  margin-bottom: 18px;
}
.add-payment-method-card .input-row {
  display: flex;
  gap: 0 30px;
}
.add-payment-method-card .buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  gap: 18px;
}

.payment-options-wrapper .payment-attention-text {
  margin-top: 22px;
  font-size: 14px;
  letter-spacing: 0.03em;
  font-weight: 700;
}
.payment-options-wrapper .payment-attention-text span {
  color: #eb4e5c;
}
.payment-options-wrapper .method-list {
  margin-top: 10px;
}
.payment-options-wrapper .method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: transparent;
  padding: 0 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  font-weight: 700;
  height: 50px;
}
.payment-options-wrapper .method:not(:last-child) {
  margin-bottom: 16px;
}
.payment-options-wrapper .method:hover {
  border-color: rgba(166, 239, 103, 0.3);
}
.payment-options-wrapper .method input {
  display: none;
}
.payment-options-wrapper .method.selected {
  border-color: rgba(166, 239, 103, 0.3);
  color: var(--td-white);
}
.payment-options-wrapper .method.selected .custom-checkbox {
  border-color: var(--td-primary);
  background-color: var(--td-primary);
}
.payment-options-wrapper .method.selected .custom-checkbox::after {
  content: "✔";
  font-size: 12px;
  color: var(--td-black);
}
.payment-options-wrapper .custom-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: 0.3s;
  position: relative;
}
.payment-options-wrapper .custom-checkbox::before {
  position: absolute;
  content: "";
  width: 100%;
  top: 50%;
  left: -12px;
  height: 22px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateY(-50%);
}

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/
.multi-steps-wrapper {
  display: table;
  table-layout: fixed;
  width: 100%;
  padding-top: 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .multi-steps-wrapper {
    display: flex;
    table-layout: fixed;
    width: 100%;
    padding-top: 50px;
    flex-direction: column;
    gap: 50px;
  }
}
.multi-steps-wrapper .step {
  text-align: center;
  display: table-cell;
  position: relative;
}
.multi-steps-wrapper .step.active .step-line {
  background-color: var(--td-primary);
}
.multi-steps-wrapper .step.active .step-button {
  border: 1px solid #2c4635;
  color: var(--td-white);
}
.multi-steps-wrapper .step.active .step-button .arrow {
  display: block;
}
.multi-steps-wrapper .step.active .step-button::before {
  background-color: var(--td-primary);
}
.multi-steps-wrapper .step-line {
  cursor: pointer;
  user-select: none;
  background-color: rgba(255, 255, 255, 0.1);
  height: 2px;
  position: absolute;
  bottom: calc(50% + 0px);
  width: 100%;
  z-index: -1;
  left: 0;
}
.multi-steps-wrapper .step-button {
  background-color: #0E1B2C;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  position: relative;
  top: -48px;
  font-size: 14px;
  font-weight: 700;
  color: var(--td-text-primary);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .multi-steps-wrapper .step-button {
    font-size: 14px;
    padding: 10px 10px;
  }
}
.multi-steps-wrapper .step-button .arrow {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
  display: none;
}
.multi-steps-wrapper .step-button .arrow::after {
  width: 20px;
  height: 20px;
  content: "";
  bottom: 0px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background-color: #0e1b2c;
  z-index: 1;
}
.multi-steps-wrapper .step-button .arrow::before {
  position: absolute;
  content: "";
  background-image: url(../images/icons/border-arrow-down.svg);
  bottom: -9px;
  background-repeat: no-repeat;
  width: 18px;
  height: 19px;
  left: 50%;
  transform: translateX(-50%);
  background-size: cover;
  background-position: center;
  z-index: -5;
}
.multi-steps-wrapper .step-button::after {
  width: 0;
  height: 0;
  border-inline-start: 7px solid transparent;
  border-inline-end: 7px solid transparent;
  border-top: 7px solid #0e1b2c;
  content: "";
  bottom: -7px;
  position: absolute;
  left: 50%;
  z-index: 1;
  transform: translateX(-50%);
}
.multi-steps-wrapper .step-button::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  background-color: var(--td-text-primary);
  z-index: 1;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 50%;
}

.post-trade-form-box {
  background: #0e1b2c;
  border-radius: 16px;
  max-width: 666px;
  padding: 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .post-trade-form-box {
    max-width: 100%;
  }
}
.post-trade-form-box .price-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 18px;
  margin-top: 30px;
  margin-bottom: 40px;
}
.post-trade-form-box .price-details div {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 8px;
  padding: 7px 16px 7px;
  min-width: 160px;
}
.post-trade-form-box .price-details div label {
  font-size: 14px;
  font-weight: 700;
  display: block;
  margin-bottom: 5px;
}
.post-trade-form-box .price-details div .currency {
  color: var(--td-white);
  font-size: 14px;
  font-weight: 700;
}
.post-trade-form-box .payment-contents h6 {
  font-size: 14px;
}
.post-trade-form-box .payment-contents span {
  font-size: 12px;
}
@media (max-width: 480px) {
  .post-trade-form-box .payment-cale {
    flex-direction: column;
    text-align: center;
  }
  .post-trade-form-box .payment-cale .approximately-equal {
    margin-top: 0 !important;
  }
}

.post-buy-sell-tabs .td-tab .nav-tabs {
  display: flex;
  gap: 16px 20px;
  flex-wrap: wrap;
}
.post-buy-sell-tabs .td-tab .nav-tabs .nav-link {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 8px;
  padding: 0px 16px 0px 16px;
  font-size: 16px;
  font-weight: 700;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--td-text-primary);
  position: relative;
}
.post-buy-sell-tabs .td-tab .nav-tabs .nav-link::before {
  position: absolute;
  content: "";
  width: 22px;
  height: 2px;
  background-color: var(--td-text-primary);
  bottom: 10px;
  border-radius: 30px;
}
.post-buy-sell-tabs .td-tab .nav-tabs .nav-link.active {
  color: var(--td-primary);
}
.post-buy-sell-tabs .td-tab .nav-tabs .nav-link.active::before {
  background-color: var(--td-primary);
}

/*----------------------------------------*/
/* Trading View styles
/*----------------------------------------*/
.trading-view-glow .glow-one {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.trading-view-glow .glow-two {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
}

.support-currency {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 50px;
}

.trading-view-intro .thumb {
  background-color: #0E1F37;
  padding: 30px;
  border-radius: 30px;
  position: relative;
  z-index: 1;
}
.trading-view-intro .thumb::before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  padding: 5px;
  background: linear-gradient(180deg, #425D82 0%, #0E141C 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}

.trading-view-intro-two {
  margin-right: -78%;
  padding-right: 120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .trading-view-intro-two {
    margin-right: -39%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .trading-view-intro-two {
    padding-right: 0;
  }
}
@media (max-width: 575px) {
  .trading-view-intro-two {
    margin-right: 0;
    padding-right: 0;
  }
}
.trading-view-intro-two .thumb img {
  width: 100%;
}

/*----------------------------------------*/
/*  Why choose styles
/*----------------------------------------*/
.why-choose-shapes .shape-one {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}

.why-choose-info-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.why-choose-info-card {
  background: #0A1729;
  padding: 35px 35px 100px;
  border-radius: 16px;
  position: relative;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  transition: all 0.5s ease-in-out;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .why-choose-info-card {
    padding: 20px 20px 60px;
  }
}
.why-choose-info-card:hover {
  transform: scale(1.03);
}
.why-choose-info-card .contents .subtitle {
  font-weight: 700;
  color: #A6EF67;
}
@media (max-width: 575px) {
  .why-choose-info-card .contents .subtitle {
    font-size: 14px;
  }
}
.why-choose-info-card .contents > p {
  margin-bottom: 8px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-info-card .contents h1 {
    font-size: 32px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .why-choose-info-card .contents h1 {
    font-size: 28px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .why-choose-info-card .contents h1 {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  .why-choose-info-card .contents h1 {
    font-size: 24px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .why-choose-info-card .contents h2 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .why-choose-info-card .contents h2 {
    font-size: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .why-choose-info-card .contents h2 {
    font-size: 26px;
  }
}
@media (max-width: 575px) {
  .why-choose-info-card .contents h2 {
    font-size: 24px;
  }
}
.why-choose-info-card .contents h1,
.why-choose-info-card .contents h2,
.why-choose-info-card .contents h3,
.why-choose-info-card .contents h4,
.why-choose-info-card .contents h5,
.why-choose-info-card .contents h6 {
  margin-bottom: 15px;
}
.why-choose-info-card .contents h4 {
  font-size: 30px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .why-choose-info-card .contents h4 {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .why-choose-info-card .contents h4 {
    font-size: 20px;
  }
}
.why-choose-info-card.large .contents {
  max-width: 570px;
}

/*----------------------------------------*/
/* Powerful tool styles
/*----------------------------------------*/
.powerful-tools {
  background-color: #0A1729;
  padding: 20px 20px;
  border-radius: 16px;
  display: flex;
  align-items: self-start;
  gap: 18px;
  height: 100%;
}
.powerful-tools .icon {
  flex: 0 0 auto;
}
.powerful-tools .icon img {
  width: 2.5rem;
  height: 2.5rem;
}
.powerful-tools .contents .title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}
@media (max-width: 575px) {
  .powerful-tools .contents .title {
    font-size: 1.125rem;
  }
}
.powerful-tools.style-two {
  display: flex;
  flex-direction: column;
  background-color: #0F0F0F;
  margin-bottom: 30px;
  height: max-content;
}

.powerful-tools-items-2 {
  margin-top: 80px;
}
.powerful-tools-items-2 .row [class*=col]:nth-child(2n) .powerful-tools {
  margin-top: -30px;
}
.powerful-tools-items-2 .row [class*=col]:nth-child(5) .powerful-tools {
  margin-top: -60px;
}

/*----------------------------------------*/
/*  Counter styles
/*----------------------------------------*/
.counter-grid {
  display: grid;
  grid-template-columns: auto auto auto auto;
  justify-content: space-between;
  gap: 30px 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .counter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .counter-grid {
    grid-template-columns: 1fr;
  }
}

.single-counter-item {
  text-align: center;
  position: relative;
}
.single-counter-item:not(:last-child)::before {
  position: absolute;
  content: "";
  height: 40px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  top: 50%;
  transform: translateY(-50%);
  right: -120px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .single-counter-item:not(:last-child)::before {
    right: -68px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-counter-item:not(:last-child)::before {
    right: -60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .single-counter-item:not(:last-child)::before {
    right: -22px;
  }
}
@media (max-width: 575px) {
  .single-counter-item:not(:last-child)::before {
    display: none;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .single-counter-item:nth-child(2)::before {
    display: none;
  }
}
.single-counter-item .icon {
  margin-bottom: 20px;
}
.single-counter-item .icon img {
  width: 50px;
}
.single-counter-item .content .description {
  font-size: 18px;
  font-weight: 700;
  color: var(--td-white);
  margin-bottom: 10px;
}
.single-counter-item .content .title,
.single-counter-item .content span {
  font-family: var(--td-ff-body);
  font-size: 30px;
  color: var(--td-primary);
  font-weight: 700;
  line-height: 1;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .single-counter-item .content .title,
  .single-counter-item .content span {
    font-size: 26px;
  }
}
.single-counter-item.style-two .content .title,
.single-counter-item.style-two .content span {
  font-size: 20px;
}
.single-counter-item.style-two .content .description {
  font-size: 16px;
  font-weight: 500;
  color: #999;
  margin-bottom: 0;
  margin-top: 5px;
}

/*----------------------------------------*/
/*  how it work styles
/*----------------------------------------*/
.how-it-work-shapes .shape-one {
  position: absolute;
  bottom: 90px;
  right: 100px;
}
.how-it-work-shapes .shape-two {
  position: absolute;
  top: 20px;
  left: 30px;
}
.how-it-work-shapes .shape-three {
  position: absolute;
  right: 15%;
  top: 70px;
}

.how-it-work {
  padding: 0px 50px;
  position: relative;
  z-index: 1;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .how-it-work {
    padding: 0;
  }
}
.how-it-work::before {
  position: absolute;
  content: "";
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  width: 320px;
  height: 1px;
  top: 40px;
  left: 65%;
  z-index: -1;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .how-it-work::before {
    width: 270px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .how-it-work::before {
    width: 220px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .how-it-work::before {
    display: none;
  }
}
.how-it-work:nth-child(3)::before {
  display: none;
}
.how-it-work .icon {
  margin-bottom: 25px;
}
.how-it-work .icon img {
  width: 80px;
  height: 80px;
}
.how-it-work .contents .title {
  font-size: 20px;
  font-weight: 700;
  color: var(--td-white);
  margin-bottom: 10px;
}
@media (max-width: 575px) {
  .how-it-work .contents .title {
    font-size: 18px;
  }
}

.row [class*=col-]:nth-child(3) .how-it-work::before, .row [class*=col-]:nth-child(6) .how-it-work::before, .row [class*=col-]:nth-child(3) .how-it-work::before, .row [class*=col-]:last-child .how-it-work::before {
  display: none;
}

.how-it-work-contents-two {
  padding-left: 140px;
  padding-right: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .how-it-work-contents-two {
    padding-left: 60px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .how-it-work-contents-two {
    padding-left: 0;
    padding-right: 0;
  }
}

.how-it-work-grid {
  display: flex;
  flex-direction: column;
  row-gap: 76px;
}

.how-it-work-two {
  display: flex;
  align-items: center;
  gap: 24px;
}
@media (max-width: 575px) {
  .how-it-work-two {
    gap: 18px;
  }
}
.how-it-work-two:not(:last-child) {
  position: relative;
}
.how-it-work-two:not(:last-child)::before {
  position: absolute;
  content: "";
  height: 40px;
  width: 40px;
  background-image: url(../images/how-it-work/down-arrow.png);
  background-repeat: no-repeat;
  top: calc(100% + 16px);
  left: 30px;
}
.how-it-work-two .icon {
  border: 1px solid #0E1F37;
  border-radius: 50%;
  padding: 6px;
}
.how-it-work-two .contents .title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 2px;
}
.how-it-work-two .contents .description {
  font-size: 14px;
  font-weight: 500;
}

.how-it-work-shaspes-two .shape-one {
  position: absolute;
  bottom: -100px;
  width: 396px;
  left: 0;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .how-it-work-shaspes-two .shape-one {
    width: 280px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .how-it-work-shaspes-two .shape-one {
    width: 230px;
  }
}

/*----------------------------------------*/
/*  Balance styles
/*----------------------------------------*/
.app-download-contents {
  margin-left: 110px;
  margin-right: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .app-download-contents {
    margin-left: 0;
    margin-right: 0;
  }
}
.app-download-contents .app-download-buttons {
  display: flex;
  padding: 8px 16px;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
}
.app-download-contents .app-download-buttons .app-download-btn {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: #0A1728;
  display: flex;
  padding: 6px 16px;
  align-items: center;
  gap: 8px;
}
.app-download-contents .app-download-buttons .app-download-btn .contents {
  display: flex;
  flex-direction: column;
}
.app-download-contents .app-download-buttons .app-download-btn .contents span {
  font-size: 12px;
  color: #999999;
  font-weight: 700;
}
.app-download-contents .app-download-buttons .app-download-btn .contents h6 {
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
}
.app-download-contents .app-download-buttons .app-download-btn .icon {
  width: 24px;
  flex: 0 0 auto;
}

.app-download-thumb {
  margin-left: 65px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .app-download-thumb {
    margin: 0 auto;
    text-align: center;
  }
}

.app-download-demo-2 {
  padding: 0 12px;
}

.app-download-section.section-two {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: left bottom 68%;
}

/*# sourceMappingURL=styles.css.map */
