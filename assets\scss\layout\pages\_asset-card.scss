@use '../../utils' as *;

/*----------------------------------------*/
/*  Asset card styles
/*----------------------------------------*/
.trade-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px 8px;
    border-bottom: 1px solid hsla(0, 0%, 100%, .1);
    min-height: 41px;

    .title {
        font-size: 14px;
        font-weight: 700;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #aaa;
        cursor: pointer;
        transition: color 0.3s;
    }
}

.asset-card-inner {
    .account-section {
        padding: 13px 8px 13px;

        &:last-child {
            border-bottom: 0;
        }
    }

    .divider-line {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .account-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        span {
            font-size: 14px;
            font-weight: 700;
            color: #999999;
        }

        i {
            cursor: pointer;
        }
    }

    .account-details {
        display: grid;
        grid-template-columns: auto auto;
        gap: 8px;
        font-size: 12px;
        font-weight: 500;
        color: var(--td-white);
    }

    .balance {
        text-align: right;
        color: #aaa;
    }

    .button-group {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding: 0 8px;
    }

    .deposit-btn {
        flex: 1;
        padding: 10px;
        font-size: 14px;
        font-weight: 700;
        cursor: pointer;
        transition: .3s;
        color: #ccc;
        margin-right: 8px;
        background: #010c1a;
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.16);

        &:hover {
            background: rgba(255, 255, 255, 0.1);
        }
    }

    .transfer-btn {
        flex: 1;
        padding: 10px;
        font-size: 14px;
        font-weight: bold;
        border-radius: 6px;
        cursor: pointer;
        transition: .3s;
        background: #A6EF67;
        border: none;
        color: #1D1D1D;

        &:hover {
            background: #83d93e;
        }
    }
}