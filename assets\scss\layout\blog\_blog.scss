@use '../../utils' as *;

/*----------------------------------------*/
/*  Blog styles
/*----------------------------------------*/
.single-blog-item {
    position: relative;

    &:hover {
        .blog-thumb {
            img {
                transform: scale(1.1);
            }
        }
    }

    .blog-thumb {
        overflow: hidden;
        border-radius: 16px;

        img {
            width: 100%;
        }
    }

    .blog-contents {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 18px 20px;
        background: rgba(0, 0, 0, 0.13);
        backdrop-filter: blur(11px);
        border-radius: 0px 0px 16px 16px;

        @media #{$xxs} {
            padding: 16px 16px;
        }

        .blog-title {
            font-size: 20px;

            @media #{$xxs,$lg} {
                font-size: 18px;
            }
        }

        .blog-tag {
            display: inline-flex;
            height: 30px;
            padding: 6px 16px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.13);
            background: rgba(255, 255, 255, 0.04);
            color: var(--td-white);
            margin-bottom: 8px
        }
    }
}

// Inner pages shapes
.inner-pages-shapes {
    .glow-two {
        position: absolute;
        right: 0;
        top: 0;
        z-index: -1;
    }

    .glow-one {
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
    }

    .shape-one {
        position: absolute;
        top: 26%;
        left: 0;
        z-index: -1;
    }
}