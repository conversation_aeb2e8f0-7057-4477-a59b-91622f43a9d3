@use '../../utils' as *;

/*----------------------------------------*/
/*  KYC Styles 
/*----------------------------------------*/



.verification-inner-contents {
    padding: 30px 30px;
}

.identity-alert-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.kyc-card {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 8px;

    &:not(:last-child) {
        margin-bottom: 15px;
    }

    @media #{$xs} {
        flex-direction: column;
        align-items: start;
        gap: 12px;
    }

    .status-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 50px;
        height: 50px;
        background-color: var(--td-white);
        border-radius: 50%;
        margin-right: 15px;
        flex: 0 0 auto;

        .icon {
            font-size: 1.5rem;
        }
    }

    .details {
        .label {
            font-weight: bold;
            color: #ccc;
        }

        .status {
            display: inline-block;
            padding: 2px 16px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 14px;
        }

        .view-details {
            color: #3a82e1;
            text-decoration: none;
            font-size: 0.9rem;

            &:hover {
                text-decoration: underline;
            }
        }

        .submission-date {
            font-size: 0.85rem;
            color: #aaa;
            margin-top: 5px;
        }

        .details-info {
            display: flex;
            align-items: center;
            gap: 12px 12px;
            flex-wrap: wrap;
        }
    }

    &.rejected {
        background: rgba(255, 46, 55, 0.04);
        border: 2px dashed rgba(255, 46, 55, 0.2);
        border-radius: 16px;

        .status-icon {
            .icon {
                color: #b53039;
            }
        }

        .details {
            .status {
                background-color: #b53039;
                color: var(--td-white);
            }
        }
    }

    &.success {
        background: rgba(0, 204, 0, 0.02);
        border: 2px dashed rgba(0, 204, 0, 0.2);
        border-radius: 16px;

        .status-icon {
            .icon {
                color: #2f7d4f;
            }
        }

        .details {
            .status {
                background-color: #2f7d4f;
                color: var(--td-white);
            }
        }
    }
}