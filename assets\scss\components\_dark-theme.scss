@use "../utils" as *;

/*----------------------------------------*/
/*  Dark Themes
/*----------------------------------------*/
body {
    &.dark-theme {
        background-color: #010C1A;
    }
}

// .dark-theme {

//     // Typography scss
//     h1,
//     h2,
//     h3,
//     h4,
//     h5,
//     h6 {
//         color: var(--td-white);
//     }

//     p {
//         color: rgba($white, $alpha: 0.75);
//     }

//     .td-btn {
//         &.btn-gray {
//             background: rgba($white, $alpha: 0.2);
//             border-color: rgba($white, $alpha: 0.3);
//             color: var(--td-white);

//             svg {
//                 * {
//                     stroke: var(--td-white);
//                 }
//             }
//         }
//     }

//     .td-single-select {
//         .nice-select {
//             color: rgba($white, $alpha: 0.50);
//             border-color: rgba($white, $alpha: 0.30);

//             &::after {
//                 border-color: rgba($white, $alpha: 0.50);
//             }

//             .current {
//                 color: var(--td-white);
//             }

//             .list {
//                 border-color: rgba($white, $alpha: 0.30);
//                 background-color: rgba($deep-black, $alpha: 0.80);
//             }

//             .option {

//                 &:hover {
//                     background-color: #484A4C;
//                     color: var(--td-white);
//                 }

//                 &.focus {
//                     background-color: #484A4C;
//                     color: var(--td-white);
//                 }

//                 &.selected {
//                     .focus {
//                         background-color: #484A4C;
//                         color: var(--td-white);
//                     }
//                 }
//             }
//         }
//     }

//     // search filter
//     .search-container {
//         position: relative;

//         .input-search {
//             .form-control {
//                 color: var(--td-white);
//                 background: rgba($white, $alpha: 0.02);
//                 border-color: rgba($gray, $alpha: 0.1);
//             }
//         }

//         .search-results {
//             background: var(--td-deep-black);
//             border-color: rgba(255, 255, 255, 0.3);
//             padding: 2px 6px 8px 6px;
//             box-shadow: 0px 2px 8px 0px rgba(255, 255, 255, 0.2);

//             li {
//                 color: var(--td-white);

//                 &:hover {
//                     background-color: rgba($white, $alpha: 0.2);
//                     color: var(--td-white);

//                     span {
//                         svg * {
//                             stroke: var(--td-white);
//                         }
//                     }
//                 }

//                 span {
//                     svg * {
//                         stroke: var(--td-white);
//                     }
//                 }
//             }
//         }

//         .results-title {
//             color: var(--td-white);

//             span {
//                 background-color: var(--td-white);
//                 color: var(--td-heading);
//             }
//         }

//     }

//     .buttons-dropdown-menu {
//         border-color: rgba(255, 255, 255, 0.3);
//         background: var(--td-deep-black);

//         li {
//             span {
//                 svg * {
//                     stroke: var(--td-white);
//                 }
//             }

//             &:hover {
//                 background-color: rgba($white, $alpha: 0.3);
//                 color: var(--td-white);
//             }
//         }
//     }

//     .language-nav {
//         border-color: rgba($white, $alpha: 0.1);

//         .translate_wrapper {
//             &.active {
//                 .more_lang {
//                     background: var(--td-deep-black);
//                     border-color: rgba(171, 178, 225, 0.3);

//                     .lang:not(:last-child) {
//                         border-color: rgba(171, 178, 225, 0.3);
//                     }

//                     &::before {
//                         border-bottom: 7px solid rgba(171, 178, 225, 0.3);
//                     }

//                     &::after {
//                         border-bottom: 7px solid rgba(171, 178, 225, 0.3);
//                     }
//                 }
//             }
//         }
//     }

//     // user profile
//     .user-profile-drop {
//         .dropdown-menu {
//             border-color: rgba(171, 178, 225, 0.3);
//             background: var(--td-deep-black);
//         }

//         .dropdown-info-list {
//             ul {
//                 li {
//                     a {
//                         .content {
//                             color: rgba($white, $alpha: 0.7);
//                         }

//                         .icon {
//                             svg * {
//                                 stroke: var(--td-white);
//                             }
//                         }

//                         &:hover {
//                             .content {
//                                 background-color: rgba($white, $alpha: 0.2);
//                                 color: var(--td-white);
//                             }

//                             .icon {
//                                 svg * {
//                                     color: var(--td-white);
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }

//     // Notification Styles
//     .notification-panel {
//         background: var(--td-deep-black);
//         border-color: rgba(171, 178, 225, 0.3);
//     }

//     .notification-item {
//         border-color: rgba(171, 178, 225, 0.3);

//         &.active {
//             background-color: rgba($primary, $alpha: 0.6);

//         }

//         .contents-box {
//             .content {
//                 .title {
//                     color: var(--td-white);

//                     strong {
//                         color: var(--td-white);
//                     }
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /* Buttons styles
//     /*----------------------------------------*/
//     .td-btn {
//         &.outline-black-btn {
//             border-color: rgba($white, $alpha: .2);
//             background-color: rgba($white, $alpha: .2);
//             color: rgba($white, $alpha: .6);
//         }
//     }

//     // upload-btn
//     .upload-btn {
//         .upload-label {
//             background-color: rgba($white, $alpha: 0.1);
//             border-color: rgba($white, $alpha: 0.12);
//             color: var(--td-white);

//             svg * {
//                 stroke: var(--td-white);
//             }

//             &:hover {
//                 background-color: rgba($white, $alpha: 0.2);
//                 border-color: rgba($white, $alpha: 0.1);
//             }
//         }
//     }


//     /*----------------------------------------*/
//     /*  AI output styles
//     /*----------------------------------------*/
//     .ai-content-box {
//         border-color: rgba($white, $alpha: 0.1);

//         .right-contents {
//             .article-contents {
//                 h5 {
//                     color: var(--td-white);
//                 }
//             }
//         }
//     }

//     .ai-templates-contents {
//         .list-items {
//             li {
//                 color: rgba($white, $alpha: .7);
//             }
//         }
//     }

//     .ai-untitled-filter {
//         .filter-option {
//             background: rgba($primary-alt, $alpha: 0.2);
//             color: var(--td-white);
//         }
//     }

//     // ai quick actions
//     .ai-quick-actions {
//         .action-btns {
//             button {
//                 border-color: rgba($white, $alpha: 0.3);

//                 &:hover {
//                     border-color: rgba($white, $alpha: 0.3);
//                     background-color: #3E4041;
//                 }
//             }
//         }

//         .language-switcher {
//             background-color: #181B1D;
//         }
//     }

//     /*----------------------------------------*/
//     /*  Chats style
//     /*----------------------------------------*/
//     .ai-chat-body {
//         .bot-message {
//             .contents {
//                 background: rgba($primary-alt, $alpha: 0.4);

//                 p {
//                     color: var(--td-white);
//                 }

//                 .message-time {
//                     color: rgba($white, $alpha: 0.7);
//                 }
//             }
//         }
//     }

//     .ai-chats-history {
//         .history-item {
//             background-color: rgba($primary-alt, $alpha: .2);

//             .action-contents {
//                 span {
//                     color: rgba($white, $alpha: 0.7);
//                 }
//             }
//         }
//     }

//     .ai-chat-footer {
//         .message-box {
//             &.has-gray {
//                 background: rgba($white, $alpha: 0.1);
//                 border-color: rgba($white, $alpha: 0.12);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  ai music css
//     /*----------------------------------------*/
//     .ai-music-content {
//         background-color: rgba($primary-alt, $alpha: 0.4);
//     }

//     .audio-body {
//         .audio-controls {

//             .audio-time,
//             .audio-duration {
//                 color: var(--td-white);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /* Forms styles
//     /*----------------------------------------*/
//     .form-select {
//         --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23dee2e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
//     }

//     .td-form-group {
//         .input-field {
//             .form-control {
//                 &:disabled {
//                     background-color: var(--td-heading);
//                     opacity: 1;
//                     color: rgba($white, $alpha: .3);
//                 }
//             }
//         }
//     }

//     .td-form-group {
//         .input-label {
//             color: var(--td-white);
//         }

//         select {
//             background-color: rgba(255, 255, 255, 0.1);
//             border: 1px solid rgba(255, 255, 255, 0.2);
//             color: var(--td-white);
//             --bs-form-select-bg-img: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23dee2e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e);

//             option {
//                 background-color: #1c1c1c;
//                 /* Dark background for options */
//                 color: var(--td-white);
//             }
//         }

//         &.input-fill {
//             input {
//                 background: rgba(255, 255, 255, 0.1);
//                 border-color: rgba(255, 255, 255, 0.2);
//                 color: var(--td-white);

//                 @include td-placeholder {
//                     color: var(--td-white);
//                 }
//             }

//             select {
//                 background-color: rgba(255, 255, 255, 0.1);
//                 border: 1px solid rgba(255, 255, 255, 0.2);
//                 color: var(--td-white);
//                 --bs-form-select-bg-img: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23dee2e6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e);

//                 option {
//                     background-color: #1c1c1c;
//                     /* Dark background for options */
//                     color: var(--td-white);
//                 }
//             }

//             textarea {
//                 background-color: #FCFCFC;
//                 border: 1px solid rgba(8, 8, 8, 0.2);
//                 background: rgba(255, 255, 255, 0.1);
//                 border: 1px solid rgba(255, 255, 255, 0.2);
//                 color: var(--td-white);
//             }
//         }

//         .input-field {
//             .form-control {
//                 background: rgba(255, 255, 255, 0.1);
//                 border-color: rgba(255, 255, 255, 0.2);
//                 color: var(--td-white);

//                 @include td-placeholder {
//                     color: var(--td-white);
//                 }
//             }
//         }
//     }

//     .td-form-group.selected_icon {
//         .input-icon {
//             svg * {
//                 stroke: var(--td-white);
//             }

//             path * {
//                 stroke: var(--td-white);
//             }
//         }
//     }
//     .attachment-actions {
//         .add-attachment {
//             background-color: rgba($white, $alpha: .1);
//             border-color: rgba($white, $alpha: .2);
//             svg * {
//                 stroke: var(--td-white);
//             }
//         }
//     }
//     .attachment-previews {
//         .preview {
//             background-color: rgba($white, $alpha: .1);
//             border-color: rgba($white, $alpha: .2);
//         }
//     }
//     /*----------------------------------------*/
//     /*  AI detector styles
//     /*----------------------------------------*/
//     .detector-result-card {
//         .legend-inner {
//             .legend {
//                 color: var(--td-white);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  profile settings styles
//     /*----------------------------------------*/
//     .sidebar {
//         nav {
//             ul {

//                 li {

//                     .link {
//                         color: rgba($white, $alpha: 0.65);
//                         transition: .3s;

//                         &:hover {
//                             background-color: rgba($white, $alpha: .2);
//                             color: var(--td-white);
//                         }

//                         &.active {
//                             background-color: rgba($white, $alpha: .2);
//                             color: var(--td-white);
//                         }
//                     }
//                 }
//             }
//         }
//     }

//     .toggle-group {
//         .toggle-item {
//             .toggle-label {
//                 p {
//                     color: rgba($white, $alpha: 0.7);
//                 }
//             }

//             .toggle-switch {
//                 .slider {
//                     background-color: rgba($white, $alpha: 0.3);
//                 }
//             }
//         }
//     }

//     .two-fa-auth-wrapper {
//         .contents {
//             .title {
//                 color: rgba($white, $alpha: 1);
//             }

//             .description {
//                 color: rgba($white, $alpha: 0.7);
//             }
//         }

//         .manual-code {
//             .manual-divider {
//                 .divider-line {
//                     border-color: rgba($white, $alpha: 0.2);
//                 }
//             }
//         }

//         .qr-code {
//             .thumb {
//                 img {
//                     border: 1px solid rgba($white, $alpha: 0.2);
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Templates styles
//     /*----------------------------------------*/
//     .all-template-card {
//         .contents {
//             background-color: rgba($primary-alt, $alpha: 0.2);

//             .inner {
//                 .card-title {
//                     color: var(--td-white);
//                 }
//             }

//             .description {
//                 color: rgba($white, $alpha: 0.7);
//             }
//         }

//         .bottom-content {
//             p {
//                 color: rgba($heading, $alpha: 0.7);
//             }
//         }

//         .top-content {
//             .tag-icon {
//                 background-color: rgba($primary-alt, $alpha: 0.2);
//                 color: var(--td-primary-alt);

//                 span {
//                     color: rgba($primary-alt, $alpha: 0.8);

//                     svg * {
//                         stroke: rgba($primary-alt, $alpha: 0.8);
//                     }
//                 }
//             }

//             .bookmark-icon {
//                 .bookmark-btn {
//                     background-color: rgba($primary-alt, $alpha: 0.2);

//                     .bookmark-icon-svg {
//                         path {
//                             stroke: rgba($primary-alt, $alpha: 0.8);
//                         }

//                         &.filled {
//                             fill: rgba($primary-alt, $alpha: 0.8);
//                         }
//                     }
//                 }
//             }
//         }
//     }

//     .workflow-tags {
//         .tag {
//             background-color: rgba($white, $alpha: 0.2);
//             color: rgba($white, $alpha: 0.8);

//             span {
//                 svg * {
//                     stroke: rgba($white, $alpha: 0.8);
//                 }
//             }
//         }
//     }


//     /*----------------------------------------*/
//     /*  AI detector styles
//     /*----------------------------------------*/
//     .plagiarism-progress-box {
//         .progress-item {
//             .progress-head {

//                 .label,
//                 .title {
//                     color: var(--td-white);
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /* Sidebar styles
//     /*----------------------------------------*/
//     .app-sidebar {
//         .slide {
//             .sidebar-menu-item {
//                 &:hover {
//                     background: rgba($white, $alpha: 0.2);

//                     .side-menu-icon {
//                         svg * {
//                             stroke: var(--td-white);
//                         }
//                     }

//                     .sidebar-menu-label {
//                         color: var(--td-white);
//                     }
//                 }
//             }

//             &.active {
//                 .sidebar-menu-item {
//                     background: rgba($white, $alpha: 0.2);
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Dashboard css 
//     /*----------------------------------------*/
//     .app-dashboard-header {
//         .right-content {
//             .dashboard-quick-actions {
//                 &>ul {
//                     li {
//                         &.has-logout {
//                             .content {
//                                 &:hover {
//                                     background-color: rgba(233, 78, 91, 0.7);
//                                     color: var(--td-white);
//                                 }
//                             }
//                         }

//                         .quick-action-item {
//                             .action-icon {
//                                 border-color: rgba($white, $alpha: 0.1);
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }

//     .app-dashboard-header {
//         .right-content {
//             .dashboard-quick-actions {
//                 &>ul {
//                     li {
//                         list-style: none;
//                     }
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Price css
//     /*----------------------------------------*/
//     .single-price-item {

//         &.is-border,
//         &.is-popular {
//             .price-info-list {
//                 ul {
//                     li {
//                         .info-item {
//                             @include flexbox();
//                             gap: 10px;

//                             .icon {
//                                 svg * {
//                                     stroke: var(--td-white);
//                                 }

//                             }
//                         }
//                     }
//                 }
//             }
//         }

//         &.is-popular {
//             background: linear-gradient(188.38deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.2) 99.65702295303345%);
//             @include border-radius(16px);
//             border: 1px solid rgba(255, 255, 255, 0.12);

//             .price-top .price-title .title {
//                 background-color: rgba($white, $alpha: 0.3);
//                 color: rgba($heading, $alpha: 0.7);
//             }

//             .price-value {
//                 border-color: rgba(255, 255, 255, 0.1);
//             }

//             .price-bottom {
//                 .td-btn {
//                     &.btn-dark {
//                         border-color: rgba($white, $alpha: 0.1);
//                         color: var(--td-white);
//                         background-color: rgba($white, $alpha: 0.3);
//                     }
//                 }
//             }
//         }

//         &.is-border {
//             border-color: rgba(255, 255, 255, 0.12);
//             background-color: #111315;
//         }

//         .price-bottom {
//             border-color: rgba($white, $alpha: 0.1);

//             .td-btn {
//                 &.btn-primary-outline {
//                     border-color: rgba($white, $alpha: 0.1);
//                     color: var(--td-white);
//                 }
//             }
//         }

//         .price-value {
//             border-color: rgba($white, $alpha: 0.1);
//         }
//     }

//     .pricing-billing-duration {
//         .nav-tabs {
//             background: rgba($white, $alpha: 0.1);
//             border-color: rgba($border-secondary, $alpha: 0.1);

//             .nav-item {
//                 .nav-link {
//                     color: rgba($white, $alpha: 0.7);

//                     &.active {
//                         background-color: var(--td-primary);
//                         color: var(--td-white);
//                     }
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  cards styles
//     /*----------------------------------------*/
//     .single-card {
//         background-color: $deep-black;
//     }

//     /*----------------------------------------*/
//     /*  Quick access styles
//     /*----------------------------------------*/
//     .quick-access-card {
//         border-color: rgba($white, $alpha: 0.3);
//     }

//     .quick-access-icon {
//         span {
//             filter: brightness(0) invert(1);
//         }
//     }

//     /*----------------------------------------*/
//     /*  Progressbar css
//     /*----------------------------------------*/
//     .package-main-box {
//         .progress-contents {
//             .progress-bar {
//                 background: linear-gradient(90deg,
//                         rgba(91, 108, 253, 1) 0%,
//                         rgba(249, 249, 255, 0.9) 100%);

//                 .progress-remaining {
//                     &::before {
//                         position: absolute;
//                         content: "";
//                         height: calc(100% - 16px);
//                         width: 80%;
//                         top: 50%;
//                         inset-inline-end: 7px;
//                         border-inline-end: 4px solid #A8AFEC;
//                         transform: translateY(-50%);
//                         @include border-radius(3px);
//                         border-color: #5B6CFD;
//                     }
//                 }
//             }
//         }
//     }

//     .ami-progress-bar {
//         .progress {
//             background-color: #313435;
//         }
//     }

//     .ami-progress-percentage {
//         color: var(--td-white) 313435
//     }

//     .language-nav {
//         .lang {
//             span {
//                 &.lang-txt {
//                     color: var(--td-white);
//                 }
//             }

//             svg * {
//                 stroke: var(--td-white);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /* Breadcrumb styles
//     /*----------------------------------------*/
//     .breadcrumb {
//         .breadcrumb-item {
//             color: rgba($white, $alpha: 0.4);

//             &:last-child {
//                 color: rgba($white, $alpha: 0.5);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Table css
//     /*----------------------------------------*/
//     .table-container {
//         border-color: rgba($white, $alpha: 0.1);
//         background: transparent;
//     }

//     .td-table {

//         thead {

//             tr {
//                 th {
//                     color: var(--td-white);
//                 }
//             }
//         }

//         th {
//             border-bottom: 1px solid rgba($white, $alpha: 0.16);
//         }

//         td {
//             border-bottom: 1px solid rgba($white, $alpha: 0.16);
//         }

//         tbody {
//             tr {
//                 &:hover {
//                     background-color: rgba($white, $alpha: 0.2);
//                 }
//             }
//         }

//         .description {
//             .subject-title {
//                 color: var(--td-white);
//             }
//         }

//         .view-button {
//             i {
//                 font-size: 16px;
//                 color: rgba($white, $alpha: 0.6);
//             }

//             &:hover i {
//                 color: currentColor;
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Folder styles
//     /*----------------------------------------*/
//     // action-menu
//     .action-menu {
//         background: var(--td-deep-black);
//         box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
//         border-color: rgba($white, $alpha: 0.1);

//         li {
//             svg * {
//                 stroke: rgba($white, $alpha: 0.6);
//             }

//             &.has-danger {
//                 svg * {
//                     stroke: var(--td-danger);
//                 }
//             }

//             &:hover {
//                 background-color: rgba($white, $alpha: 0.1);
//                 ;
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Modal styles
//     /*----------------------------------------*/

//     .default-model {

//         .modal-content {
//             background: var(--td-deep-black);
//         }
//     }

//     .default-model-two {
//         .profile-delete-content {
//             .title {
//                 color: var(--td-heading);
//             }

//             .description {
//                 color: rgba($black, $alpha: 0.7);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Project styles
//     /*----------------------------------------*/
//     .project-section,
//     .activity-section {

//         .project-list,
//         .activity-list {

//             .project-item,
//             .activity-item {
//                 border-color: rgba($white, $alpha: 0.3);

//                 .icon {
//                     svg * {
//                         stroke: rgba($white, $alpha: 0.7);
//                     }
//                 }

//                 .details {

//                     .date-status,
//                     .date {
//                         color: rgba($white, $alpha: 0.7);
//                     }
//                 }

//                 .menu {
//                     color: var(--td-white);
//                 }
//             }
//         }
//     }

//     .app-sidebar.nav-folded {
//         .main-sidebar-header {
//             .sidebar-logo {
//                 .main-logo.logo-white-mode {
//                     display: none;
//                 }
//             }
//         }

//         &.side-nav-hover {
//             .main-sidebar-header {
//                 .sidebar-logo {
//                     .main-logo {
//                         &.logo-white-mode {
//                             display: block;
//                         }
//                     }
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  Charts styles
//     /*----------------------------------------*/
//     .total-usage-chart-box {
//         background-color: var(--td-deep-black);
//     }

//     /*----------------------------------------*/
//     /*  Charts styles
//     /*----------------------------------------*/
//     .professionals-team {
//         .contents {
//             .designation {
//                 color: rgba($heading, $alpha: 0.7);
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /* Supports styles
//     /*----------------------------------------*/
//     .messages-box-wrapper {
//         .message-list {
//             .attachments {
//                 .attachment {
//                     .text {
//                         color: rgba($black, $alpha: 0.7);
//                     }
//                 }
//             }

//             .description,
//             .author {
//                 color: rgba($white, $alpha: 0.7);
//             }
//         }
//     }

//     .attachment-actions-buttons {
//         .add-attachment {
//             border-color: rgba($white, $alpha: 0.2);
//             color: rgba($white, $alpha: 0.7);

//             svg *,
//             i {
//                 color: rgba($white, $alpha: 0.7);
//                 stroke: rgba($white, $alpha: 0.7);
//             }
//         }
//     }

//     // Ticket side panel
//     .ticket-side-panel {
//         width: 500px;
//         background: var(--td-deep-black);
//         border-color: var(--td-deep-black);
//     }

//     /*----------------------------------------*/
//     /*  Affiliate styles
//     /*----------------------------------------*/

//     .affiliate-card-item {
//         background: var(--td-deep-black);
//     }

//     .how-to-work-contents {
//         .info-box {
//             background: rgba($primary, $alpha: .14);
//         }

//         ul {
//             li {
//                 .list-item {
//                     .info {
//                         strong {
//                             color: var(--td-white);
//                         }
//                     }
//                 }
//             }
//         }

//         .referral-box {
//             .referral-icon {
//                 background: rgba($white, $alpha: 0.1);

//                 svg * {
//                     stroke: rgba($white, $alpha: 1);
//                 }
//             }

//             .copy-btn {
//                 svg * {
//                     stroke: var(--td-white);
//                 }
//             }
//         }
//     }

//     /*----------------------------------------*/
//     /*  article-writer styles
//     /*----------------------------------------*/

//     .article-dropdown {
//         .dropdown-btn {
//             color: var(--td-white);

//             .icon {
//                 color: var(--td-white);

//                 svg * {
//                     stroke: var(--td-white);
//                 }
//             }
//         }
//     }

//     .article-untitled-card {
//         background-color: rgba($primary-alt, $alpha: 0.2);

//         .icon span {
//             svg * {
//                 stroke: var(--td-white);
//             }
//         }
//     }

//     .article-input-box {
//         background-color: rgba($white, $alpha: 0.1);
//         border-color: rgba($white, $alpha: 0.12);
//     }


//     // app sidebar styles
//     .app-sidebar {
//         background: var(--td-deep-black);
//         border-color: rgba($gainsboro, $alpha: 0.1);

//         .sidebar-menu-label {
//             color: rgba($white, $alpha: 0.65);
//         }

//         .side-menu-icon {
//             svg * {
//                 stroke: rgba($white, $alpha: 0.65);
//             }
//         }

//         .sidebar-menu-category {
//             .category-name {
//                 color: hsl(230.94deg 100% 68.36%);
//             }
//         }

//         .main-sidebar-header {
//             .sidebar-logo {
//                 .main-logo {
//                     display: none;

//                     &.logo-white-mode {
//                         display: block;
//                     }
//                 }
//             }
//         }
//     }

//     .app-sidebar {
//         .slide.active {
//             .sidebar-menu-item {
//                 .sidebar-menu-label {
//                     color: var(--td-white);
//                 }

//                 .side-menu-icon {
//                     svg * {
//                         stroke: var(--td-white);
//                     }
//                 }
//             }
//         }
//     }

//     .invite-card-box {
//         background: var(--td-deep-black);

//         .invite-card-icon {
//             svg * {
//                 stroke: rgba($white, $alpha: 0.65);
//             }
//         }

//         .invite-card-content {
//             .copy-btn {
//                 svg {
//                     * {
//                         stroke: rgba($white, $alpha: 0.70);
//                     }
//                 }
//             }
//         }
//     }

//     // app dashboard styles
//     .app-dashboard-header {
//         background-color: #111315;
//         border-color: rgba($gainsboro, $alpha: 0.1);
//     }

//     .app-dashboard-header {
//         .right-content {
//             .dashboard-quick-actions {
//                 &>ul {
//                     li {
//                         .quick-action-item {
//                             .notification-btn {
//                                 svg * {
//                                     stroke: rgba($white, $alpha: 1);
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }


//     .ai-card-item {
//         background-color: var(--td-deep-black);
//         border: 1px solid var(--td-deep-black);
//     }

//     // package main box
//     .package-main-box {
//         padding: 14px 14px;
//         border: 1px solid rgba(8, 8, 8, 0.12);
//         @include border-radius(16px);
//         background-color: var(--td-deep-black);

//         .package-options {
//             border-color: rgba($white, $alpha: 0.30);

//             .nav-tabs {
//                 .nav-link {
//                     color: rgba(255, 255, 255, 0.7);

//                     &.active {
//                         background-color: #5A5EFF;
//                         color: var(--td-white);
//                     }
//                 }
//             }
//         }

//         .package-info {

//             .period,
//             .remaining {
//                 background: rgba($white, $alpha: 0.10);
//                 color: var(--td-white);
//                 border: 1px solid rgba(255, 255, 255, 0.1);
//             }
//         }
//     }
// }