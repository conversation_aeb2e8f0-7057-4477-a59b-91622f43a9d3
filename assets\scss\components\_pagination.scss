@use '../utils' as *;

/*----------------------------------------*/
/*  Basic pagination styles
/*----------------------------------------*/
.td-pagination {
	ul {
		@include flexbox();
		align-items: center;
		gap: 8px;
		flex-wrap: wrap;

		@media #{$xs,$sm,$md} {
			justify-content: start;
		}

		li {
			list-style: none;

			a {
				width: 32px;
				height: 32px;
				@include inline-flex();
				align-items: center;
				justify-content: center;
				position: relative;
				inset-inline-end: 0;
				top: 50%;
				font-size: 14px;
				line-height: 20px;
				font-weight: 900;
				@include border-radius(4px);
				border: 1px solid rgba($white, $alpha: 0.1);
				color: var(--td-text-primary);

				&:hover {
					background: #0e1b2c;
					color: var(--td-white);
				}

				i {
					font-size: 12px;
				}
			}

			.current {
				width: 32px;
				height: 32px;
				@include inline-flex();
				align-items: center;
				justify-content: center;
				position: relative;
				inset-inline-end: 0;
				top: 50%;
				font-weight: 500;
				font-size: 16px;
				background: #0e1b2c;
				color: var(--td-white);
			}

			.disabled {
				background: #0a0e17;
				color: #1e2530;
				&:hover {
					background: #0a0e17;
					color: #1e2530;
				}
			}
		}
	}
}