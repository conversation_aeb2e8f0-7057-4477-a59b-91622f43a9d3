@use '../../utils' as *;

/*----------------------------------------*/
/*  Dashboard-widget styles
/*----------------------------------------*/
.dashboard-widget-wrapper {
    display: grid;
    grid-template-columns: auto 432px;
    gap: rem(20);

    @media #{$xxl} {
        grid-template-columns: auto 350px;
    }

    @media #{$xs,$sm,$md,$lg,$xl} {
        display: flex;
        flex-direction: column;
    }
}

.dashboard-widget-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
    height: 100%;
    @media #{$xs,$sm} {
        grid-template-columns: repeat(2, 1fr);
    }

    @media #{$xxs} {
        grid-template-columns: 1fr;
    }
}

.dashboard-widget-card {
    display: flex;
    align-items: center;
    background: #091628;
    padding: 18px 18px;
    border-radius: 16px;
    gap: 16px;
    background-repeat: no-repeat;
    background-size: cover;

    @media #{$xs,$xxl} {
        padding: 12px 12px;
    }

    .icon {
        background: #f26822;
        border-radius: 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        img {
            width: 40px;
        }

        @media #{$xs} {
            width: 44px;
            height: 44px;
            img {
                width: 26px;
            }
        }
    }

    .contents {
        .card-title {
            color: #999999;
            font-size: 16px;
            font-weight: 700;

            @media #{$xs} {
                font-size: 16px;
            }
        }

        .card-value {
            color: var(--td-white);
            font-size: 20px;
            font-weight: 500;
            margin-top: rem(5);

            @media #{$xs} {
                font-size: 18px;
            }
        }
    }

    &:nth-child(2) {
        .icon {
            background-color: #3B57E7;
        }
    }

    &:nth-child(3) {
        .icon {
            background-color: #45772F;
        }
    }

    &:nth-child(4) {
        .icon {
            background-color: #00694D;
        }
    }

    &:nth-child(5) {
        .icon {
            background-color: #574B90;
        }
    }

    &:nth-child(6) {
        .icon {
            background-color: #00758F;
        }
    }

    &:nth-child(7) {
        .icon {
            background-color: #4B6584;
        }
    }

    &:nth-child(8) {
        .icon {
            background-color: #497D74;
        }
    }

    &:nth-child(9) {
        .icon {
            background-color: #EB3B5A;
        }
    }
}