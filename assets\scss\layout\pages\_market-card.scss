@use '../../utils' as *;

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/

/* Market Card */
.market-card {
    position: relative;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(10px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}

.market-list-box {
    padding: 8px;
}

/* Search Bar */
.market-search-bar {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px;
    position: relative;

    input {
        border: none;
        color: var(--td-white);
        width: 100%;
        background: #091628;
        border-radius: 100px;
        padding: 5px 16px 5px 34px;
        height: 40px;
        font-size: 14px;

        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }

    .icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 22px;
    }

}

/* Tabs Wrapper */
.market-tabs-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0 8px;

    &:hover {
        .scroll-left {
            opacity: 1;
        }

        .scroll-right {
            opacity: 1;
        }
    }

    .market-tabs {
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
        scrollbar-width: none;
        scroll-behavior: smooth;
        width: 100%;
        padding: 5px 0;
        align-items: center;

        &::-webkit-scrollbar {
            display: none;
        }

        .nav-tabs {
            display: inline-flex;
            flex: none;
            gap: 14px;

            .nav-link {
                font-size: 12px;
                line-height: 16px;
                font-weight: 700;
                background-color: transparent;
                color: #999999;
                display: flex;
                align-items: center;
                column-gap: 4px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding-bottom: 1px;

                &.active {
                    border-color: var(--td-primary);
                    color: var(--td-white);
                }
            }
        }
    }

    /* Scroll Buttons */
    .scroll-left {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        padding: 5px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        left: -5px;
    }

    .scroll-right {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        padding: 5px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
        right: -5px;
    }

}

/* Sticky Header */
.market-header {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    color: hsla(0, 0%, 100%, .8);
    border-radius: 8px;
    font-size: 12px;
    margin-bottom: 8px;
    margin-top: 2px;
    padding: 0 16px;
}

/* Market List */
.market-list {
    max-height: 403px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-right: 8px;

    &:not(:last-child) {
        margin-bottom: 8px;
    }

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
    }
}

/* Market Item */
.market-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 6px;
    background: #071220;
    @include border-radius(4px);

    .symbol {
        display: flex;
        align-items: center;

        .favorite {
            color: var(--td-white);
            font-family: "Satoshi-Bold", sans-serif;
            font-size: 11px;
            line-height: 16px;
            font-weight: 700;
            padding-right: 4px;
            display: inline-flex;

            i {
                width: 14px;
                height: 14px;
                color: var(--td-text-primary);
            }
        }

        .flag {
            font-size: 18px;
            margin-right: 8px;

            i {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                background-size: cover;
            }
        }

        .currency {
            font-size: 11px;
            line-height: 16px;
            font-weight: 700;
            color: var(--td-white);
        }
    }

    .price {
        display: flex;
        column-gap: 26px;
        align-items: center;

        .price-text {
            font-family: "Satoshi-Bold", sans-serif;
            font-size: 11px;
            line-height: 16px;
            font-weight: 700;
            padding-right: 4px;
            display: flex;
            align-items: center;
        }
    }

    .change {
        .change-text {
            color: #eb4e5c;
            text-align: right;
            font-family: "Satoshi-Medium", sans-serif;
            font-size: 11px;
            font-weight: 500;

            &.positive {
                color: #00c58e;
            }

            &.negative {
                color: #ff4d4d;
            }
        }
    }

    .mini-chart {
        width: 50px;
        height: 20px;
    }
}

.tradingview-main-chart {
    padding: 10px;
    border-radius: 8px;
    position: relative;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(10px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}