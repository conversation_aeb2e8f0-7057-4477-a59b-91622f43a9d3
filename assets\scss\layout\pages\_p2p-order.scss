@use '../../utils' as *;

/*----------------------------------------*/
/*  P2p order styles
/*----------------------------------------*/
.p2p-order-area {
    background: #091628;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 16px 0px 0px 0px;
    position: relative;

    @media #{$xs} {
        border-radius: 12px;
    }
}

.p2p-order-header {
    padding: 10px;

    .top-contents {
        display: flex;
        gap: 16px;

        @media #{$xs,$sm,$md} {
            flex-direction: column;
        }
    }
}

// buy sell box tab
.buy-sell-box-tab {
    background: rgba(3, 166, 109, 0.06);
    border-radius: 12px;
    border: 1px solid #03a66d;
    padding: 6px;
    display: inline-flex;
    flex-direction: row;
    gap: 9px;
    align-items: center;
    justify-content: flex-start;
    flex: 0 0 auto;
    height: max-content;
    width: max-content;

    .nav-tabs {
        .nav-item {
            .nav-link {
                border: none;
                padding: 0px 30px;
                color: white;
                border-radius: 5px;
                cursor: pointer;
                height: 30px;
                background-color: transparent;

                &.active {
                    background: var(--td-green);
                }
            }
        }
    }
}

// currency options tab
.p2p-currency-options-tab {
    .nav-tabs {
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 5px 8px 5px 8px;
        display: inline-flex;
        gap: 10px;
        align-items: flex-start;
        justify-content: start;
        position: relative;


        .nav-item {
            .nav-link {
                border-radius: 5px;
                border: 1px solid #0e1f36;
                padding: 10px 16px 10px 16px;
                display: flex;
                flex-direction: row;
                gap: 10px;
                align-items: center;
                justify-content: center;
                height: 28px;
                font-size: 12px;
                font-weight: 700;
                color: #999;

                &:hover {
                    color: var(--td-primary);
                    background: #142032;
                }

                &.active {
                    color: var(--td-primary);
                    background: #142032;
                }
            }
        }
    }
}

// p2p order filters
.p2p-order-filters {
    margin: 20px 0;
    display: grid;
    gap: 16px;
    align-items: center;
    grid-template-columns: repeat(auto-fit, minmax(236px, 1fr));
}

.filter-button {
    background: #091628;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-width: 1px;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #1e2535;
    }
}
.filter-option {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}
.filter-options {
    position: absolute;
    top: calc(100% + 10px);
    left: 0;
    background-color: #181f2f;
    border: 1px solid #1e2535;
    border-radius: 8px;
    width: 250px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 100;
    display: none;

    &.active {
        display: block;
    }

    .filter-header {
        padding: 12px 16px;
        border-bottom: 1px solid #1e2535;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .close-button {
        background: none;
        border: none;
        color: #8b93a7;
        cursor: pointer;
        font-size: 18px;
    }

    .filter-section {
        padding: 12px 16px;
        border-bottom: 1px solid #1e2535;

        &:last-child {
            border-bottom: none;
        }
    }

    .section-title {
        font-size: 14px;
        color: #8b93a7;
        margin-bottom: 12px;
    }

    .option-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .checkbox {
        width: 20px;
        height: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .checkbox.selected {
        background-color: #3a6df0;
        border-color: #3a6df0;

        &:after {
            content: "✓";
            color: white;
            font-size: 14px;
            width: 20px;
            height: 20px;
            left: 6px;
            display: inline-block;
            position: absolute;
            top: 0px;
        }
    }

    .range-slider {
        width: 100%;
        margin: 10px 0;
    }

    .apply-button {
        background-color: #3a6df0;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 16px;
        font-weight: 500;
        cursor: pointer;
        width: 100%;
        margin-top: 10px;

        &:hover {
            background-color: #2d5cd7;
        }
    }

}

// multiple select
.custom-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    color: #ffffff;
    position: relative;

    input {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid #4A5568;
        border-radius: 4px;
        background: #1a1f2d;
        cursor: pointer;
        transition: all 0.3s;

        &:checked {
            background: #22c55e;
            border-color: #22c55e;

            &::after {
                content: '✔';
                font-size: 14px;
                color: #ffffff;
                font-weight: bold;
                position: absolute;
                left: 4px;
                top: 1px;
            }
        }
    }
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    color: #6d6b77;
    background-color: rgb(255 255 255 / 8%);
}

.select2-selection__choice {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    /* Adjust the width as needed */
    display: inline-block;
}

.select2-container .select2-search--inline .select2-search__field {
    height: 44px;
    line-height: 44px;
    margin-top: 0 !important;
}

.table-advertiser {
    display: flex;
    gap: 10px;

    .avatar {
        width: 38px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #1C2C44;
        border-radius: 50%;
        font-size: 16px;
        font-weight: bold;
        color: var(--td-white);
    }

    &.is-online,
    &.is-offline {
        .avatar {
            position: relative;

            &::before {
                position: absolute;
                content: "";
                width: 10px;
                height: 10px;
                background-color: var(--td-green);
                border-radius: 50%;
                bottom: 0;
                right: 0;
            }
        }
    }

    &.is-offline {
        .avatar {
            &::before {
                background-color: #303d52;
            }
        }
    }

    .name {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 8px;
        color: var(--td-white);
    }

    .details {
        font-size: 13px;
        font-weight: 500;
        color: #bdbdbd;
        margin-bottom: 8px;
    }

    .status {
        font-size: 13px;
        font-weight: 500;
    }

    .online {
        color: #4caf50;
    }

    .devider {
        color: #999999;
        opacity: 0.5;
    }
}

// Oder status
.oder-status-wrapper {
    max-width: 606px;
    margin: 0 auto;
    border-radius: 24px;
    padding: 30px 30px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.1);

    @media #{$xs} {
        padding: 20px 20px;
    }

    .review-box {
        margin-top: 0;

        .review-table {
            .review-btn {
                font-size: 16px;
                font-weight: 700;
            }

            background: #0e1b2c;
            border-radius: 30px;
            padding: 18px;

            .has-border {
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 40px;
            }
        }
    }
}