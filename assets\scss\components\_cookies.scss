@use "../utils" as *;

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
    max-width: 1040px;
    position: fixed;
    bottom: 30px;
    inset-inline-start: 30px;
    left: 50%;
    transform: translateX(-50%);
    row-gap: 12px;
    column-gap: 12px;
    border-radius: 12px;
    border: 1px solid #26456F;
    background: #0E1B2C;
    box-shadow: 0px 23px 100px 0px rgba(166, 239, 103, 0.16);
    padding: 12px 24px 12px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 90%;
    z-index: 111;
    transition: 0.3s;

    @media #{$xs,$sm} {
        flex-direction: column;
        align-items: self-start;
    }

    @media #{$xxs} {
        padding: 12px 16px 12px 16px;

        .caches-btns {
            .td-btn {
                font-size: 12px;
            }
        }
    }

    .caches-contents {
        .title {
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--td-white);
            font-weight: 600;

            @media #{$xs} {
                font-size: 18px;
            }
        }

        p {
            font-size: 14px;
            margin-bottom: 0;
            color: var(--td-white);

            a {
                color: $primary;
            }
        }
    }

    .caches-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0 0 auto;
        flex-wrap: wrap;
        gap: 12px;
    }
}