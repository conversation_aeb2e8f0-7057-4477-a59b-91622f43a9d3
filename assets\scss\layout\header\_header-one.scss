@use '../../utils' as *;

/*----------------------------------------*/
/*  Header Styles
/*----------------------------------------*/

// Header transparent
.header-transparent {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  z-index: 99;
}

// Active-sticky
.active-sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
  background-color: rgba($black, $alpha: 0.7) !important;
  box-shadow: 0 2px 4px rgba(7, 37, 68, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

// Mode-switcher
.mode-switcher {
  button {
    position: relative;
    top: -3px;
  }
}

// Heder one styles
.header-style-one {
  border-bottom: 1px solid rgba($white, $alpha: 0.1);

  &.has-landing {
    background-color: #010C1A;
    position: relative;

    .td-main-menu nav>ul>li>a {
      padding: 27px 5px;
    }

    z-index: 99;

    &.active-sticky {
      background-color: #010C1A;
    }
  }

  &.is-auth-header {
    padding: 15px 0;
    border-bottom: 0;
  }

  @media #{$xs,$sm,$md} {
    padding: 18px 0;
  }

  .header-btns-wrap {
    display: flex;
    align-items: center;
    column-gap: 10px;
  }

  .header-quick-actions {
    column-gap: 16px;

    @media #{$xs,$sm,$md,$lg} {
      column-gap: 8px;
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    column-gap: 80px;

    @media #{$lg} {
      column-gap: 16px;
    }
  }

  .header-logo {
    a {
      display: block
    }

    img {
      height: 30px;

      @media #{$xs} {
        height: 26px;
        object-fit: cover;
      }
    }
  }

  .header-inner {
    @include flexbox();
    align-items: center;
    justify-content: space-between;
  }
}

// Language css
.language-nav {
  background-color: transparent;
  position: relative;
  @include inline-flex();
  align-items: center;
  justify-content: center;

  .translate_wrapper.active {
    .more_lang {
      display: block;
      position: absolute;
      background-color: var(--td-white);
      top: 47px;
      inset-inline-end: 0;
      @include border-radius(16px);
      padding: 6px 6px 6px 6px;
      width: 220px;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
      background: #171c35;
      padding: 10px;
      width: 200px;
      z-index: 31;
    }
  }

  .current_lang {
    cursor: pointer;
    text-transform: uppercase;
    overflow: hidden;

    .lang {
      .flag-icon {
        width: 24px;
        height: 24px;
        @include inline-flex();
        align-items: center;
        background-size: cover;
        border-radius: 40px;
      }
    }
  }

  .lang.selected {
    display: none;
  }

  .lang {
    span.lang-txt {
      @include inline-flex();
      margin-inline-start: 6px;
      margin-inline-end: 6px;
      font-size: 14px;
      font-weight: 600;
      color: #999999;
    }

    span {
      span {
        color: #999;
        font-weight: 400;
        margin-inline-start: 5px;
      }
    }
  }

  .more_lang {
    transform: translateY(-20px);
    opacity: 0;
    cursor: pointer;
    display: none;
    transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
    z-index: 1;

    .lang {
      padding: 6px 10px;
      @include flexbox();
      @include border-radius(6px);

      i {
        width: 24px;
        height: 24px;
        @include inline-flex();
        align-items: center;
        background-size: cover;
        border-radius: 40px;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.04);
        color: var(--td-white);

        span {
          color: var(--td-white);
        }
      }
    }
  }

  .more_lang.active {
    opacity: 1;
    transform: translateY(0px);
  }
}