@use "../utils" as *;

/*----------------------------------------*/
/* FAQ styles
/*----------------------------------------*/
.td-faq-syle {
    margin-inline-start: rem(35);
    padding-bottom: 50px;
    margin-bottom: -50px;

    @media #{$xs,$sm,$md,$lg} {
        margin-inline-start: 0;
    }

    .accordion {
        .accordion-button {
            padding: 0;
            font-size: rem(20);
            background: transparent;
            box-shadow: none;
            color: #999999;
            font-weight: 500;


            @media #{$xs,$sm,$lg} {
                font-size: rem(18);
            }

            @media #{$xxs} {
                font-size: rem(16);
            }

            &:not(.collapsed) {
                border-radius: 0;

                span {
                    color: var(--td-primary);
                }

                .accordion-body {
                    background: var(--td-white);
                }

                &::after {
                    content: "\f068";
                }
            }

            &::after {
                position: absolute;
                inset-inline-end: 20px;
                content: "\2b";
                font-family: var(--td-ff-fontawesome);
                font-size: rem(18);
                font-weight: 400;
                text-align: center;
                top: 50%;
                transform: translateY(-50%);
                background-image: none;
            }

            span {
                padding-inline-end: rem(10);
                display: inline-block;
                transition: none;
            }
        }

        .accordion-body {
            background: transparent;
            border-radius: rem(0);
            padding: 0px 30px 30px 30px;

            @media #{$xxs} {
                padding-inline-start: rem(20);
                padding-inline-end: rem(20);
            }

            .description {
                font-size: rem(14);
                color: #999999;

                strong {
                    color: var(--td-heading);
                }
            }

            .accordion-body-list {
                margin-top: rem(14);

                ul {
                    li {
                        &:not(:last-child) {
                            margin-bottom: rem(7);
                        }
                    }
                }
            }
        }

        .accordion-item {
            box-shadow: none;
            background: #0A1729;
            @include border-radius(rem(16));
            border: 0;
            z-index: 1;
            overflow: hidden;

            .faq-shape-bg {
                position: absolute;
                top: -30px;
                left: 0;
                z-index: -1;
                opacity: 0;
            }

            &:not(:last-of-type) {
                margin-bottom: rem(20);
            }

            &:not(:first-of-type) {
                border-top: 0;
            }


            &:last-of-type {
                &>.accordion-collapse {
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }

            &:first-of-type {
                .accordion-button {
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                }
            }

            &.accordion-active {
                box-shadow: 0px 4px 11px rgba(166, 239, 103, 0.1);

                .accordion-button {
                    color: var(--td-white);
                }

                border: var(--bs-accordion-border-width) solid rgba($white, $alpha: 0.16);

                &:not(:first-of-type) {
                    border-top: var(--bs-accordion-border-width) solid rgba($white, $alpha: 0.16);
                }

                .faq-shape-bg {
                    opacity: 1;
                }
            }
        }

        .accordion-header {
            button {
                padding: rem(28) rem(40) rem(28) rem(28);
                line-height: 1.5;

                @media #{$xs} {
                    padding: rem(18) rem(35) rem(18) rem(18);
                }
            }
        }
    }
    &.style-two {
        .accordion .accordion-item {
            background: #181818;
        }
    }
}