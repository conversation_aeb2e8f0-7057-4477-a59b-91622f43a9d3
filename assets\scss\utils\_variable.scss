//color Variables

// Common Color
$white: #ffffff;
$black: #000000;
$placeholder: #000000;
$selection: #000000;

// Theme Color
$primary: #a6ef67;
$secondary: #050505;
$primary-alt: #5B6CFD;
$deep-black: #1A1D1F;
$liberty-blue: #0A1729;

// Heading Color
$heading: #080808;

// Text Color
$text-primary: rgba(8, 8, 8, 0.7);

// Border Color
$border-primary: #eaeaea;
$border-secondary: #D1D6FF;

// Others Color
$yellow: #F79E1C;
$warning: #FFA336;
$success: #03A66D;
$danger: #E94E5B;
$danger-alt: #eb4e5c;
$green: #03A66D;
$dynamic-black: #1D1D1D;




// Responsive Variables
$x3l: 'only screen and (min-width: 1600px) and (max-width: 1800px)';
$xxl: 'only screen and (min-width: 1400px) and (max-width: 1599px)';
$xl: 'only screen and (min-width: 1200px) and (max-width: 1399px)';
$lg: 'only screen and (min-width: 992px) and (max-width: 1199px)';
$md: 'only screen and (min-width: 768px) and (max-width: 991px)';
$sm: 'only screen and (min-width: 576px) and (max-width: 767px)';
$xs: '(max-width: 575px)';
$xxs: "(max-width: 480px)";