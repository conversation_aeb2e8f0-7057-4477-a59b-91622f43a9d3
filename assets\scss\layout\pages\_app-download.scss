@use '../../utils' as *;

/*----------------------------------------*/
/*  Balance styles
/*----------------------------------------*/
.app-download-contents {
    margin-left: 110px;
    margin-right: 30px;

    @media #{$xs,$sm,$md,$lg} {
        margin-left: 0;
        margin-right: 0;
    }

    .app-download-buttons {
        display: flex;
        padding: 8px 16px;
        align-items: center;
        gap: 12px 12px;
        flex-wrap: wrap;

        .app-download-btn {
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.10);
            background: #0A1728;
            display: flex;
            padding: 6px 16px;
            align-items: center;
            gap: 8px;

            .contents {
                display: flex;
                flex-direction: column;

                span {
                    font-size: 12px;
                    color: #999999;
                    font-weight: 700;
                }

                h6 {
                    font-size: 14px;
                    font-weight: 700;
                    color: var(--td-white);
                }
            }

            .icon {
                width: 24px;
                flex: 0 0 auto;
            }
        }
    }
}

.app-download-thumb {
    margin-left: 65px;

    @media #{$xs,$sm,$md} {
        margin: 0 auto;
        text-align: center;
    }
}

// Style two 
.app-download-demo-2 {
    padding: 0 12px;
}
.app-download-section {
    &.section-two {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: left bottom 68%;
    }
}