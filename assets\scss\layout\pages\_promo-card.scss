@use '../../utils' as *;

/*----------------------------------------*/
/*  Promo card styles
/*----------------------------------------*/
.promo-card-box {
    background: #091628;
    border-radius: 16px;
    padding: 16px;
}

.promo-card-item {
    background: rgba(255, 255, 255, 0.04);
    border-radius: 8px;
    padding: 18px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: rem(12) rem(20);

    .inner {
        display: flex;
        align-items: center;
        gap: rem(12);
    }

    .card-icon {
        width: 40px;
        height: 40px;
        flex: 0 0 auto;

        img {
            width: 100%;
        }
    }

    .card-contents {
        .card-title {
            color: var(--td-white);
            font-size: 14px;
            font-weight: 700;
        }

        .card-description {
            color: #999999;
            font-size: 14px;
            font-weight: 500;
        }
    }
}

// promo affiliate
.promo-affiliate-card {
    background: #091628;
    border-radius: 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: rem(30);
    padding: 18px 18px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    .card-icon {
        width: 66px;
        height: 66px;

        img {
            width: 100%;
        }
    }

    .card-contents {
        .card-title {
            color: var(--td-white);
            font-size: rem(20);
            font-weight: 700;

            @media #{$xs,$xl,$xxl} {
                font-size: rem(18);
            }
        }

        .card-description {
            font-size: 14px;
            font-weight: 500;
        }

        .card-link {
            display: inline-block;
            .td-btn {
                background: rgba(166, 239, 103, 0.07);
                border-radius: 8px;
                color: var(--td-primary);
                font-weight: 400;
            }
        }
    }
}

// promo feature
.promo-feature-card {
    background: #091628;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: rem(30);
    padding: 18px 18px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    .card-contents {
        .card-title {
            color: var(--td-white);
            font-size: rem(16);
            font-weight: 700;
            margin-bottom: rem(4);
        }

        .card-description {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 0;
        }

        .card-link {
            margin-top: rem(12);

            .td-btn {
                background: rgba(166, 239, 103, 0.07);
                border-radius: 8px;
                color: var(--td-primary);
                font-weight: 400;
            }
        }
    }
}