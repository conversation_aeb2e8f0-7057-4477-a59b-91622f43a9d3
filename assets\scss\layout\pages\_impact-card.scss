@use '../../utils' as *;

/*----------------------------------------*/
/*  Impact card styles
/*----------------------------------------*/
.impact-card-area {
    position: relative;
    z-index: 1;
    border-radius: 8px;
    padding: 8px;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(8px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(166, 239, 103, 0.2) 0%, rgba(127, 188, 203, 0.25) 49.50000047683716%, rgba(6, 17, 20, 0.25) 99.50000047683716%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}

// impact tab
.impact-tab {
    .nav-tabs {
        background: #071220;
        border-radius: 100px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 6px;
        display: flex;
        flex-direction: row;
        gap: 14px;
        align-items: center;
        justify-content: flex-start;
        align-self: stretch;
        position: relative;
        margin-bottom: 10px;

        .nav-link {
            font-weight: 700;
            background-color: transparent;
            position: relative;
            text-transform: uppercase;
            border: 0;
            gap: 5px;
            padding: 6px 6px;
            text-decoration: none;
            border-radius: 30px;
            font-size: 9px;
            display: inline-flex;
            align-items: center;
            line-height: 1;
            color: var(--td-white);

            &.active {
                background-color: var(--td-primary);
                color: #1D1D1D;
            }
        }
    }
}

// impact box
.impact-box {
    .item-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 6px;
        border: 1px solid hsla(0, 0%, 100%, .1);
        background: #071220;
        border-radius: 4px;

        &:not(:last-child) {
            margin-bottom: 8px;
        }

        .flag {
            margin-right: 4px;

            i {
                width: 22px;
                height: 22px;
                border-radius: 50%;
                background-size: cover;
            }
        }
    }

    .item-details {
        display: flex;
        align-items: center;
    }

    .time {
        color: var(--td-white);
        margin-right: 8px;
        background: rgba(255, 255, 255, 0.06);
        border-radius: 4px;
        display: block;
        padding: 5px 7px;
        font-size: 10px;
        font-weight: 700;
    }

    .description {
        font-size: 10px;
        font-weight: 700;
    }

    .impact {
        display: flex;
        align-items: center;

        .title {
            margin-right: 8px;
            font-size: 10px;
            line-height: 16px;
            font-weight: 500;
        }
    }

    .impact-circle {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 4px;

        &:last-child {
            margin-right: 0;
        }

        &.impact-down {
            background-color: #606060;
        }
    }

    .impact-low {
        background-color: #5db18b;
    }

    .impact-medium {
        background-color: #f39c12;
    }

    .impact-high {
        background-color: #e74c3c;
    }
}