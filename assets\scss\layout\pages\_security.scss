@use '../../utils' as *;

/*----------------------------------------*/
/*  Security styles
/*----------------------------------------*/
.security-card-items {
    margin-top: 84px;

    @media #{$xs} {
        margin-top: 44px;
    }
}

.security-card-item {
    text-align: center;
    display: inline-flex;
    padding: 47px 30px 47px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 16px;
    border-radius: 24px;
    border: 2px solid rgba(3, 166, 109, 0.1);
    background: rgba(255, 255, 255, 0.04);
    backdrop-filter: blur(10px);
    width: 100%;

    @media #{$xs} {
        padding: 20px 20px 20px;
    }

    @media #{$xxs} {
        padding: 20px 16px 20px;
    }

    .security-card-icon {
        width: 70px;
        height: 70px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #03A66D;
        border-radius: 50%;
        margin-bottom: 20px;
        position: relative;
        border: 9px solid #0A242A;
        @media #{$xxs} {
            margin-bottom: 0px;
        }
    }

    .security-card-contents {
        .title {
            font-size: 16px;
        }
    }
}

.security-thumb {
    position: absolute;
    z-index: 5;
    right: 189px;
    top: 26%;
    width: 335px;

    @media #{$xs} {
        width: 220px;
    }

    @media #{$xl} {
        right: 0;
        top: 26%;
        width: 250px;
    }

    @media #{$xs,$sm,$md,$lg} {
        position: relative;
        right: 0;
        left: 0;
        margin:
            auto;
        text-align: center;
    }

    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 455px;
        height: 455px;
        border-radius: 455px;
        opacity: 0.1;
        background: #03A66D;
        filter: blur(100px);
        z-index: -1;

        @media #{$xs,$sm,$md,$lg,$xl} {
            width: 200px;
            height: 200px;
        }
    }
}

.security-wrapper {
    .row {
        & [class*="col-"] {
            &:nth-child(2) {
                margin-top: -20px;

                @media #{$xs,$sm,$md,$lg} {
                    margin-top: 30px;
                }
            }

            &:nth-child(3) {
                margin-top: -70px;

                @media #{$xs,$sm,$md,$lg} {
                    margin-top: 30px;
                }
            }
        }
    }
}

// Style two
.security-cards-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 24px;
    padding-left: 50px;
    padding-right: 80px;

    @media #{$xs,$sm,$md,$lg,$xl} {
        padding-right: 0;
    }

    @media #{$xs,$sm,$md} {
        padding-left: 0;
    }

    @media #{$md} {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
    }

    .has_fade_anim {
        &:nth-child(2) {
            grid-row: span 2;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    }

    .security-card-item {
        background-color: #001B11;
    }
}