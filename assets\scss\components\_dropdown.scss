@use '../utils' as *;

/*----------------------------------------*/
/*  Dropdown styles
/*----------------------------------------*/
.dropdown {
	position: relative;
	display: inline-block;
}

.dropdown-list {
	display: none;
	position: absolute;
	top: 100%;
	inset-inline-end: 0;
	background-color: #1c2e27;
	padding: 5px 0;
	min-width: 130px;
	@include box-shadow(0px 4px 6px rgba($black, $alpha: 0.1));
	@include border-radius(4px);
	z-index: 10;

	&.show {
		display: block;
	}
}

.dropdown-item {
	padding: 8px 16px;
	width: 100%;
	background: none;
	border: none;
	color: var(--td-white);
	text-align: left;
	cursor: pointer;

	&:hover {
		background-color: #555;
	}
}

.earnings-chard {
	background: rgba($white, $alpha: 0.02);
	border: 1px solid rgba($white, $alpha: 0.1);
	@include border-radius(12px);
	padding: 30px 30px;
}