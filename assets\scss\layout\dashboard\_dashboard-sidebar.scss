@use '../../utils' as *;

/*----------------------------------------*/
/* Dashboard Sidebar styles
/*----------------------------------------*/

.app-sidebar-wrapper {
  position: fixed;
  height: 100%;
  top: 0;
  z-index: 9;
  line-height: inherit;
  text-align: left;
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;

  &.close_icon {
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    margin-inline-start: 0;

    @media #{$xs,$sm,$md,$lg} {
      -webkit-transition: all 0.3s ease;
      transition: all 0.3s ease;
      margin-inline-start: -300px;
    }
  }

  .sidebar-inner {
    -webkit-box-shadow: 0 0 21px 0 rgba(89, 102, 122, .1);
    box-shadow: 0 0 21px 0 rgba(89, 102, 122, .1);
    border-inline-end: 1px solid rgba($white, $alpha: 0.1);
    background: var(--td-heading);

    @media #{$xs,$sm,$md,$lg,$xl} {
      background-color: #11131A;
    }
  }
}

.app-sidebar {
  width: 290px;
  height: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;
  background: #010C1A;
  border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(3px);
  transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  @media #{$xl,$xxl} {
    width: 260px;
}

  .main-sidebar-header {
    height: 70px;
    @include flexbox();
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    transition: all 0.03s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    img {
      transition: all 0.02s ease;
    }

    .sidebar-logo {
      .main-logo {
        img {
          height: 45px;
        }

        &.logo-white-mode {
          display: none;
        }
      }

      .small-logo {
        display: none;
      }
    }
  }

  .nav {
    >ul {
      padding-inline-start: 0px;
    }

    ul {
      li {
        list-style: none;
        margin: 0 14px;
        margin-bottom: 2px;
      }
    }
  }

  .sidebar-menu {
    display: none;
  }

  .sidebar-left,
  .sidebar-right {
    display: none;
  }

  .main-menu {
    >.slide {

      &.active,
      &:hover {

        .sidebar-menu {
          .sidebar-menu-item {
            &:hover {
              .side-menu-angle {
                color: var(--td-primary) !important;
              }
            }
          }
        }
      }
    }
  }

  .slide {
    &.has-sub .sidebar-menu {
      transform: translate(0, 0) !important;
      visibility: visible !important;
    }

    &.has-sub {
      @include gridbox();

      &.open {
        >.sidebar-menu-item .side-menu-angle {
          transform: rotate(180deg);
        }
      }
    }

    &.has-sub {
      @include gridbox();

      &.open {
        >.sidebar-menu-item .side-menu-angle {
          transform: rotate(180deg);
        }
      }
    }

    &.active {
      .sidebar-menu-item {
        background: rgba(91, 108, 253, 0.09);

        .side-menu-icon {
          svg * {
            stroke: var(--td-white);
          }
        }

        .sidebar-menu-label {
          color: var(--td-white);
        }
      }
    }

    &.logout {
      .sidebar-menu-item {
        .sidebar-menu-label {
          color: rgba(233, 78, 91, 0.65);
        }

        .side-menu-icon {
          svg * {
            stroke: rgba(233, 78, 91, 0.65);
          }
        }
      }
    }
  }

  .sidebar-menu.child1 .sidebar-menu-item:hover,
  .sidebar-menu.child2 .sidebar-menu-item:hover,
  .sidebar-menu.child3 .sidebar-menu-item:hover {
    color: var(--td-primary);
  }

  .sidebar-menu-category {
    .category-name {
      color: var(--td-secondary);
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      padding: 4px 10px;
      white-space: nowrap;
      position: relative;
      margin-top: 15px;
      display: block;

      @include rtl {
        text-align: right;
      }
    }
  }

  .sidebar-menu-item {
    padding: 12px 10px;
    position: relative;
    @include flexbox();
    align-items: center;
    gap: 6px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    border: 1px solid transparent;
    @include border-radius(8px);

    &.active {
      color: var(--td-primary);

      .sidebar-menu-label,
      .side-menu-angle {
        color: var(--td-primary);
      }

      .side-menu-icon {
        color: var(--td-primary);
      }
    }

    &:hover {
      background-color: rgba(91, 108, 253, .09);

      .sidebar-menu-item {
        .sidebar-menu-label {
          color: rgba(233, 78, 91, 0.65);
        }

        .side-menu-icon {
          svg * {
            stroke: rgba(233, 78, 91, 0.65);
          }
        }
      }

      .logout {
        &:hover {
          .sidebar-menu-item {
            .sidebar-menu-label {
              color: rgba(233, 78, 91, 0.65);
            }

            .side-menu-icon {
              svg * {
                stroke: rgba(233, 78, 91, 0.65);
              }
            }
          }
        }
      }
    }
  }

  .sidebar-menu {
    padding: 0;

    &.child1,
    &.child2 {
      .sidebar-menu-item {
        padding: 6px 6px;
      }
    }

    &.child1,
    &.child2,
    &.child3 {

      .sidebar-menu-item {
        background-color: transparent !important;

        &:before {
          position: absolute;
          content: "\e404";
          font-family: "Font Awesome 6 Pro";
          font-size: 12px;
          inset-inline-start: -10px;
          opacity: 0.8;
        }

        &.active {
          background-color: transparent !important;
        }
      }

      li {
        padding: 0;
        position: relative;
      }
    }

    &.child1 li {
      padding-inline-start: 56px;
    }

    &.child2 li {
      padding-inline-start: 12px;
    }

    &.child3 li {
      padding-inline-start: 16px;
    }
  }

  .sidebar-menu-label {
    white-space: nowrap;
    color: #999;
    position: relative;
    font-size: 14px;
    font-weight: 700;
    line-height: 1;
    @include flexbox();
    align-items: center;
    text-transform: capitalize;
    font-weight: 600;
    transition: .3s;
  }

  .side-menu-icon {
    line-height: 0;
    font-size: 14px;
    text-align: center;
    @include border-radius(4px);

    svg {
      width: 16px;
      height: 16px;
    }

    svg * {
      transition: .3s;
      width: 16px;
      height: 16px;
      stroke: #999;
    }
  }

  .side-menu-angle {
    transform-origin: center;
    position: absolute;
    inset-inline-end: 20px;
    line-height: 1;
    font-size: 14px;
    transition: all 0.03s ease;
    opacity: 0.8;
  }
}


.close_sidebar {
  &.app-sidebar {
    @media #{$lg,$md,$sm,$xs} {
      inset-inline-start: 0px;
    }
  }
}

.app-sidebar {
  @media #{$lg,$md,$sm,$xs} {
    inset-inline-start: -300px;
  }

  &.collapsed {
    inset-inline-start: -300px;
  }

  // has folded class
  &.nav-folded {
    width: 80px;
    transition: 0.2s;

    .nav ul li {
      margin: 0 10px;
    }

    .category-name {
      display: none;
    }

    .sidebar-menu-item {
      padding: 10px 16px;
      @include inline-flex();

      .sidebar-menu-label,
      .category-name {
        display: none;
      }
    }

    .sidebar-logo {
      .main-logo {
        display: none;
      }

      .small-logo {
        display: block;
      }
    }

    .invite-card-content {
      display: none;
    }

    .invite-card-box {
      padding: 8px;
      margin: 10px 10px;
    }
  }

  // side nav hover
  .app-sidebar {
    &.nav-folded {
      &.side-nav-hover {
        .sidebar-menu-category {
          .category-name {
            display: block;
          }
        }
      }
    }
  }

  &.side-nav-hover {
    width: 290px;
    transition: all 0.3s ease;

    .sidebar-menu-item {
      .sidebar-menu-label {
        display: none;
      }
    }

    .sidebar-menu-category {
      .category-name {
        display: block !important;
      }
    }

    .sidebar-menu-item {
      @include flexbox();
    }

    .sidebar-logo {
      .main-logo {
        display: block;
      }

      .small-logo {
        display: none;
      }
    }

    .sidebar-menu-item {
      .sidebar-menu-label {
        display: block;
      }
    }

    .invite-card-box {
      padding: 8px 8px 8px 16px;
    }

    .invite-card-content {
      display: block;
      transition: .2s ease;
      opacity: 1;
    }
  }
}

.toggle-sidebar {
  position: absolute;
  top: 60px;
  inset-inline-end: -10px;
  z-index: 5;

  @media #{$xs,$sm,$md,$lg} {
    position: relative;
    top: inherit;
    inset-inline-end: inherit;
    z-index: 5;
  }

  &.active {
    .bar-icon {
      transform: rotate(-180deg);
    }
  }
}