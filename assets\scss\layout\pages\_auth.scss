@use '../../utils' as *;

/*----------------------------------------*/
/*  Authentication styles
/*----------------------------------------*/

.td-authentication-section {
    padding: 30px 0 50px;
}

.auth-main-box {
    max-width: 575px;
    display: grid;
    margin: 0 auto;
    background: #071220;
    border-radius: 30px;
    border-image: linear-gradient(180deg, rgba(115, 168, 248, 0.6) 0%, rgba(59, 87, 231, 0) 100%);
    padding: 45px 50px;
    position: relative;
    z-index: 1;

    @media #{$xs} {
        padding: 35px 25px;
    }

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        @include border-radius(30px);
        padding: 1px;
        background: linear-gradient(180deg, rgba(115, 168, 248, 0.6) 0%, rgba(59, 87, 231, 0) 100%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }
}

.auth-logo {
    margin-bottom: 40px;

    img {
        height: 28px;
    }
}

.auth-intro-contents {
    margin-bottom: 35px;

    .title {
        font-size: 20px;
        font-weight: 700;
    }

    .description {
        color: #999;
        font-weight: 500;
        margin-top: 10px;
        font-size: 14px;
    }
}

.auth-login-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 10px;
    margin-bottom: 20px;
}

.auth-divide {
    -moz-box-align: center;
    align-items: center;
    display: -moz-box;
    @include flexbox();
    padding: 27px 0 27px;

    .divider-line {
        border-top: 1px solid rgba($white, $alpha: .1);
        -moz-box-flex: 1;
        flex-grow: 1;
        height: 0;
    }

    .or {
        cursor: default;
        flex-shrink: 0;
        font-size: 16px;
        margin-inline-start: 10px;
        margin-inline-end: 10px;
        font-weight: 500;
    }
}

.auth-from-bottom-content {
    text-align: center;
}

.have-auth-account {
    margin-top: 12px;

    .description {
        color: #999999;
        font-size: 14px;
        font-weight: 500;
    }
}