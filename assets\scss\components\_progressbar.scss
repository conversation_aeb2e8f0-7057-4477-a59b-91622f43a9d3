@use '../utils' as *;

/*----------------------------------------*/
/* Progressbar styles
/*----------------------------------------*/
.ami-progress-bar {
	overflow: hidden;

	.progress {
		height: 12px;
		overflow: visible;

		.progress-bar {
			background-color: #5EA7FD;
			@include border-radius(10px);
			position: relative;
		}
	}

	&.lg-progress-bar {
		.progress {
			height: 15px !important;
		}
	}

	&.sm-progress-bar {
		.progress {
			height: 5px !important;
		}
	}

	&.multi-progress-bar {
		.progress-bar {
			border-radius: 0;
		}
	}
}

.ami-progress-title {
	font-size: 16px;
	font-weight: 500;
	color: var(--clr-text-label);

	span {
		font-weight: 500;
		font-size: 14px;
		color: rgba($heading, $alpha: 0.7);
	}
}

.ami-progress-head {
	@include flexbox();
	justify-content: space-between;
	margin-bottom: 7px;
}

.ami-progress-percentage {
	font-size: 14px;
	font-weight: 500;
	color: var(--td-heading);
	position: relative;
	padding-inline-start:23px;

	&::after {
		position: absolute;
		content: "";
		width: 14px;
		height: 14px;
		background-color: #5EA7FD;
		top: 50%;
		transform: translateY(-50%);
		inset-inline-start: 0;
		border-radius: 50%;
	}
}

.ami-progress-item {
	&:not(:last-child) {
		margin-bottom: 19px;
	}

	&:nth-child(2) {
		.progress {
			.progress-bar {
				background-color: #3ECB5D;
			}
		}

		.ami-progress-percentage {
			&::after {
				background-color: #3ECB5D;
			}
		}
	}

	&:nth-child(3) {
		.progress {
			.progress-bar {
				background-color: #FD5E5E;
			}
		}

		.ami-progress-percentage {
			&::after {
				background-color: #FD5E5E;
			}
		}
	}

	&:nth-child(4) {
		.progress {
			.progress-bar {
				background-color: #ED6E3A;
			}
		}

		.ami-progress-percentage {
			&::after {
				background-color: #ED6E3A;
			}
		}
	}

	&:nth-child(5) {
		.progress {
			.progress-bar {
				background-color: #5E6FFD;
			}
		}

		.ami-progress-percentage {
			&::after {
				background-color: #5E6FFD;
			}
		}
	}
}