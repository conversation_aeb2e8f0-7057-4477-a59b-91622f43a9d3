@use '../../utils' as *;

/*----------------------------------------*/
/*  Welcome promo gift styles
/*----------------------------------------*/
.welcome-promo-gift-popup {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 1099;
    background-color: rgba($black, $alpha: 0.3);
    transition: all 0.3s;
    margin: 0 auto;
    text-align: center;
    visibility: visible;
    opacity: 1;

    &.show {
        visibility: hidden;
        opacity: 0;
    }
}

.welcome-promo-gift-box {
    width: 600px;
    margin: 0 auto;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 30px;
    background: #091628;
    padding: 35px 50px 30px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    border: 2px solid #142A49;
    z-index: 1;

    @media #{$md} {
        width: 500px;
    }

    @media #{$xs,$sm} {
        width: 80%;
        padding: 25px 20px 20px;
    }

    &::before {
        position: absolute;
        content: "";
        top: 65px;
        background-color: var(--td-white);
        filter: blur(84.5px);
        width: 154px;
        height: 154px;
        left: 50%;
        transform: translateX(-50%);

        @media #{$xs,$sm} {
            width: 120px;
            height: 120px;
        }
    }

    .promo-gift-icon {
        width: 142px;
        margin: 0 auto;
        margin-bottom: 30px;

        @media #{$xs,$sm} {
            width: 100px;
            margin-bottom: 20px;
        }

        img {
            animation: promoBounce 1.2s infinite alternate;
        }
    }

    .promo-gift-contents {
        .promo-amount {
            color: var(--2, #A6EF67);
            font-size: 30px;
            font-weight: 900;
        }

        .promo-subtitle {
            font-weight: 500;
            margin-top: 2px;
        }

        .promo-text {
            font-size: 18px;
            max-width: 378px;
            margin: 0 auto;
            margin-bottom: 30px;
            color: rgba($white, $alpha: 0.8);
            @media #{$xs,$sm} {
                font-size: 16px;
            }
            @media #{$xxs} {
                font-size: 14px;
            }
        }
    }

    .promo-close {
        position: absolute;
        top: -20px;
        right: -20px;
        border: none;
        font-size: 20px;
        @media #{$xs,$sm} {
            right: -10px;
            top: -10px;
        }
        svg {
            @media #{$xs,$sm} {
                width: 28px;
                height: 28px;
            }
        }
    }
}

@keyframes promoBounce {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-10px);
    }
}