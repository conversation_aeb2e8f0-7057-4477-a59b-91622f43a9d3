@use '../../utils' as *;

/*----------------------------------------*/
/*  Financial card styles
/*----------------------------------------*/
.financial-card {
    background-color: #091628;
    border-radius: 16px;
    padding: 18px 18px;
    display: flex;
    justify-content: space-between;
    min-height: 198px;
    background-repeat: no-repeat;
    background-size: cover;

    @media #{$xs} {
        flex-direction: column;
    }

    .left-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .inner {
            .greeting {
                font-size: 16px;
                margin-bottom: 5px;
                font-weight: 700;
                color: var(--td-primary);
            }

            .name {
                font-size: 20px;
                margin-bottom: 6px;
            }

            .last-login {
                font-size: 14px;
            }

        }

        .uid-value {
            color: #E2E8F0;
            font-size: 14px;
            font-weight: 500;
        }

        .uid-title {
            background: hsla(0, 0%, 100%, .04);
            border-radius: 4px;
            padding: 4px 10px 4px 10px;
            display: inline-flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            height: 20px;
            position: relative;
            font-size: 12px;
            font-weight: 700;
        }

        .uid-bottom {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 6px;
        }

        .copy-icon {
            display: flex;
            align-items: center;

            i {
                width: 14px;
                height: 14px;
            }
        }
    }

    .right-section {
        flex: 1;
        position: relative;
        display: flex;
        padding-left: rem(18);
        margin-left: rem(18);

        @media #{$xs} {
            padding-left: rem(0);
            margin-left: rem(0);
            padding-top: rem(18);
            margin-top: rem(18);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            flex-wrap: wrap;
            gap: rem(6);
        }

        &::before {
            position: absolute;
            content: "";
            min-height: calc(100% + 36px);
            background: linear-gradient(241deg, rgba(9, 22, 40, 0.1) 0%, rgb(166, 239, 103) 50%, rgba(9, 22, 40, 0.1) 100%);
            top: -18px;
            left: 0;
            width: 1px;

            @media #{$xs} {
                display: none;
            }
        }

        .assets-contents {
            text-align: left;

            .total-assets {
                span {
                    font-size: 20px;
                    font-weight: 700;
                    color: var(--td-white);
                    display: block;
                    margin-bottom: rem(12);
                }
            }

            .asset-value {
                background: rgba(255, 255, 255, 0.04);
                border-radius: 5px;
                padding: 2px 14px 2px;
                display: flex;
                align-items: center;
                gap: rem(5);
                position: relative;
                margin-bottom: rem(10);

                .amount {
                    font-size: rem(16);
                    font-weight: 700;
                    color: var(--td-white);
                }

                .asset-select {
                    .select2-container {
                        min-width: 80px;
                    }
                    .select2-container--default {
                        .select2-selection--single {
                            height: 25px;
                            line-height: 25px;
                        }
                        .select2-selection {
                            border: 0;
                        }
                    }  
                }
            }
        }

        .others-actions {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: end;
            gap: rem(4);
        }

        .time-zone {
            text-align: end;
            span {
                background: rgba(255, 255, 255, 0.04);
                border-radius: 4px;
                padding: 4px 10px 4px 10px;
                display: inline-flex;
                align-items: center;
                height: 24px;
                backdrop-filter: blur(15px);
                font-size: 12px;
                line-height: 17px;
                font-weight: 700;
                margin-bottom: rem(8);
            }
        }

        .time-zone-value {
            .select2-container {
                min-width: 250px;
            }
            .select2-container--default {
                .select2-selection--single {
                    height: 25px;
                    line-height: 25px;
                }
                .select2-selection {
                    border: 0;
                }
            }            
        }

        .hide-info {
            cursor: pointer;
            padding: 5px 10px;
            height: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 4px;
            line-height: 1;
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--td-white);
            font-size: 12px;
            font-weight: 700;
            .icon {
                i {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }
}