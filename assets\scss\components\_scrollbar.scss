@use '../utils' as *;

/*----------------------------------------*/
/*  Scrollbar styles
/*----------------------------------------*/
[data-simplebar] {
  position: unset;
}

.simplebar-track {
  inset-inline-end: -2px;

  @include rtl {
    right: auto;
    left: -2px;
  }

  &.simplebar-vertical {
    top: 100px;
    width: 10px;
  }

  &.simplebar-horizontal {
    visibility: hidden !important;
  }
}

.simplebar-scrollbar {
  &:before {
    background: rgba(99, 98, 231, .2);
  }
}

[data-simplebar] {
  position: unset;
}

.simplebar-mask {
  top: 80px;
}

.main-sidebar {
  height: calc(100vh - 70px);
  -webkit-transition: color 1s ease;
  transition: color 1s ease;
  overflow: auto;
  margin-top: 20px;
  margin-bottom: 50px;
}

.simplebar-offset {
  height: calc(100vh - 70px);
}