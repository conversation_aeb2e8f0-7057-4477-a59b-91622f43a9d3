@use "../utils" as *;

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/
// alert-card for auth pages
.alert-card {
	height: 54px;
	background: var(--td-white);
	border-radius: 40px;
	@include inline-flex();
	align-items: center;
	justify-content: center;
	gap: 12px;
	padding: 12px 30px;

	.alert-contents {
		.description {
			color: var(--td-heading);
		}
	}
}

.alert-box {
	@include flexbox();
	align-items: center;
	justify-content: space-between;
	background-color: var(--td-heading);
	color: var(--td-white);
	padding: 15px 25px;
	@include border-radius(8px);
	transition: opacity 0.4s ease, transform 0.4s ease;
	position: fixed;
	inset-inline-start: 50%;
	transform: translateX(-50%);
	bottom: 60px;
	z-index: 99;

	.alert-content {
		@include flexbox();
		align-items: center;
		gap: 8px;
	}

	.alert-icon {
		flex: 0 0 auto;
	}

	.alert-message {
		margin: 0;
		color: var(--td-white);
	}

	.close-btn {
		color: var(--td-white);
		font-size: 16px;
		border: none;
		border-radius: 50%;
		position: absolute;
		inset-inline-end: -4px;
		top: -7px;
		@include box-shadow(0px 1px 2px rgba($black, $alpha: 0.25));

		svg {
			width: 18px;
			height: 18px;
		}
	}
}

.alert-box.has-success {
	background-color: #3ECB5D;
}

.alert-box.has-warning {
	background-color: #FD7E14;
}

.alert-box.has-danger {
	background-color: var(--td-danger);
}

.alert-box.hidden {
	opacity: 0;
	transform: translate(-50%, 20px);
	pointer-events: none;
}