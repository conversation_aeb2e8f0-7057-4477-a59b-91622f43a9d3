@use '../utils' as *;

/*----------------------------------------*/
/*  Select2 customize
/*----------------------------------------*/
.select2-dropdown {
    background: #212A3D;
}

.select2-container--default {
    .select2-results__option--highlighted[aria-selected] {
        background-color: var(--td-primary) !important;
        color: var(--td-heading) !important;
    }

    .select2-selection {
        &:hover {
            border-color: #82808b;
        }

        background-color: transparent;
        border-color: #212a3d !important;
    }

    .select2-selection--single {
        height: 50px;
        line-height: 50px;
        @include flexbox();
        align-items: center;

        .select2-selection__arrow {
            top: 6px;
        }
    }

    .select2-search--dropdown {
        .select2-search__field {
            height: 40px;
            background-color: transparent;
            border-color: rgba(255, 255, 255, 0.1);
            padding: 0 12px;
            ;
        }
    }
}

.select2-container--default.select2-container--focus {
    .select2-selection {
        border-color: var(--td-primary) !important;
    }
}

.select2-container--default.select2-container--open {
    .select2-selection {
        border-color: var(--td-primary) !important;
    }
}

.select2-primary {
    .select2-container--default {
        .select2-selection--multiple {
            .select2-selection__choice {
                background: rgba(115, 103, 240, .16) !important;
                color: var(--td-primary) !important;
            }
        }
    }
}

.select2-search__field {
    height: 40px;
}

.select2-container--default .select2-results__option {
    color: #999999;
    font-weight: 700;
    font-size: 14px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #999;
    font-weight: 700;
    font-size: 14px;
}

.defaults-select {
    .select2-container--default.select2-container--focus .select2-selection {
        border-width: 0;
    }

    .select2-container--default .select2-selection {
        border: 0;
    }

    .select2-container--default .select2-selection--single {
        height: inherit;
    }

    .select2-dropdown {
        min-width: 200px;
    }

    .select2-results__options {
        &::-webkit-scrollbar {
            width: 5px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        top: 0px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="%232f2b3d" fill-opacity="0.9"/></svg>');
        background-size: 20px;
        right: 10px;
    }
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    width: 20px !important;
    height: 100% !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    top: 0;
    right: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    position: absolute;
    background-repeat: no-repeat;
    background-size: 20px 19px;
    transform-origin: center;
    transition: transform .3s ease;
    width: 8px;
    height: 8px;
    border-bottom: 1.5px solid var(--td-text-primary);
    border-right: 1.5px solid var(--td-text-primary);
    position: absolute;
    top: 45%;
    transform: translateY(-50%) rotate(45deg);
}

.select2-container--default.select2-container--above.select2-container--open .select2-selection__arrow b {
    transform: rotate(222deg) !important;
}

.select2-dropdown.select2-dropdown--above {
    box-shadow: none !important;
    border: 1px solid rgba($white, $alpha: 0.1) !important;
}