<!doctype html>
<html class="no-js" lang="zxx">

<head>
   <meta charset="utf-8">
   <meta http-equiv="x-ua-compatible" content="ie=edge">
   <title>OTP Verification || PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange</title>
   <meta name="description" content="">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <!-- Place favicon.ico in the root directory -->
   <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
   <!-- CSS here -->
   <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
   <link rel="stylesheet" href="../assets/css/flag-icon.css">
   <link rel="stylesheet" href="../assets/css/select2.css">
   <link rel="stylesheet" href="../assets/css/styles.css">
   <link rel="stylesheet" href="../assets/css/satoshi.css">
</head>

<body>

   <!--[if lte IE 9]>
   <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
   <![endif]-->

   <!-- auth body-overlay -->
   <div class="auth-overlay-bg"></div>

   <!-- Pre loader start -->
   <div id="td-loadingDiv">
      <div class="td-loading-wrapper">
         <svg width="184" height="30" viewBox="0 0 184 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
               d="M0 8.73976C0 8.21184 0.263844 7.71884 0.703105 7.426L9.38732 1.63653C10.4366 0.936994 11.8421 1.68919 11.8421 2.95029V21.2602C11.8421 21.7882 11.5783 22.2812 11.139 22.574L2.45479 28.3635C1.40549 29.063 0 28.3108 0 27.0497V8.73976Z"
               fill="#3B57E7" />
            <path
               d="M8.68408 8.73976C8.68408 8.21184 8.94793 7.71884 9.38719 7.426L18.0714 1.63653C19.1207 0.936994 20.5262 1.68919 20.5262 2.95029V21.2602C20.5262 21.7882 20.2623 22.2812 19.8231 22.574L11.1389 28.3635C10.0896 29.063 8.68408 28.3108 8.68408 27.0497V8.73976Z"
               fill="#CCFF70" />
            <path
               d="M176.954 22.3422C172.858 22.3422 169.989 19.3611 169.989 15.0984C169.989 10.7799 172.803 7.79883 176.843 7.79883C180.966 7.79883 183.585 10.5571 183.585 14.8476V15.8785L173.221 15.9064C173.471 18.3303 174.753 19.5561 177.01 19.5561C178.876 19.5561 180.102 18.8318 180.492 17.5223H183.641C183.056 20.5313 180.548 22.3422 176.954 22.3422ZM176.87 10.5849C174.864 10.5849 173.639 11.6715 173.304 13.7332H180.214C180.214 11.8387 178.904 10.5849 176.87 10.5849Z"
               fill="#A6EF67" />
            <path
               d="M152.903 14.7915C152.903 10.7795 155.522 7.77051 159.45 7.77051C161.512 7.77051 163.128 8.6342 163.936 10.1108L164.131 8.21628H167.279V21.2831C167.279 25.8801 164.521 28.7498 160.063 28.7498C156.107 28.7498 153.404 26.4931 152.986 22.8154H156.385C156.608 24.5985 157.974 25.6572 160.063 25.6572C162.403 25.6572 163.908 24.1806 163.908 21.896V19.6114C163.044 20.893 161.345 21.701 159.367 21.701C155.466 21.701 152.903 18.7756 152.903 14.7915ZM156.33 14.7079C156.33 17.0203 157.806 18.7477 160.035 18.7477C162.376 18.7477 163.824 17.1039 163.824 14.7079C163.824 12.3676 162.403 10.7516 160.035 10.7516C157.778 10.7516 156.33 12.4511 156.33 14.7079Z"
               fill="#A6EF67" />
            <path
               d="M142.515 22.3422C139.589 22.3422 137.806 20.6427 137.806 18.0517C137.806 15.5163 139.645 13.9282 142.905 13.6775L147.028 13.371V13.0645C147.028 11.1979 145.914 10.4456 144.186 10.4456C142.18 10.4456 141.066 11.2814 141.066 12.7302H138.168C138.168 9.74909 140.62 7.79883 144.353 7.79883C148.059 7.79883 150.344 9.80481 150.344 13.6218V21.98H147.362L147.112 19.9462C146.527 21.3671 144.66 22.3422 142.515 22.3422ZM143.629 19.779C145.719 19.779 147.056 18.5253 147.056 16.4079V15.6835L144.186 15.9064C142.069 16.1014 141.261 16.7979 141.261 17.9123C141.261 19.1661 142.097 19.779 143.629 19.779Z"
               fill="#A6EF67" />
            <path
               d="M119.934 21.9801L115.477 8.2168H119.015L120.826 14.1233C121.132 15.2099 121.411 16.4079 121.662 17.6895C121.912 16.3522 122.163 15.4885 122.609 14.1233L124.504 8.2168H127.958L129.797 14.1233C129.964 14.6805 130.521 16.7422 130.689 17.6617C130.912 16.6308 131.357 14.987 131.608 14.1233L133.447 8.2168H137.041L132.277 21.9801H129.101L127.206 16.0179C126.649 14.2069 126.342 12.8974 126.231 12.2009C126.092 12.8417 125.841 13.8168 125.144 16.0736L123.25 21.9801H119.934Z"
               fill="#A6EF67" />
            <path
               d="M112.11 21.9797H108.711V11.0582H106.064V8.21636H108.711V3.92578H112.11V8.21636H114.785V11.0582H112.11V21.9797Z"
               fill="white" />
            <path
               d="M98.2143 22.3422C94.1187 22.3422 91.249 19.3611 91.249 15.0984C91.249 10.7799 94.063 7.79883 98.1028 7.79883C102.226 7.79883 104.845 10.5571 104.845 14.8476V15.8785L94.4809 15.9064C94.7316 18.3303 96.0132 19.5561 98.27 19.5561C100.137 19.5561 101.363 18.8318 101.753 17.5223H104.901C104.316 20.5313 101.808 22.3422 98.2143 22.3422ZM98.1307 10.5849C96.1247 10.5849 94.8988 11.6715 94.5645 13.7332H101.474C101.474 11.8387 100.165 10.5849 98.1307 10.5849Z"
               fill="white" />
            <path
               d="M81.1182 21.9795H77.7471V1.25098H81.1182V13.9834L86.4675 8.21621H90.7303L85.4924 13.677L90.6188 21.9795H86.7183L83.1242 16.1844L81.1182 18.274V21.9795Z"
               fill="white" />
            <path
               d="M61.5649 15.0705C61.5649 10.8078 64.3789 7.79883 68.4466 7.79883C72.2078 7.79883 74.771 9.8884 75.1332 13.2038H71.7342C71.3441 11.6436 70.2018 10.8357 68.5859 10.8357C66.4127 10.8357 64.964 12.4795 64.964 15.0705C64.964 17.6616 66.3013 19.2775 68.4744 19.2775C70.174 19.2775 71.372 18.4417 71.7342 16.9372H75.1611C74.7432 20.1412 72.0685 22.3422 68.4744 22.3422C64.2953 22.3422 61.5649 19.4447 61.5649 15.0705Z"
               fill="white" />
            <path
               d="M44.9683 15.07C44.9683 10.7794 48.0608 7.82617 52.3235 7.82617C56.5863 7.82617 59.6788 10.7794 59.6788 15.07C59.6788 19.3606 56.5863 22.3139 52.3235 22.3139C48.0608 22.3139 44.9683 19.3606 44.9683 15.07ZM48.3673 15.07C48.3673 17.5775 49.9832 19.277 52.3235 19.277C54.6639 19.277 56.2798 17.5775 56.2798 15.07C56.2798 12.5625 54.6639 10.863 52.3235 10.863C49.9832 10.863 48.3673 12.5625 48.3673 15.07Z"
               fill="white" />
            <path
               d="M28.5264 28.3602V8.21674H31.6747L31.8975 10.3063C32.7334 8.71824 34.5443 7.79883 36.6339 7.79883C40.5066 7.79883 43.0698 10.6128 43.0698 14.9312C43.0698 19.2218 40.7295 22.3422 36.6339 22.3422C34.5722 22.3422 32.7891 21.5343 31.9254 20.1412V28.3602H28.5264ZM31.9533 15.0984C31.9533 17.578 33.4856 19.2775 35.8259 19.2775C38.222 19.2775 39.6429 17.5502 39.6429 15.0984C39.6429 12.6466 38.222 10.8914 35.8259 10.8914C33.4856 10.8914 31.9533 12.6188 31.9533 15.0984Z"
               fill="white" />
         </svg>

         <div class="td-loading">
            <div class="td-loading-overlay"></div>
         </div>
      </div>
   </div>
   <!-- Pre loader start -->

   <!-- Header section start -->
   <header>
      <div class="header-area header-style-one is-auth-header">
         <div class="container">
            <div class="header-right">
               <div class="header-quick-actions d-flex align-items-center justify-content-end">
                  <div class="theme-switcher">
                     <!-- Theme toggle button for switching between light and dark mode -->
                     <button id="theme-toggle" class="action-icon theme-switcher" aria-label="Toggle Theme">
                        <!-- Light mode icon (visible in dark mode) -->
                        <span class="light-mode" aria-hidden="true">
                           <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M16.5 11.8831C15.5149 12.3286 14.4214 12.5766 13.2701 12.5766C8.93647 12.5766 5.42337 9.06352 5.42337 4.7299C5.42337 3.57851 5.67135 2.48505 6.11683 1.5C3.39432 2.73122 1.5 5.47102 1.5 8.65327C1.5 12.9869 5.0131 16.5 9.34672 16.5C12.529 16.5 15.2688 14.6056 16.5 11.8831Z" stroke="#080808" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                           </svg>
                        </span>
                        <!-- Dark mode icon (visible in light mode) -->
                        <span class="dark-mode" aria-hidden="true">
                           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M12 3V4.8M12 19.2V21M4.8 12H3M6.88271 6.88271L5.60991 5.60991M17.1173 6.88271L18.3901 5.60991M6.88271 17.121L5.60991 18.3938M17.1173 17.121L18.3901 18.3938M21 12H19.2M16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12Z" stroke="white" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                           </svg>
                        </span>
                    </button>
                  </div>
                  <div class="language-dropdown">
                     <div class="language-box language-nav">
                        <div class="translate_wrapper">
                           <div class="current_lang">
                              <div class="quick-action-item">
                                 <button type="button" class="action-icon notification-btn">
                                    <i class="solar--global-line-duotone"></i>    
                                 </button>
                              </div>
                           </div>
                           <div class="more_lang">
                              <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English</span></div>
                              <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                              <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                              <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </header>
   <!-- Header section end -->

   <!-- Body main wrapper start -->
   <main>

      <!-- Authentication section start -->
      <section class="td-authentication-section">
         <div class="container">
            <div class="auth-main-box">   
               <div class="auth-too-wrapper text-center">
                  <div class="auth-logo">
                     <a href="../themes/index.html">
                        <img src="../assets/images/logo/logo.svg" alt="logo">
                     </a>
                  </div>
                  <div class="auth-intro-contents">
                     <h3 class="title">OTP Verification</h3>
                     <p class="description">Enter your PocketWage Eamil Address</p>
                  </div>
               </div>
               <div class="auth-from-box">
                  <form id="otp-form" action="#">
                     <div class="row gy-3">
                        <div class="col-xlg-12">
                           <div class="td-form-group">
                              <div class="otp-verification">
                                 <input type="text" maxlength="1" class="control-form" autofocus>
                                 <input type="text" maxlength="1" class="control-form">
                                 <input type="text" maxlength="1" class="control-form">
                                 <input type="text" maxlength="1" class="control-form">
                                 <input type="text" maxlength="1" class="control-form">
                                 <input type="text" maxlength="1" class="control-form">
                              </div>
                          </div>
                        </div>
                        <div class="col-xlg-12">
                           <div class="auth-from-btn-wrap">
                              <button class="td-btn btn-secondary w-100" type="submit">Verify</button>
                           </div>
                        </div>
                     </div>
                 </form>
               </div>          
               <div class="auth-from-bottom-content">
                  <div class="have-auth-account">
                     <p class="description">Don’t receive code? <a class="td-underline-btn" href="#"> Resend code</a></p>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- Authentication section end -->

   </main>
   <!-- Body main wrapper end -->

   <!-- JS here -->
   <script src="../assets/js/jquery-3.7.1.min.js"></script>
   <script src="../assets/js/bootstrap.bundle.min.js"></script>
   <script src="../assets/js/iconify-icon.min.js"></script>
   <script src="../assets/js/select2.js"></script>
   <script src="../assets/js/iconify.min.js"></script>
   <script src="../assets/js/main.js"></script>
   <script>
      (function ($) {
         'use strict';

         const form = document.getElementById('otp-form');
         const inputs = form.querySelectorAll('.otp-verification input');
         const verifyButton = form.querySelector('button[type="submit"]');

         const KEY_CODES = {
            BACKSPACE: 8,
            ARROW_LEFT: 37,
            ARROW_RIGHT: 39
         };

         function handleInput(event) {
            const input = event.target;
            const nextInput = input.nextElementSibling;
            if (nextInput && input.value) {
               nextInput.focus();
               if (nextInput.value) {
                  nextInput.select();
               }
            }
         }

         function handlePaste(event) {
            event.preventDefault();
            const pasteData = event.clipboardData.getData('text').slice(0, inputs.length);
            inputs.forEach((input, index) => {
               input.value = pasteData[index] || '';
            });
         }

         function handleBackspace(event) {
            const input = event.target;
            if (!input.value) {
               const previousInput = input.previousElementSibling;
               if (previousInput) {
                  previousInput.focus();
               }
            }
         }

         function handleArrowNavigation(event, keyCode) {
            const input = event.target;
            if (keyCode === KEY_CODES.ARROW_LEFT) {
               const previousInput = input.previousElementSibling;
               if (previousInput) {
                  previousInput.focus();
               }
            } else if (keyCode === KEY_CODES.ARROW_RIGHT) {
               const nextInput = input.nextElementSibling;
               if (nextInput) {
                  nextInput.focus();
               }
            }
         }

         function setupInputEventListeners(input) {
            input.addEventListener('focus', event => {
               setTimeout(() => event.target.select(), 0);
            });

            input.addEventListener('input', handleInput);
            input.addEventListener('keydown', event => {
               if (event.keyCode === KEY_CODES.BACKSPACE) {
                  handleBackspace(event);
               } else if (event.keyCode === KEY_CODES.ARROW_LEFT || event.keyCode === KEY_CODES.ARROW_RIGHT) {
                  handleArrowNavigation(event, event.keyCode);
               }
            });
         }

         function setupFormEventListeners() {
            form.addEventListener('submit', event => {
               event.preventDefault(); // Prevent form submission for demo purposes
               const otpValue = Array.from(inputs).map(input => input.value).join('');
               console.log('OTP Value:', otpValue);
            });

            inputs[0].addEventListener('paste', handlePaste);
         }

         // Initialize the event listeners
         function initialize() {
            inputs.forEach(setupInputEventListeners);
            setupFormEventListeners();
         }

         // Run the initialization
         $(document).ready(initialize);

      })(jQuery);
   </script>
</body>

</html>