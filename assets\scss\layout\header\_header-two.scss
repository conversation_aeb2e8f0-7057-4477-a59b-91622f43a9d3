@use '../../utils' as *;

/*----------------------------------------*/
/*  Header two styles
/*----------------------------------------*/

// Heder one styles
.header-style-two {
  border-bottom: 1px solid rgba($white, $alpha: 0.1);
  background-color: #0F0F0F;

  @media #{$xs,$sm,$md} {
    padding: 18px 0;
  }

  .header-inner {
    @include flexbox();
    align-items: center;
    justify-content: space-between;
  }

  .header-logo {
    a {
      display: block
    }

    img {
      height: 30px;

      @media #{$xs} {
        height: 26px;
        object-fit: cover;
      }
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    column-gap: 80px;

    @media #{$lg} {
      column-gap: 16px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    column-gap: 20px;
  }

  .header-btns-wrap {
    display: flex;
    align-items: center;
    column-gap: 10px;
  }

  .header-quick-actions {
    column-gap: 16px;

    @media #{$xs,$sm,$md,$lg} {
      column-gap: 8px;
    }

    @media #{$xxs} {
      column-gap: 6px;
    }
  }

  .td-main-menu {
    nav {
      >ul {
        >li {
          >a {
            padding: 31px 5px;
          }
        }
      }
    }
  }
}