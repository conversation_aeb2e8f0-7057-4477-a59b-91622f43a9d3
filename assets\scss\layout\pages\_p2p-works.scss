@use '../../utils' as *;

/*----------------------------------------*/
/*  P2p works styles
/*----------------------------------------*/
.p2p-works-area {
  background: linear-gradient(90deg, rgba(14, 27, 44, 0.5) 0%, rgba(14, 27, 44, 0) 50.9469032288%, rgba(14, 27, 44, 0.5) 100%);
  border-radius: 24px;
  position: relative;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  padding: 100px 30px;

  @media #{$sm,$md,$lg} {
    padding: 60px 30px;
  }

  @media #{$xs} {
    padding: 60px 15px;
  }
}

.p2p-works-wrapper {
  max-width: 1320px;
  margin: 0 auto;
}

.p2p-works-grid {
  display: flex;
  grid-template-columns: repeat(2, 1fr);
  justify-content: space-between;
  border-radius: 40px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.1);
  border-width: 1px;
  padding: 50px;
  row-gap: 30px;
  column-gap: 30px;

  @media #{$xs,$lg,$xl} {
    padding: 30px 30px;
  }

  @media #{$xs,$sm,$md} {
    display: grid;
  }

  @media #{$xs} {
    grid-template-columns: 1fr;
  }
}

.p2p-works-item {
  text-align: center;
  max-width: 260px;

  @media #{$xs} {
    margin: auto;
    width: 100%
  }

  &:not(:last-child) {
    position: relative;

    &::before {
      position: absolute;
      content: "";
      height: 75px;
      width: 1px;
      right: -40%;
      background: rgba(255, 255, 255, 0.1);
      top: 50%;
      transform: translateY(-50%);

      @media #{$xl} {
        right: -20px;
      }

      @media #{$md} {
        right: -40px;
      }

      @media #{$xs,$sm} {
        display: none;
      }
    }
  }

  .icon {
    img {
      width: rem(60);
      height: rem(60);
    }

    margin-bottom: rem(20);
  }

  .contents {
    .title {
      font-size: 20px;
      font-weight: 700;
    }

    .description {
      margin-top: rem(8)
    }
  }
}