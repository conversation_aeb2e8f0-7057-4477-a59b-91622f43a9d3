@use '../../utils' as *;

/*----------------------------------------*/
/* Trading View styles
/*----------------------------------------*/
.trading-view-glow {
    .glow-one {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: -1;
    }

    .glow-two {
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
    }
}

.support-currency {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50px;
}

.trading-view-intro {
    .thumb {
        background-color: #0E1F37;
        padding: 30px;
        border-radius: 30px;
        position: relative;
        z-index: 1;

        &::before {
            position: absolute;
            content: "";
            inset: 0;
            @include border-radius(30px);
            padding: 5px;
            background: linear-gradient(180deg, #425D82 0%, #0E141C 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }
    }
}

// Style two
.trading-view-intro-two {
    margin-right: -78%;
    padding-right: 120px;

    @media #{$xl} {
        margin-right: -39%;
    }

    @media #{$lg} {
        padding-right: 0;
    }

    @media #{$xs} {
        margin-right: 0;
        padding-right: 0;
    }

    .thumb {

        img {
            width: 100%;
        }
    }
}