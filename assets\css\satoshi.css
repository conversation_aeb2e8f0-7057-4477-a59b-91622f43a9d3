/**
* @license
*
* Font Family: <PERSON><PERSON>
* Designed by: <PERSON><PERSON>
* URL: https://www.fontshare.com/fonts/satoshi
* © 2025 Indian Type Foundry
*
* <PERSON><PERSON> Light 
* <PERSON>shi LightItalic 
* Satoshi Regular 
* Satoshi Italic 
* <PERSON><PERSON> Medium 
* <PERSON>shi MediumItalic 
* Satoshi Bold 
* <PERSON>shi BoldItalic 
* <PERSON><PERSON> Black 
* <PERSON><PERSON> BlackItalic 
* <PERSON>shi Variable (Variable font)
* Satoshi VariableItalic (Variable font)

*
*/
@font-face {
  font-family: 'Satoshi-Light';
  src: url('../fonts/satoshi/Satoshi-Light.woff2') format('woff2'),
       url('../fonts/satoshi/<PERSON>shi-Light.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Light.ttf') format('truetype');
  font-weight: 300;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'Satoshi-LightItalic';
  src: url('../fonts/satoshi/<PERSON><PERSON>-LightItalic.woff2') format('woff2'),
       url('../fonts/satoshi/<PERSON><PERSON>-LightItalic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-LightItalic.ttf') format('truetype');
  font-weight: 300;
  font-display: swap;
  font-style: italic;
}
@font-face {
  font-family: 'Satoshi-Regular';
  src: url('../fonts/satoshi/Satoshi-Regular.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Regular.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'Satoshi-Italic';
  src: url('../fonts/satoshi/Satoshi-Italic.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Italic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Italic.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
  font-style: italic;
}
@font-face {
  font-family: 'Satoshi-Medium';
  src: url('../fonts/satoshi/Satoshi-Medium.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Medium.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Medium.ttf') format('truetype');
  font-weight: 500;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'Satoshi-MediumItalic';
  src: url('../fonts/satoshi/Satoshi-MediumItalic.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-MediumItalic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-MediumItalic.ttf') format('truetype');
  font-weight: 500;
  font-display: swap;
  font-style: italic;
}
@font-face {
  font-family: 'Satoshi-Bold';
  src: url('../fonts/satoshi/Satoshi-Bold.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Bold.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Bold.ttf') format('truetype');
  font-weight: 700;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'Satoshi-BoldItalic';
  src: url('../fonts/satoshi/Satoshi-BoldItalic.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-BoldItalic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-BoldItalic.ttf') format('truetype');
  font-weight: 700;
  font-display: swap;
  font-style: italic;
}
@font-face {
  font-family: 'Satoshi-Black';
  src: url('../fonts/satoshi/Satoshi-Black.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Black.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Black.ttf') format('truetype');
  font-weight: 900;
  font-display: swap;
  font-style: normal;
}
@font-face {
  font-family: 'Satoshi-BlackItalic';
  src: url('../fonts/satoshi/Satoshi-BlackItalic.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-BlackItalic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-BlackItalic.ttf') format('truetype');
  font-weight: 900;
  font-display: swap;
  font-style: italic;
}
/**
* This is a variable font
* You can control variable axes as shown below:
* font-variation-settings: wght 900.0;
*
* available axes:
'wght' (range from 300.0 to 900.0
*/
@font-face {
  font-family: 'Satoshi-Variable';
  src: url('../fonts/satoshi/Satoshi-Variable.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-Variable.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-Variable.ttf') format('truetype');
  font-weight: 300 900;
  font-display: swap;
  font-style: normal;
}
/**
* This is a variable font
* You can control variable axes as shown below:
* font-variation-settings: wght 900.0;
*
* available axes:
'wght' (range from 300.0 to 900.0
*/
@font-face {
  font-family: 'Satoshi-VariableItalic';
  src: url('../fonts/satoshi/Satoshi-VariableItalic.woff2') format('woff2'),
       url('../fonts/satoshi/Satoshi-VariableItalic.woff') format('woff'),
       url('../fonts/satoshi/Satoshi-VariableItalic.ttf') format('truetype');
  font-weight: 300 900;
  font-display: swap;
  font-style: italic;
}

