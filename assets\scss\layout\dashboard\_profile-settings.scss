@use '../../utils' as *;

/*----------------------------------------*/
/*  Profile settings styles
/*----------------------------------------*/
// Sidebar css
.profile-sidebar-left {
	width: 280px;
	flex: 0 0 auto;
	background: #091628;
	height: 100vh;
	border-radius: 8px;

	@media #{$md,$lg} {
		width: 245px;
	}

	@media #{$xs,$sm} {
		width: 100%;
		height: max-content;
	}

	.heading-contents {
		padding: 18px 30px 18px;
		border-bottom: 1px solid rgba(255, 255, 255, 0.1);

		.title {
			font-size: 20px;
			font-weight: 700;
		}
	}

	.sidebar {
		padding: 18px 18px 18px;

		nav {
			ul {
				padding: 0;
				position: relative;

				&::before {
					position: absolute;
					content: "";
					width: 2px;
					height: 100%;
					background: rgba(217, 217, 217, 0.1);
					border-radius: 30px;
				}

				li {
					list-style: none;
					&.active {
						a {
							color: var(--td-primary);
						}
					}

					&.has-danger {
						.link {
							color: var(--td-danger);
							font-size: 14px;
							font-weight: 700;

							&:hover {
								background-color: rgba($danger, $alpha: 0.2);
								color: var(--td-danger);
							}
						}
					}

					&:not(:last-child) {
						margin-bottom: 12px;
					}

					.link {
						padding: 0rem 1rem;
						display: block;
						@include border-radius(6px);
						transition: 0.2s;
						text-align: left;
						display: inline-flex;
						align-items: center;
						font-size: 14px;
						font-weight: 700;

						&:hover {
							color: var(--td-primary);
						}

						&.active {
							color: var(--td-primary);
						}
					}
				}
			}
		}
	}
}

// Profile settings
.profile-settings-area {
	@include flexbox();

	@media #{$xs,$sm} {
		flex-direction: column;
		gap: 18px;
	}

	.profile-right-content {
		flex-grow: 1;
		margin-inline-start: 18px;

		@media #{$xs,$sm} {
			margin-inline-start: 0;
		}
	}

	.profile-btns {
		@include flexbox();
		gap: 8px;
		flex-wrap: wrap;
	}
}

// Toggle actions
.profile-quick-action-item {
	@include gridbox();
	grid-template-columns: 276px 1fr;
	justify-content: space-between;
	gap: 24px 16px;

	@media #{$xs,$sm,$md} {
		grid-template-columns: 1fr;
	}

	&:not(:last-child) {
		margin-bottom: 30px;
		padding-bottom: 30px;
		border-bottom: 1px solid rgba($heading, $alpha: 0.1);
	}
}

// Toggle group
.toggle-group {
	@include flexbox();
	flex-direction: column;
	gap: 30px;

	.toggle-item {
		@include gridbox();
		grid-template-columns: 1fr auto;
		align-items: start;

		@media #{$xs,$sm,$md} {
			grid-template-columns: 62px 1fr;
		}

		.toggle-label {
			h5 {
				margin: 0;
				font-size: 16px;
			}
		}

		.toggle-switch {
			position: relative;
			display: inline-block;
			width: 40px;
			height: 20px;

			input {
				opacity: 0;
				width: 0;
				height: 0;

				&:checked+.slider {
					background-color: var(--td-green);
				}

				&:checked+.slider:before {
					transform: translate(18px, -50%);
				}
			}

			.slider {
				position: absolute;
				cursor: pointer;
				top: 0;
				inset-inline-start: 0;
				inset-inline-end: 0;
				bottom: 0;
				background-color: #ccc;
				@include border-radius(6px);
				transition: 0.4s;
				height: 22px;
				width: 40px;

				&:before {
					position: absolute;
					content: "";
					height: 14px;
					width: 14px;
					left: 4px;
					top: 50%;
					background-color: var(--td-white);
					border-radius: 50%;
					transition: 0.4s;
					transform: translateY(-50%);
				}
			}
		}
	}
}

// Two Factor Authentication
.two-fa-auth-wrapper {
	.contents {

		.description {
			font-size: 0.95rem;
			color: rgba($white, $alpha: 0.8);
			line-height: 1.6;
			margin-bottom: 26px;
		}
	}

	.qr-code {
		p {
			color: var(--td-primary);
		}

		.thumb {
			max-width: 336px;
			margin-top: 35px;

			@media #{$xs} {
				max-width: 236px;
			}

			img {
				@include border-radius(16px);
			}
		}
	}
}