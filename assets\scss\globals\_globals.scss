@use '../utils' as *;

/*-----------------------------------------------------------------------------------

  Project Name:  PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange
  Author: Tdevs
  Support: 
  Description:  PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange
  Version: 1.0
  
-----------------------------------------------------------------------------------

/*----------------------------------------*/
/*   Globals Default
/*----------------------------------------*/

// Google Fonts

/*----------------------------------------
  <PERSON>trap customize
-----------------------------------------*/
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
	--bs-gutter-x: 30px;
}

@media (min-width: 1601px) {

	.container,
	.container-lg,
	.container-md,
	.container-sm,
	.container-xl,
	.container-xxl {
		max-width: 1350px;
	}
}

.row {
	--bs-gutter-x: 30px;
}

.g-0,
.gx-0 {
	--bs-gutter-x: ;
}

.g-0,
.gy-0 {
	--bs-gutter-y: 0
}

.g-10,
.gx-10 {
	--bs-gutter-x: 10px;
}

.gy-12 {
	--bs-gutter-y: 12px;
}

.gy-14 {
	--bs-gutter-y: 14px;
}

.gx-14 {
	--bs-gutter-x: 14px;
}

.g-1,
.gx-1 {
	--bs-gutter-x: 0.25rem;
}

.g-2,
.gx-2 {
	--bs-gutter-x: 0.5rem;
}

.g-3,
.gx-3 {
	--bs-gutter-x: 1rem;
}

.g-20 {
	--bs-gutter-x: 20px;
	--bs-gutter-y: 20px;
}

.gy-20 {
	--bs-gutter-y: 20px;
}

.gx-20 {
	--bs-gutter-x: 20px;
}

.gy-20 {
	--bs-gutter-y: 20px;
}

.gx-24 {
	--bs-gutter-x: 24px;
}

.gy-24 {
	--bs-gutter-y: 24px;
}

.g-30,
.gx-30 {
	--bs-gutter-x: 30px;
}

.g-30,
.gy-30 {
	--bs-gutter-y: 30px;
}

.g-40,
.gx-40 {
	--bs-gutter-x: 40px;
}

.g-40,
.gy-40 {
	--bs-gutter-y: 40px;
}

.g-50,
.gx-50 {
	--bs-gutter-x: 50px;
}

.g-50,
.gy-50 {
	--bs-gutter-y: 50px;
}

.g-60,
.gy-60 {
	--bs-gutter-y: 60px;
}

/*----------------------------------------
  Body Overlay 
-----------------------------------------*/
.body-overlay {
	background-color: $black;
	height: 100%;
	width: 100%;
	position: fixed;
	top: 0;
	z-index: 9999;
	inset-inline-start: 0;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all 0.3s 0s ease-out;
	-moz-transition: all 0.3s 0s ease-out;
	-ms-transition: all 0.3s 0s ease-out;
	-o-transition: all 0.3s 0s ease-out;
	transition: all 0.3s 0s ease-out;

	&.opened {
		opacity: 0.7;
		visibility: visible;
	}
}