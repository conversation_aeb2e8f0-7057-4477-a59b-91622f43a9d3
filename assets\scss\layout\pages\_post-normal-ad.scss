@use '../../utils' as *;

/*----------------------------------------*/
/*  Market card styles
/*----------------------------------------*/
.multi-steps-wrapper {
    display: table;
    table-layout: fixed;
    width: 100%;
    padding-top: 50px;

    @media #{$xs,$sm} {
        display: flex;
        table-layout: fixed;
        width: 100%;
        padding-top: 50px;
        flex-direction: column;
        gap: 50px;
    }

    .step {
        text-align: center;
        display: table-cell;
        position: relative;

        &.active {
            .step-line {
                background-color: var(--td-primary);
            }

            .step-button {
                border: 1px solid #2c4635;
                color: var(--td-white);

                .arrow {
                    display: block;
                }

                &::before {
                    background-color: var(--td-primary);
                }
            }
        }
    }

    .step-line {
        cursor: pointer;
        user-select: none;
        background-color: rgba($white, $alpha: .1);
        height: 2px;
        position: absolute;
        bottom: calc(50% + 0px);
        width: 100%;
        z-index: -1;
        left: 0;
    }

    .step-button {
        background-color: #0E1B2C;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        transition: background-color 0.3s ease;
        position: relative;
        top: -48px;
        font-size: 14px;
        font-weight: 700;
        color: var(--td-text-primary);

        @media #{$xs,$sm,$md} {
            font-size: 14px;
            padding: 10px 10px;
        }

        .arrow {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: -1;
            display: none;

            &::after {
                width: 20px;
                height: 20px;
                content: "";
                bottom: 0px;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                background-color: #0e1b2c;
                z-index: 1
            }


            &::before {
                position: absolute;
                content: "";
                background-image: url(../images/icons/border-arrow-down.svg);
                bottom: -9px;
                background-repeat: no-repeat;
                width: 18px;
                height: 19px;
                left: 50%;
                transform: translateX(-50%);
                background-size: cover;
                background-position: center;
                z-index: -5;
            }
        }

        &::after {
            width: 0;
            height: 0;
            border-inline-start: 7px solid transparent;
            border-inline-end: 7px solid transparent;
            border-top: 7px solid #0e1b2c;
            content: "";
            bottom: -7px;
            position: absolute;
            left: 50%;
            z-index: 1;
            transform: translateX(-50%);
        }

        &::before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            background-color: var(--td-text-primary);
            z-index: 1;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 50%;
        }
    }
}

.post-trade-form-box {
    background: #0e1b2c;
    border-radius: 16px;
    max-width: 666px;
    padding: 20px;

    @media #{$xs,$sm,$md} {
        max-width: 100%;
    }

    .price-details {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 18px;
        margin-top: 30px;
        margin-bottom: 40px;
    }

    .price-details div {
        background: rgba(255, 255, 255, 0.04);
        border-radius: 8px;
        padding: 7px 16px 7px;
        min-width: 160px;

        label {
            font-size: 14px;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
        }

        .currency {
            color: var(--td-white);
            font-size: 14px;
            font-weight: 700;
        }
    }

    .payment-contents {
        h6 {
            font-size: 14px;
        }

        span {
            font-size: 12px;
        }
    }
    .payment-cale {
        @media #{$xxs} {
            flex-direction: column;
            text-align: center;
            .approximately-equal {
                margin-top: 0 !important;
            }
        }
    }
}

// post buy sell tabs
.post-buy-sell-tabs {
    .td-tab {
        .nav-tabs {
            display: flex;
            gap: 16px 20px;
            flex-wrap: wrap;

            .nav-link {
                background: rgba(255, 255, 255, 0.06);
                border-radius: 8px;
                padding: 0px 16px 0px 16px;
                font-size: 16px;
                font-weight: 700;
                height: 50px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: var(--td-text-primary);
                position: relative;

                &::before {
                    position: absolute;
                    content: "";
                    width: 22px;
                    height: 2px;
                    background-color: var(--td-text-primary);
                    bottom: 10px;
                    border-radius: 30px;
                }

                &.active {
                    color: var(--td-primary);

                    &::before {
                        background-color: var(--td-primary)
                    }
                }
            }
        }
    }
}