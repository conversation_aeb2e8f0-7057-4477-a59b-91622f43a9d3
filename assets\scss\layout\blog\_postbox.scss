@use '../../utils' as *;

/*----------------------------------------*/
/*  Postbox styles
/*----------------------------------------*/
.postbox-main-wrapper {
	padding-right: 30px;

	@media #{$xs,$sm,$md,$lg} {
		padding-right: 0;
	}
}

.postbox-details-contents {
	p {
		margin-bottom: 30px;
	}

	h3 {
		line-height: 1.27;
		margin-bottom: 25px;
	}

	h5 {
		padding-bottom: 10px;
		border-bottom: 1px solid rgba($white, $alpha: 0.1);
		margin-bottom: 10px;
	}

	h6 {
		padding-bottom: 10px;
	}

	ul {
		margin-bottom: 30px;

		li {
			list-style: none;
			padding-left: 18px;
			position: relative;

			&::before {
				position: absolute;
				content: "";
				top: 10px;
				left: 0;
				width: 8px;
				height: 8px;
				background: #A6EF67;
				border-radius: 50%;
			}

			&:not(:last-child) {
				margin-bottom: 4px;
			}
		}
	}
}

.postbox-img {
	margin-bottom: 25px;

	img {
		@include border-radius(16px);
	}
}

// postbox share 
.postbox-share {
	@include flexbox();
	align-items: center;
	gap: 16px;

	a {
		font-size: 16px;
		color: #BBBBBB;
		transition: all 0.3s ease-in-out;
		border-radius: 6px;
		border-style: solid;
		border: 1px solid #bbbbbb;
		display: flex;
		flex-direction: row;
		gap: 10px;
		align-items: center;
		justify-content: center;
		width: 30px;
		height: 30px;

		&:hover {
			background-color: var(--td-primary);
			border-color: var(--td-primary);
		}
	}
}

// Tag cloud
.tagcloud-items {
	@include flexbox();
	gap: 8px;
	align-items: self-start;
}

.tagcloud-box {
	@include flexbox();
	align-items: center;
	flex-wrap: wrap;
	gap: 8px 8px;

	a {
		font-size: 14px;
		@include inline-flex();
		align-items: center;
		position: relative;
		text-transform: capitalize;
		background: rgba(255, 255, 255, 0.04);
		background-color: rgba(114, 128, 255, 0.1);
		border: 1px solid rgba(114, 128, 255, 0.2);
		color: rgba(8, 8, 8, 0.6);
		padding: 5px 14px;
		@include border-radius(4px);
		cursor: pointer;
		font-weight: 600;
	}
}

// sidebar-widget 
.sidebar-sticky {
	position: sticky;
	top: 80px;
}

.sidebar-widgets-wrapper {
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.sidebar-widget {
	background: #091628;
	border-radius: 16px;
	padding: 25px 30px 30px;

	@media #{$xxs} {
		padding: 16px 16px 16px;
	}
}

.sidebar-wrapper {
	@media #{$xs,$sm,$md} {
		padding-inline-start: 0;
	}
}

.sidebar-widget-title {
	position: relative;
	display: inline-block;
	font-weight: 500;
	font-size: 22px;
	margin-bottom: 20px;

	@media #{$xs} {
		margin-bottom: 18px;
	}
}