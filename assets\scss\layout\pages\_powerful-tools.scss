@use '../../utils' as *;

/*----------------------------------------*/
/* Powerful tool styles
/*----------------------------------------*/
.powerful-tools {
    background-color: #0A1729;
    padding: 20px 20px;
    border-radius: 16px;
    display: flex;
    align-items: self-start;
    gap: 18px;
    height: 100%;

    .icon {
        flex: 0 0 auto;

        img {
            width: rem(40);
            height: rem(40);
        }
    }

    .contents {
        .title {
            font-size: rem(20);
            margin-bottom: rem(8);

            @media #{$xs} {
                font-size: rem(18);
            }
        }
    }

    &.style-two {
        display: flex;
        flex-direction: column;
        background-color: #0F0F0F;
        margin-bottom: 30px;
        height: max-content;
    }
}

.powerful-tools-items-2 {
    margin-top: 80px;
    .row {
        & [class*="col"] {
            &:nth-child(2n) {
                .powerful-tools {
                    margin-top: -30px;
                }
            }

            &:nth-child(5) {
                .powerful-tools {
                    margin-top: -60px;
                }
            }
        }
    }
}