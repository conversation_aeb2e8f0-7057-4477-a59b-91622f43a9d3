@use '../../utils' as *;

/*----------------------------------------*/
/*  Header actions Styles
/*----------------------------------------*/
.currency-switcher {
    position: relative;

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        right: -6px;
    }

    .select2-container--default:not([dir=rtl]) .select2-selection--single .select2-selection__rendered {
        padding-left: 0;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        padding-right: 26px;
    }

    .defaults-select .select2-dropdown {
        min-width: 100px;
    }
}

.quick-action-item {
    .action-icon {
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 100px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--td-white);

        @media #{$xs} {
            width: 28px;
            height: 28px;

            i {
                width: 16px;
                height: 16px;
            }
        }

        &.notification-btn {
            -webkit-animation: tada 1.5s ease infinite;
            animation: tada 1.5s ease infinite;
        }
    }
}

// Profile contents
.profile-card-box {
    @include flexbox();
    align-items: end;
    justify-content: space-between;
    gap: 12px 12px;
    flex-wrap: wrap;
}

.profile-card {
    .profile-title {
        font-size: clamp(2rem, 1.5rem + 2vw, 2rem);
        margin-bottom: 10px;
    }
}

.create-project-container {
    position: relative;
}

.create-project-btn {
    padding: 10px 20px;
    background-color: #f5f7fa;
    border: 1px solid var(--td-border-primary);
    @include border-radius(5px);
    cursor: pointer;
}

// buttons-dropdown-menu
.buttons-dropdown-menu {
    display: none;
    position: absolute;
    top: calc(100% + 5px);
    inset-inline-end: 0;
    width: 220px;
    background: var(--td-white);
    border: 1px solid rgba(171, 178, 225, 0.3);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
    @include border-radius(8px);
    z-index: 1;

    ul {
        list-style: none;
        padding: 10px;
        margin: 0;
    }

    li {
        padding: 8px 15px;
        cursor: pointer;
        @include border-radius(8px);
        @include flexbox();
        align-items: center;
        gap: 8px;
        font-size: 14px;

        svg {
            height: 16px;
            width: 16px;
        }

        &:hover {
            background-color: var(--td-alice-blue);
        }
    }
}

// Notification Styles
.notification-panel-box {
    position: relative;
}

.notification-panel {
    width: 415px;
    position: absolute;
    top: calc(100% + 12px);
    backdrop-filter: blur(30px);
    background: #171C35;
    @include border-radius(8px);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
    visibility: hidden;
    display: block;
    inset-inline-end: 0;
    z-index: 9;
    transform: translateY(-20px);
    opacity: 0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);

    @media #{$xs} {
        width: 350px;
        inset-inline-end: -66px;
    }

    &.active {
        visibility: visible;
        opacity: 1;
        transform: translateY(0px);
    }

    .notification-item {
        &:not(:last-child) {
            margin-bottom: 12px;
        }
    }
}

.notifications-inner {
    padding: 20px 20px 20px;

    @media #{$xs} {
        padding: 12px 12px 12px;
    }
}

.notification-header {
    @include flexbox();
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    padding: 0px 0px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 18px 20px 18px;

    @media #{$xs} {
        padding: 12px 12px 12px;
    }

    h3 {
        font-size: 20px;
        font-weight: 500;
        color: var(--td-white);
    }
}

.notification-btn-close {
    width: 32px;
    height: 32px;
    @include inline-flex();
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 2;
    font-size: 20px;
}

.notifications-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    @media #{$xs} {
        margin-bottom: 12px;
    }

    button {
        font-size: 14px;
        font-weight: 500;

        &:hover {
            color: var(--td-white);
        }
    }
}

.notification-list {
    display: flex;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 10px 10px;
    border-radius: 16px;

    .time {
        color: #a0a0a0;
        font-size: 14px;
        flex: 0 0 auto;
    }

    .inner-item {
        display: flex;
        align-items: self-start;
        column-gap: 12px;

        .icon {
            width: 24px;
            height: 24px;
            flex: 0 0 auto;
        }

        .contents {
            .title {
                font-weight: 500;
                font-size: 14px;
            }

            .message {
                margin-top: 5px;
                color: #7C7C7C;
                font-size: 14px;
            }
        }
    }
}

.notifications-lists {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
}



// user notifications
.notifications-box {
    .notifications-drop-btn {
        &::after {
            display: none;
        }
    }

    .dropdown-menu {
        background: #101016;
        backdrop-filter: blur(50px);
        @include border-radius(20px);
        width: 442px;
        top: 150% !important;
        padding: 24px 24px 24px;
        border: 0;
        inset-inline-end: 0;

        @media #{$xs,$sm,$md} {
            transform: translateX(35%);
        }

        @media #{$xxs} {
            transform: translateX(45%);
        }

        @media #{$xxs} {
            width: 350px;
            padding: 15px 15px;
        }

        @media (max-width: 360px) {
            width: 300px;
            padding: 15px 15px;
            inset-inline-end: -15px;
        }

        &:before {
            position: absolute;
            content: "";
            inset: 0;
            @include border-radius(20px);
            padding: 2px;
            background: linear-gradient(139.9deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }
    }

    .notifications-top-content {
        @include flexbox();
        align-items: center;
        justify-content: space-between;
        margin-bottom: 27px;

        @media #{$xxs} {
            margin-bottom: 17px;
        }

        .title {
            color: var(--td-white);
            font-size: 16px;
            font-weight: 800;

            @media #{$xxs} {
                font-size: 14px;
            }
        }

        .link {
            color: var(--td-white);
            font-size: 14px;
            font-weight: 500;

            @media #{$xxs} {
                font-size: 12px;
            }

            &:hover {
                color: var(--td-primary);
            }
        }
    }

    .notifications-info-wrapper {
        height: 280px;
        overflow-y: scroll;
        scrollbar-width: thin;
        padding-inline-end: 5px;
    }

    .notifications-info-list {
        ul {
            li {
                list-style: none;

                .list-item {
                    @include flexbox();
                    align-items: start;
                    gap: 10px;
                    border: 1px solid rgba($color: $white, $alpha: .1);
                    @include border-radius(12px);
                    padding: 10px 10px;

                    .content {
                        .title {
                            font-size: 14px;
                            font-weight: 700;
                            color: var(--td-white);

                            @media #{$xxs} {
                                font-size: 12px;
                            }
                        }

                        .info {
                            font-size: 11px;
                            color: var(--td-white);
                        }
                    }

                    &:hover {
                        background: rgba($color: $white, $alpha: .1);
                    }
                }

                &:not(:last-child) {
                    margin-bottom: 6px;
                }
            }
        }
    }

    .notifications-bottom-content {
        margin-top: 24px;

        @media #{$xxs} {
            margin-top: 14px;
        }

        .notifications-btn {
            background: rgba($white, $alpha: 0.08);
            border: 1px solid rgba($white, $alpha: 0.08);
            @include border-radius(12px);
            width: 100%;
            @include inline-flex();
            align-items: center;
            justify-content: center;
            height: 38px;
            color: var(--td-white);
            font-size: 14px;
            font-weight: 800;
        }
    }
}

// User dropdown
.user-profile-drop {
    position: relative;
    cursor: pointer;

    .dropdown-menu {
        top: calc(100% + 12px);
        inset-inline-end: 0;
        z-index: 9;
        background: var(--td-white);
        @include border-radius(8px);
        width: 220px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
        transform: translateY(-20px);
        opacity: 0;
        cursor: pointer;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
        position: absolute;
        display: block;
        background-color: #171C35;
        width: 320px;
        border-radius: 16px;
        padding-top: 0 !important;

        @media #{$xxs} {
            width: 300px;
            inset-inline-end: -36px;
        }

        &.show {
            visibility: visible;
            opacity: 1;
            transform: translateY(0px);
        }
    }

    /* Profile Header */
    .profile-header {
        display: flex;
        column-gap: 12px;
        border-bottom: 1px solid hsla(0, 0%, 100%, .1);
        padding: 16px 20px 15px;

        @media #{$xs,$sm} {
            padding: 12px 12px 12px;
        }

        .avatar {
            width: 44px;
            height: 44px;
            background: #7ed957;
            color: var(--td-black);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            border-radius: 50%;
        }

        .user-info {
            .title {
                font-size: 20px;
                font-weight: 700;

                @media #{$xs} {
                    font-size: 18px;
                }
            }

            .uid {
                display: flex;
                align-items: center;
                gap: 5px;
                position: relative;
                margin-top: 5px;
                margin-bottom: 10px;
                font-weight: 500;
                color: #aaa;
                font-size: 12px;

                .uid-text {
                    text-decoration: underline;

                    i {
                        cursor: pointer;
                    }
                }
            }

            .copy-btn {
                cursor: pointer;
                transition: color 0.3s ease-in-out;
                width: 14px;

                &:hover {
                    color: var(--td-primary);
                }
            }

            /* Tooltip */
            .tooltip {
                position: absolute;
                top: -30px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.3s ease-in-out;
            }

            .show-tooltip {
                opacity: 1;
            }

            .unverified {
                background: rgba($warning, $alpha: 0.12);
                color: var(--td-warning);
                border-radius: 6px;
                padding: 6px 10px 6px 10px;
                font-size: 14px;
                font-weight: 500;
            }
        }
    }

    .profie-info-list {
        display: flex;
        flex-direction: column;
        gap: 6px;
        padding: 15px 20px;

        @media #{$xs,$sm} {
            padding: 12px 12px;
        }


        .profie-info-item {
            border-radius: 8px;
            padding: 7px 10px 8px 10px;
            display: flex;
            gap: 8px;
            color: var(--td-white);
            font-size: 14px;
            font-weight: 700;

            .icon {
                display: inline-flex;
                align-items: center;
            }

            &:hover {
                background: rgba(255, 255, 255, 0.06);
            }

            &.profile-log-out {
                background-color: rgba(235, 78, 92, 0.16);
                color: var(--td-danger);

                .icon {
                    span {
                        color: var(--td-danger);
                    }
                }
            }
        }
    }

}

// switcher
.theme-switcher {
    .dark-mode {
        display: none;
    }
}

.dark-theme {
    .light-mode {
        display: none;
    }

    .dark-mode {
        display: block;
    }
}