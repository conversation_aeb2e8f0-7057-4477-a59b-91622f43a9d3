@use '../../utils' as *;

/*----------------------------------------*/
/* Footer primary style
/*----------------------------------------*/
.footer-primary {
  background: #0E1B2C;
  position: relative;
  z-index: 5;

  .footer-bg {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: -1;
  }

  .footer-main {
    padding: 80px 0 50px;
  }
}

// Footer line
.footer-line {
  position: relative;
  height: 10px;
  background-color: transparent;

  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: 0;
    width: 30%;
    height: 10px;
    background: #649448;
    clip-path: polygon(0% 0%, 99.829% 0%, 98.695% 100%, 0% 100%, 0% 0%);
    z-index: 30;
  }

  &::after {
    content: '';
    position: absolute;
    border-top: 4px solid #293F9C;
    width: 100%;
    top: 6px;
    left: 0;
  }

}

// Footer widget grid
.footer-widget-grid {
  @include gridbox();
  grid-template-columns: repeat(5, 1fr);
  gap: 50px 64px;
  justify-content: space-between;

  @media #{$lg} {
    grid-template-columns: auto auto auto 270px;
    justify-content: space-between;
  }

  @media #{$md} {
    grid-template-columns: 1fr 1fr;
  }

  @media #{$xs,$sm} {
    grid-template-columns: 1fr 1fr;
    justify-content: space-between
  }

  @media #{$xxs} {
    grid-template-columns: 1fr;
    justify-content: space-between
  }
}

.footer-wg-head {
  .title {
    color: var(--td-white);
    margin-bottom: 20px;
    font-size: 18px;
  }
}

// Footer links
.footer-links {
  ul {
    li {
      list-style: none;

      &:not(:last-child) {
        margin-bottom: 6px;
      }

      a {
        font-size: 14px;
        font-weight: 700;

        &:hover {
          color: var(--td-primary);
          text-decoration: underline
        }
      }
    }
  }
}

// Footer social
.footer-social {
  @include flexbox();
  gap: 16px;
  flex-direction: column;

  ul {
    @include flexbox();
    gap: 16px;

    li {
      list-style: none;

      a {
        font-size: 16px;
        color: #BBBBBB;
        transition: all 0.3s ease-in-out;
        border-radius: 6px;
        border-style: solid;
        border: 1px solid #bbbbbb;
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;

        &:hover {
          background-color: var(--td-primary);
          border-color: var(--td-primary);
        }
      }
    }
  }
}