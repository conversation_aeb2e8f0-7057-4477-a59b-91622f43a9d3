@use '../../utils' as *;

/*----------------------------------------*/
/*  Why choose styles
/*----------------------------------------*/
.why-choose-shapes {
    .shape-one {
        position: absolute;
        right: 0;
        top: 0;
        z-index: -1;
    }
}

.why-choose-info-column {
    display: flex;
    flex-direction: column;
    gap: 20px;

}

.why-choose-info-card {
    background: #0A1729;
    padding: 35px 35px 100px;
    border-radius: 16px;
    position: relative;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    transition: all 0.5s ease-in-out;

    @media #{$xs,$sm} {
        padding: 20px 20px 60px;
    }

    &:hover {
        transform: scale(1.03);
    }

    .contents {
        .subtitle {
            font-weight: 700;
            color: #A6EF67;
            @media #{$xs} {
                font-size: 14px;
            }
        }

        >p {
            margin-bottom: 8px;
        }

        h1 {
            @media #{$lg} {
                font-size: 32px;
            }

            @media #{$md} {
                font-size: 28px;
            }

            @media #{$sm} {
                font-size: 26px;
            }

            @media #{$xs} {
                font-size: 24px;
            }
        }
        h2 {
            @media #{$lg} {
                font-size: 36px;
            }
        
            @media #{$md} {
                font-size: 30px;
            }
        
            @media #{$sm} {
                font-size: 26px;
            }
        
            @media #{$xs} {
                font-size: 24px;
            }
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-bottom: 15px;
        }

        h4 {
            font-size: 30px;

            @media #{$sm,$md,$lg,$xl} {
                font-size: 24px;
            }

            @media #{$xs} {
                font-size: 20px;
            }
        }
    }

    &.large {
        .contents {
            max-width: 570px;
        }
    }
}