<!doctype html>
<html class="no-js" lang="zxx">

<head>
   <meta charset="utf-8">
   <meta http-equiv="x-ua-compatible" content="ie=edge">
   <title>Sign In || PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange</title>
   <meta name="description" content="">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <!-- Place favicon.ico in the root directory -->
   <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
   <!-- CSS here -->
   <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
   <link rel="stylesheet" href="../assets/css/flag-icon.css">
   <link rel="stylesheet" href="../assets/css/select2.css">
   <link rel="stylesheet" href="../assets/css/styles.css">
   <link rel="stylesheet" href="../assets/css/satoshi.css">
</head>

<body>

   <!--[if lte IE 9]>
   <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
   <![endif]-->

   <!-- auth body-overlay -->
   <div class="auth-overlay-bg"></div>

   <!-- Pre loader start -->
   <div id="td-loadingDiv">
      <div class="td-loading-wrapper">
         <svg width="184" height="30" viewBox="0 0 184 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
               d="M0 8.73976C0 8.21184 0.263844 7.71884 0.703105 7.426L9.38732 1.63653C10.4366 0.936994 11.8421 1.68919 11.8421 2.95029V21.2602C11.8421 21.7882 11.5783 22.2812 11.139 22.574L2.45479 28.3635C1.40549 29.063 0 28.3108 0 27.0497V8.73976Z"
               fill="#3B57E7" />
            <path
               d="M8.68408 8.73976C8.68408 8.21184 8.94793 7.71884 9.38719 7.426L18.0714 1.63653C19.1207 0.936994 20.5262 1.68919 20.5262 2.95029V21.2602C20.5262 21.7882 20.2623 22.2812 19.8231 22.574L11.1389 28.3635C10.0896 29.063 8.68408 28.3108 8.68408 27.0497V8.73976Z"
               fill="#CCFF70" />
            <path
               d="M176.954 22.3422C172.858 22.3422 169.989 19.3611 169.989 15.0984C169.989 10.7799 172.803 7.79883 176.843 7.79883C180.966 7.79883 183.585 10.5571 183.585 14.8476V15.8785L173.221 15.9064C173.471 18.3303 174.753 19.5561 177.01 19.5561C178.876 19.5561 180.102 18.8318 180.492 17.5223H183.641C183.056 20.5313 180.548 22.3422 176.954 22.3422ZM176.87 10.5849C174.864 10.5849 173.639 11.6715 173.304 13.7332H180.214C180.214 11.8387 178.904 10.5849 176.87 10.5849Z"
               fill="#A6EF67" />
            <path
               d="M152.903 14.7915C152.903 10.7795 155.522 7.77051 159.45 7.77051C161.512 7.77051 163.128 8.6342 163.936 10.1108L164.131 8.21628H167.279V21.2831C167.279 25.8801 164.521 28.7498 160.063 28.7498C156.107 28.7498 153.404 26.4931 152.986 22.8154H156.385C156.608 24.5985 157.974 25.6572 160.063 25.6572C162.403 25.6572 163.908 24.1806 163.908 21.896V19.6114C163.044 20.893 161.345 21.701 159.367 21.701C155.466 21.701 152.903 18.7756 152.903 14.7915ZM156.33 14.7079C156.33 17.0203 157.806 18.7477 160.035 18.7477C162.376 18.7477 163.824 17.1039 163.824 14.7079C163.824 12.3676 162.403 10.7516 160.035 10.7516C157.778 10.7516 156.33 12.4511 156.33 14.7079Z"
               fill="#A6EF67" />
            <path
               d="M142.515 22.3422C139.589 22.3422 137.806 20.6427 137.806 18.0517C137.806 15.5163 139.645 13.9282 142.905 13.6775L147.028 13.371V13.0645C147.028 11.1979 145.914 10.4456 144.186 10.4456C142.18 10.4456 141.066 11.2814 141.066 12.7302H138.168C138.168 9.74909 140.62 7.79883 144.353 7.79883C148.059 7.79883 150.344 9.80481 150.344 13.6218V21.98H147.362L147.112 19.9462C146.527 21.3671 144.66 22.3422 142.515 22.3422ZM143.629 19.779C145.719 19.779 147.056 18.5253 147.056 16.4079V15.6835L144.186 15.9064C142.069 16.1014 141.261 16.7979 141.261 17.9123C141.261 19.1661 142.097 19.779 143.629 19.779Z"
               fill="#A6EF67" />
            <path
               d="M119.934 21.9801L115.477 8.2168H119.015L120.826 14.1233C121.132 15.2099 121.411 16.4079 121.662 17.6895C121.912 16.3522 122.163 15.4885 122.609 14.1233L124.504 8.2168H127.958L129.797 14.1233C129.964 14.6805 130.521 16.7422 130.689 17.6617C130.912 16.6308 131.357 14.987 131.608 14.1233L133.447 8.2168H137.041L132.277 21.9801H129.101L127.206 16.0179C126.649 14.2069 126.342 12.8974 126.231 12.2009C126.092 12.8417 125.841 13.8168 125.144 16.0736L123.25 21.9801H119.934Z"
               fill="#A6EF67" />
            <path
               d="M112.11 21.9797H108.711V11.0582H106.064V8.21636H108.711V3.92578H112.11V8.21636H114.785V11.0582H112.11V21.9797Z"
               fill="white" />
            <path
               d="M98.2143 22.3422C94.1187 22.3422 91.249 19.3611 91.249 15.0984C91.249 10.7799 94.063 7.79883 98.1028 7.79883C102.226 7.79883 104.845 10.5571 104.845 14.8476V15.8785L94.4809 15.9064C94.7316 18.3303 96.0132 19.5561 98.27 19.5561C100.137 19.5561 101.363 18.8318 101.753 17.5223H104.901C104.316 20.5313 101.808 22.3422 98.2143 22.3422ZM98.1307 10.5849C96.1247 10.5849 94.8988 11.6715 94.5645 13.7332H101.474C101.474 11.8387 100.165 10.5849 98.1307 10.5849Z"
               fill="white" />
            <path
               d="M81.1182 21.9795H77.7471V1.25098H81.1182V13.9834L86.4675 8.21621H90.7303L85.4924 13.677L90.6188 21.9795H86.7183L83.1242 16.1844L81.1182 18.274V21.9795Z"
               fill="white" />
            <path
               d="M61.5649 15.0705C61.5649 10.8078 64.3789 7.79883 68.4466 7.79883C72.2078 7.79883 74.771 9.8884 75.1332 13.2038H71.7342C71.3441 11.6436 70.2018 10.8357 68.5859 10.8357C66.4127 10.8357 64.964 12.4795 64.964 15.0705C64.964 17.6616 66.3013 19.2775 68.4744 19.2775C70.174 19.2775 71.372 18.4417 71.7342 16.9372H75.1611C74.7432 20.1412 72.0685 22.3422 68.4744 22.3422C64.2953 22.3422 61.5649 19.4447 61.5649 15.0705Z"
               fill="white" />
            <path
               d="M44.9683 15.07C44.9683 10.7794 48.0608 7.82617 52.3235 7.82617C56.5863 7.82617 59.6788 10.7794 59.6788 15.07C59.6788 19.3606 56.5863 22.3139 52.3235 22.3139C48.0608 22.3139 44.9683 19.3606 44.9683 15.07ZM48.3673 15.07C48.3673 17.5775 49.9832 19.277 52.3235 19.277C54.6639 19.277 56.2798 17.5775 56.2798 15.07C56.2798 12.5625 54.6639 10.863 52.3235 10.863C49.9832 10.863 48.3673 12.5625 48.3673 15.07Z"
               fill="white" />
            <path
               d="M28.5264 28.3602V8.21674H31.6747L31.8975 10.3063C32.7334 8.71824 34.5443 7.79883 36.6339 7.79883C40.5066 7.79883 43.0698 10.6128 43.0698 14.9312C43.0698 19.2218 40.7295 22.3422 36.6339 22.3422C34.5722 22.3422 32.7891 21.5343 31.9254 20.1412V28.3602H28.5264ZM31.9533 15.0984C31.9533 17.578 33.4856 19.2775 35.8259 19.2775C38.222 19.2775 39.6429 17.5502 39.6429 15.0984C39.6429 12.6466 38.222 10.8914 35.8259 10.8914C33.4856 10.8914 31.9533 12.6188 31.9533 15.0984Z"
               fill="white" />
         </svg>

         <div class="td-loading">
            <div class="td-loading-overlay"></div>
         </div>
      </div>
   </div>
   <!-- Pre loader start -->

   <!-- Header section start -->
   <header>
      <div class="header-area header-style-one is-auth-header">
         <div class="container">
            <div class="header-right">
               <div class="header-quick-actions d-flex align-items-center justify-content-end">
                  <div class="theme-switcher">
                     <!-- Theme toggle button for switching between light and dark mode -->
                     <button id="theme-toggle" class="action-icon theme-switcher" aria-label="Toggle Theme">
                        <!-- Light mode icon (visible in dark mode) -->
                        <span class="light-mode" aria-hidden="true">
                           <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M16.5 11.8831C15.5149 12.3286 14.4214 12.5766 13.2701 12.5766C8.93647 12.5766 5.42337 9.06352 5.42337 4.7299C5.42337 3.57851 5.67135 2.48505 6.11683 1.5C3.39432 2.73122 1.5 5.47102 1.5 8.65327C1.5 12.9869 5.0131 16.5 9.34672 16.5C12.529 16.5 15.2688 14.6056 16.5 11.8831Z" stroke="#080808" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                           </svg>
                        </span>
                        <!-- Dark mode icon (visible in light mode) -->
                        <span class="dark-mode" aria-hidden="true">
                           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M12 3V4.8M12 19.2V21M4.8 12H3M6.88271 6.88271L5.60991 5.60991M17.1173 6.88271L18.3901 5.60991M6.88271 17.121L5.60991 18.3938M17.1173 17.121L18.3901 18.3938M21 12H19.2M16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12Z" stroke="white" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                           </svg>
                        </span>
                    </button>
                  </div>
                  <div class="language-dropdown">
                     <div class="language-box language-nav">
                        <div class="translate_wrapper">
                           <div class="current_lang">
                              <div class="quick-action-item">
                                 <button type="button" class="action-icon notification-btn">
                                    <i class="solar--global-line-duotone"></i>    
                                 </button>
                              </div>
                           </div>
                           <div class="more_lang">
                              <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English</span></div>
                              <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                              <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                              <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </header>
   <!-- Header section end -->

   <!-- Body main wrapper start -->
   <main>

      <!-- Authentication section start -->
      <section class="td-authentication-section">
         <div class="container">
            <div class="auth-main-box">   
               <div class="auth-too-wrapper text-center">
                  <div class="auth-logo">
                     <a href="../themes/index.html">
                        <img src="../assets/images/logo/logo.svg" alt="logo">
                     </a>
                  </div>
                  <div class="auth-intro-contents">
                     <h3 class="title">Welcome Back!</h3>
                     <p class="description">Your personal data will be used to support your experience.</p>
                  </div>
               </div>
               <div class="auth-from-box">
                  <form id="sign-in-form" novalidate>
                     <div class="row gy-3">
                        <div class="col-xxl-12">
                           <div class="td-form-group">
                              <label class="input-label">Email address <span>*</span></label>
                              <div class="input-field">
                                 <input type="email" class="form-control" required>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                           </div>
                        </div>
                        <div class="col-xxl-12">
                           <div class="td-form-group has-right-icon">
                              <label class="input-label">Password<span>*</span></label>
                              <div class="input-field">
                                 <input type="password" class="form-control password-input" required>
                                 <span class="input-icon eyeicon"><i class="mage--eye-off"></i></span>
                              </div>
                              <p class="feedback-invalid">This field is required</p>
                           </div>
                        </div>
                     </div>
                     <div class="auth-login-option">
                        <div class="animate-custom">
                           <input class="inp-cbx" id="auth_remind" type="checkbox" style="display: none;">
                           <label class="cbx" for="auth_remind">
                              <span>
                                 <svg width="12px" height="9px" viewbox="0 0 12 9">
                                    <polyline points="1 5 4 8 11 1"></polyline>
                                 </svg>
                              </span>
                              <span>Keep me logged in</span>
                           </label>
                        </div>
                        <div class="forget-content">
                           <span><a class="td-underline-btn" href="forgot-password.html">Forget Password</a></span>
                        </div>
                     </div>
                     <div class="auth-from-btn-wrap">
                        <button class="td-btn btn-secondary w-100" type="submit">Login</button>
                     </div>
                  </form>
               </div>          
               <div class="auth-from-bottom-content">
                  <div class="auth-divide">
                     <div class="divider-line"></div>
                     <div class="or">or sign in with</div>
                     <div class="divider-line"></div>
                  </div>
                  <div class="auth-alternative d-flex flex-column gap-15">
                     <a href="#" class="td-btn outline-auth-btn"> <span>
                        <span class="btn-icon">
                           <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8.79642 0.643626C5.90985 1.65782 3.41337 3.84225 2.00909 6.57278C1.541 7.50897 1.15093 8.52316 0.994895 9.61538C0.448788 12.1899 0.838864 14.9984 2.00909 17.3389C2.78925 18.8992 3.88146 20.2254 5.28573 21.3177C6.53398 22.3319 8.01626 23.112 9.65458 23.5021C11.683 24.0482 13.7894 24.0482 15.7398 23.5801C17.5341 23.19 19.2505 22.3319 20.6547 21.0056C22.137 19.6793 23.1512 17.963 23.6973 16.0126C24.3214 13.9842 24.3995 11.7998 24.0094 9.69339C20.2647 9.69339 16.5199 9.69339 12.7752 9.69339V14.3743H19.2505C19.0164 15.8566 18.0802 17.2609 16.832 18.041C16.0518 18.5871 15.1157 18.8992 14.1795 19.0552C13.2433 19.2113 12.2291 19.2113 11.2929 19.0552C10.3567 18.8992 9.42054 18.4311 8.64039 17.885C7.39214 17.0268 6.37795 15.7006 5.90985 14.2183C5.36375 12.736 5.36375 11.0977 5.90985 9.53736C6.29993 8.52316 6.84604 7.50897 7.62619 6.72881C8.56237 5.71462 9.81062 5.01248 11.2149 4.77843C12.3851 4.54439 13.5553 4.54439 14.7256 4.93446C15.6618 5.24652 16.5979 5.79263 17.3001 6.41675C18.0022 5.71462 18.7824 4.93446 19.4845 4.23233C19.8746 3.84225 20.2647 3.45217 20.6547 3.0621C19.5625 2.0479 18.2363 1.18973 16.832 0.643626C14.2575 -0.214542 11.3709 -0.214542 8.79642 0.643626Z" fill="white"/>
                              <path d="M8.79611 0.643713C11.3706 -0.214454 14.2572 -0.214454 16.8317 0.721729C18.236 1.26784 19.5622 2.04799 20.6544 3.1402C20.2644 3.53028 19.8743 3.92035 19.4842 4.31043C18.7821 5.01257 18.0019 5.79272 17.2998 6.49486C16.5976 5.79272 15.6615 5.24661 14.7253 5.01257C13.6331 4.70051 12.3848 4.62249 11.2146 4.85654C9.88833 5.1686 8.64008 5.87073 7.62588 6.80692C6.84573 7.58707 6.22161 8.60127 5.90955 9.61547C4.58329 8.60127 3.33505 7.58707 2.00879 6.57287C3.41306 3.84234 5.90955 1.65791 8.79611 0.643713Z" fill="#EA4335"/>
                              <path d="M0.994895 9.61535C1.22894 8.60115 1.541 7.58695 2.00909 6.57275C3.33535 7.58695 4.5836 8.60115 5.90985 9.61535C5.36375 11.0976 5.36375 12.736 5.90985 14.2963C4.5836 15.3105 3.33535 16.3247 2.00909 17.3389C0.838864 14.9204 0.448788 12.1899 0.994895 9.61535Z" fill="#FBBC05"/>
                              <path d="M12.6982 9.69348H23.9324C24.3225 11.7999 24.2445 13.9843 23.6204 16.0127C23.0743 17.8851 22.0601 19.6014 20.5778 21.0057C19.3295 19.9915 18.0813 19.0553 16.755 18.0411C18.0033 17.1829 18.8614 15.8567 19.1735 14.3744C16.9891 14.3744 14.8047 14.3744 12.6982 14.3744C12.6982 12.8141 12.6982 11.2538 12.6982 9.69348Z" fill="#4285F4"/>
                              <path d="M2.00879 17.3387C3.33505 16.3245 4.58329 15.3103 5.90955 14.2961C6.37764 15.7784 7.39184 17.1047 8.64008 17.9629C9.42024 18.509 10.3564 18.899 11.2926 19.1331C12.2288 19.2891 13.165 19.2891 14.1792 19.1331C15.1153 18.9771 16.0515 18.587 16.8317 18.1189C18.0799 19.1331 19.3282 20.0693 20.6544 21.0835C19.2502 22.3317 17.6118 23.1899 15.7395 23.658C13.7111 24.1261 11.6047 24.1261 9.65428 23.58C8.09398 23.1899 6.61169 22.4097 5.28543 21.3955C3.95917 20.2253 2.78894 18.821 2.00879 17.3387Z" fill="#34A853"/>
                           </svg>
                        </span>                                    
                        </span class="btn-text">Continue with Google</span> 
                     </a>
                     <a href="#" class="td-btn outline-auth-btn">      
                        <span class="btn-icon">
                           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.4046 6.13874C16.6086 6.13874 16.9563 6.17119 17.4477 6.2361C17.9391 6.301 18.4769 6.48645 19.0611 6.79243C19.6545 7.08914 20.1923 7.58521 20.6744 8.28063C20.6466 8.30844 20.5122 8.41044 20.2711 8.58661C20.03 8.75351 19.7611 9.00386 19.4644 9.33766C19.1677 9.66219 18.9081 10.0794 18.6856 10.5894C18.463 11.0901 18.3517 11.6882 18.3517 12.3836C18.3517 13.181 18.4908 13.8579 18.769 14.4142C19.0564 14.9706 19.3856 15.4203 19.7565 15.7633C20.1367 16.0971 20.4705 16.3428 20.7579 16.5005C21.0546 16.6581 21.2122 16.7416 21.2308 16.7508C21.2215 16.7879 21.101 17.1171 20.8692 17.7383C20.6466 18.3596 20.2757 19.0503 19.7565 19.8107C19.3022 20.469 18.8107 21.0763 18.2822 21.6327C17.763 22.189 17.1371 22.4672 16.4046 22.4672C15.9131 22.4672 15.5098 22.3976 15.1945 22.2585C14.8793 22.1102 14.5548 21.9665 14.221 21.8274C13.8872 21.679 13.4375 21.6049 12.8718 21.6049C12.3248 21.6049 11.8658 21.679 11.4949 21.8274C11.1333 21.9757 10.7856 22.1241 10.4518 22.2725C10.1273 22.4208 9.74247 22.495 9.2974 22.495C8.62052 22.495 8.0271 22.2261 7.51713 21.6883C7.00715 21.1505 6.48327 20.5061 5.94548 19.755C5.32424 18.8649 4.79108 17.78 4.34602 16.5005C3.91022 15.2116 3.69232 13.9135 3.69232 12.6061C3.69232 11.206 3.95658 10.0331 4.4851 9.08731C5.01362 8.13227 5.69049 7.41367 6.51572 6.93151C7.35022 6.44008 8.21254 6.19437 9.10268 6.19437C9.57556 6.19437 10.0206 6.27318 10.4379 6.43081C10.8551 6.57917 11.2446 6.73216 11.6062 6.88979C11.9771 7.04742 12.3109 7.12623 12.6076 7.12623C12.895 7.12623 13.2288 7.04278 13.609 6.87588C13.9892 6.70898 14.4157 6.54672 14.8886 6.38909C15.3614 6.22219 15.8668 6.13874 16.4046 6.13874ZM15.6396 4.37237C15.278 4.80817 14.8237 5.17442 14.2766 5.47113C13.7295 5.75857 13.2103 5.90229 12.7189 5.90229C12.6169 5.90229 12.5195 5.89302 12.4268 5.87448C12.4175 5.84666 12.4082 5.79566 12.399 5.72149C12.3897 5.64731 12.3851 5.56849 12.3851 5.48504C12.3851 4.92871 12.5056 4.39092 12.7467 3.87167C12.9878 3.34315 13.2613 2.90736 13.5673 2.56428C13.9567 2.10067 14.4481 1.71587 15.0416 1.40989C15.635 1.1039 16.2006 0.94164 16.7384 0.923096C16.7662 1.04363 16.7801 1.18735 16.7801 1.35426C16.7801 1.91059 16.6735 2.45302 16.4602 2.98154C16.2469 3.50078 15.9734 3.96439 15.6396 4.37237Z" fill="white"/>
                           </svg>                                
                        </span>          
                        </span class="btn-text"> Continue with apple</span>
                     </a>
                  </div>
                  <div class="have-auth-account">
                     <p class="description">Don’t have an account? <a class="td-underline-btn" href="sign-up.html"> Sign up</a></p>
                  </div>
               </div>
            </div>
         </div>
      </section>
      <!-- Authentication section end -->

   </main>
   <!-- Body main wrapper end -->

   <!-- JS here -->
   <script src="../assets/js/jquery-3.7.1.min.js"></script>
   <script src="../assets/js/bootstrap.bundle.min.js"></script>
   <script src="../assets/js/iconify-icon.min.js"></script>
   <script src="../assets/js/select2.js"></script>
   <script src="../assets/js/iconify.min.js"></script>
   <script src="../assets/js/main.js"></script>
   <script>
      document.addEventListener('DOMContentLoaded', function () {
         'use strict';

         // Function to toggle password visibility
         function togglePasswordVisibility(event) {
            const eyeIconSpan = event.currentTarget;
            const passwordInput = eyeIconSpan.previousElementSibling;
            const icon = eyeIconSpan.querySelector('i');

            if (passwordInput.type === "password") {
               passwordInput.type = "text";
               icon.classList.replace('mage--eye-off', 'mage--eye'); // Fixed class replacement
            } else {
               passwordInput.type = "password";
               icon.classList.replace('mage--eye', 'mage--eye-off'); // Fixed class replacement
            }
         }

         // Function to validate form inputs
         function validateForm(event) {
            event.preventDefault();
            const form = event.target;
            let isValid = form.checkValidity();

            // Show feedback and toggle danger class
            form.querySelectorAll('.td-form-group').forEach(group => {
               const input = group.querySelector('input');
               const feedback = group.querySelector('.feedback-invalid');

               if (!input.validity.valid) {
                  feedback.style.display = 'block';
               } else {
                  feedback.style.display = 'none';
               }
            });

            if (!isValid) {
               form.classList.add('was-not-validated');
            } else {
               form.classList.remove('was-not-validated');
               console.log('Form is valid and ready to submit.');
               // form.submit();
            }
         }

         // Attach event listener to eye icon spans
         document.querySelectorAll('.eyeicon').forEach(eyeIconSpan => {
            eyeIconSpan.addEventListener('click', togglePasswordVisibility);
         });

         // Attach event listener to form submission
         document.querySelector('#sign-in-form').addEventListener('submit', validateForm);
      });
   </script>
</body>

</html>