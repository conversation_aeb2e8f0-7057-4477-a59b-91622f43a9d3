@use '../utils' as *;

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder {
	.mfp-content {
		line-height: 0;
		width: 100%;
		max-width: 1280px;

		@media #{$xxl,$x3l} {
			max-width: 1000px;
		}

		@media #{$xl} {
			max-width: 850px;
		}

		@media #{$lg} {
			max-width: 820px;
		}

		@media #{$md} {
			max-width: 750px;
		}
	}
}

.mfp-close {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);

	&:hover {
		color: var(--td-white);
		;
	}

	&::after {
		position: absolute;
		content: "\f00d";
		height: 100%;
		width: 100%;
		font-family: var(--td-ff-fontawesome);
		font-size: 31px;
		font-weight: 200;
		inset-inline-end: -20px;
		margin-top: -25px;

		@media #{$xs,$sm,$md} {
			inset-inline-end: 15px;
			margin-top: -30px;
		}
	}
}