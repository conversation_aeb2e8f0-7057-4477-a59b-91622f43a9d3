@use '../../utils' as *;

/*----------------------------------------*/
/*  Counter styles
/*----------------------------------------*/
.counter-grid {
    display: grid;
    grid-template-columns: auto auto auto auto;
    justify-content: space-between;
    gap: 30px 50px;

    @media #{$xs,$sm,$md} {
        grid-template-columns: repeat(2, 1fr);
    }

    @media #{$xs} {
        grid-template-columns: 1fr;
    }
}

.single-counter-item {
    text-align: center;
    position: relative;

    &:not(:last-child) {
        &::before {
            position: absolute;
            content: "";
            height: 40px;
            width: 1px;
            background-color: rgba($white, $alpha: 0.2);
            top: 50%;
            transform: translateY(-50%);
            right: -120px;

            @media #{$xl} {
                right: -68px;
            }

            @media #{$lg} {
                right: -60px;
            }

            @media #{$md,$sm} {
                right: -22px;
            }

            @media #{$xs} {
                display: none;
            }
        }
    }

    &:nth-child(2) {
        &::before {
            @media #{$xs,$sm,$md} {
                display: none;
            }
        }
    }

    .icon {
        margin-bottom: 20px;

        img {
            width: 50px;
        }
    }

    .content {
        .description {
            font-size: 18px;
            font-weight: 700;
            color: var(--td-white);
            margin-bottom: 10px;
        }

        .title,
        span {
            font-family: var(--td-ff-body);
            font-size: 30px;
            color: var(--td-primary);
            font-weight: 700;
            line-height: 1;

            @media #{$xs,$sm} {
                font-size: 26px;
            }
        }
    }

    &.style-two {
        .content {

            .title,
            span {
                font-size: 20px;
            }

            .description {
                font-size: 16px;
                font-weight: 500;
                color: #999;
                margin-bottom: 0;
                margin-top: 5px;
            }
        }
    }
}