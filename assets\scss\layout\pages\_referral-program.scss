@use '../../utils' as *;

/*----------------------------------------*/
/*  Referral program styles
/*----------------------------------------*/
.referral-card {
    padding: 30px 30px;
    border-radius: 12px;
    justify-content: space-between;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: grid;
    align-items: end;
    grid-template-columns: 1fr 1px 490px;
    gap: 20px 60px;
    position: relative;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 2;

    @media #{$xxl,$xl,$lg,$md} {
        grid-template-columns: 1fr 1px 290px;
        gap: 20px 30px;
    }

    @media #{$xs,$sm,$md} {
        grid-template-columns: 1fr;
        padding: 20px 20px;
    }

    .left-contents {
        .title {
            font-size: 30px;
            font-weight: 700;
            margin-bottom: rem(60);

            @media #{$xs,$sm} {
                margin-bottom: rem(30);
                font-size: 24px;
            }

            span {
                color: var(--td-primary);
            }
        }

        .ref-link-contents {
            .ref-link {
                margin-bottom: 25px;
                font-size: 14px;
                font-weight: 700;
            }

            .link-box {
                display: flex;
                align-items: center;
                gap: rem(12);
                flex-wrap: wrap;
                justify-content: space-between;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 0px 0px 16px 0px;

                .lint-text {
                    margin-bottom: 0;
                    font-size: 16px;
                    font-weight: 700;
                    color: var(--td-white);
                }

                .copy-btn {
                    cursor: pointer;
                }
            }
        }
    }

    .separator {
        height: 100%;
        width: 1px;
        background: linear-gradient(37deg, rgba(9, 22, 40, 0.1) 0%, rgba(166, 239, 103, 1) 50%, rgba(9, 22, 40, 0.1) 100%);
        border-image-slice: 1;
        opacity: 0.5;
    }

    .right-contents {
        .share-text {
            font-size: 14px;
            font-weight: 700;
        }

        .social-icons {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: rem(8) rem(8);

            a {
                width: 32px;
                height: 32px;
                text-align: center;
                font-size: 14px;
                border-radius: 6px;
                border-style: solid;
                border: 1px solid #bbbbbb;
                display: inline-flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background-color: var(--td-primary);
                    color: var(--td-heading);
                    border-color: var(--td-primary);

                    svg * {
                        fill: #1D1D1D;
                    }
                }
            }
        }
    }

    .illustration {
        display: inline-block;
        width: 203px;
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: -1;

        @media #{$xs,$sm,$md} {
            width: 100px;
        }
    }
}

// referral info box
.referral-info-box {
    background: #091628;
    border-radius: 24px;
    padding: 30px 30px;
    @media #{$xs,$sm} {
        padding: 20px 20px;
    }

    .heading {
        .title {
            font-size: rem(24);
            margin-bottom: rem(4);
        }
    }

    ul {
        margin-top: 16px;

        li {
            list-style: none;

            &:not(:last-child) {
                margin-bottom: 8px;
            }

            .list {
                display: flex;
                gap: rem(8);

                .list-text {
                    font-size: 16px;
                    font-weight: 700;
                }

                .list-icon {
                    &.success {
                        color: var(--td-green);
                    }

                    &.danger {
                        color: var(--td-danger);
                    }
                }
            }
        }
    }
}