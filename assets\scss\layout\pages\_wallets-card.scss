@use '../../utils' as *;
/*----------------------------------------*/
/*  Wallets styles
/*----------------------------------------*/

.wallet-card-grid {
    display: grid;
    gap: 18px;
    align-items: center;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    @media #{$sm} {
        grid-template-columns: repeat(auto-fit, minmax(256px, 1fr));
    }
    @media #{$xs} {
        grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
    }
}

.wallet-card {
    padding: 24px 24px 24px;
    border-radius: 15px;
    background: linear-gradient(98.99deg, rgba(59, 87, 231, 0.1) 0%, rgba(112, 135, 255, 0.1) 100%);
    position: relative;
    overflow: hidden;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1;

    &.active {
        // background: linear-gradient(135deg, #4A00E0, #E040FB);
        background: linear-gradient(98.99deg, rgb(52 97 111 / 98%) 0%, rgb(166 239 103) 100%);

        .wave {
            opacity: 80%;
        }
    }

    .card-info {
        margin-bottom: 12px;

        .currency-name {
            font-size: 20px;
            font-weight: bold;
        }

    }

    p {
        font-size: 14px;
        opacity: 0.8;
        margin-top: 2px;
        margin-bottom: 0;
        color: var(--td-white);
    }

    .balance {
        font-size: 20px;
        font-weight: 700;
        margin-top: 2px;
        color: var(--td-white);
    }

    .usd {
        font-size: 16px;
        font-weight: 700;
        color: var(--td-primary);
        margin-top: 8px;
    }

    .icon {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px;
        border-radius: 10px;
        width: 46px;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.04);

        img {
            width: 24px;
            height: 24px;
        }
    }

    .wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 70px;
        background: url(../images/bg/wallet-wave-bg.png);
        background-repeat: no-repeat;
        background-size: cover;
        z-index: -1;
        opacity: 15%;
    }
}