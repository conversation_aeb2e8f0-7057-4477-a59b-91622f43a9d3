@use '../../utils' as *;

/*----------------------------------------*/
/*  Dashboard styles 
/*----------------------------------------*/

// Dashboard header styles
.app-page-header {
    max-width: 100vw;
    position: fixed;
    top: 0;
    z-index: 5;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    margin-inline-start: 290px;
    width: calc(100% - 290px);
    background-image: #010C1A;
    @media #{$xl,$xxl} {
        margin-inline-start: 260px;
        width: calc(100% - 260px);
    }

    @media #{$xs} {
        margin-inline-start: 0;
        width: 100%;
    }

    &.close_icon {
        margin-inline-start: 80px;
        width: calc(100% - 80px);
    }

    &.dashboard-sticky {
        position: fixed;
        animation: sticky 0.3s;
        -webkit-animation: sticky 0.3s;
        top: 0;
        width: -webkit-fill-available;
        background: #131314;
    }
}

.app-dashboard-header {
    @include flexbox();
    justify-content: space-between;
    align-items: center;
    gap: 16px 30px;
    padding: 6px 20px;
    height: 70px;
    border-bottom: 1px solid rgba($white, $alpha: 0.1);
    background-color: #010C1A;

    @media #{$xs} {
        padding: 15px 15px;
        gap: 16px 16px;
    }

    .left-content {
        .td-main-menu nav>ul>li>a {
            padding: 24px 5px;
        }
    }

    .right-content {
        .header-quick-actions {
            gap: 16px;
        }
        .header-btns-wrap {
            position: relative;
            padding-right: 16px;
            margin-right: 16px;

            &::before {
                position: absolute;
                content: "";
                right: 0;
                top: -14px;
                height: 70px;
                width: 1px;
                background-color: rgba($white, $alpha: 0.1);
            }
        }

        .others-actions {
            display: flex;
            column-gap: 10px;

            @media #{$xs,$sm,$md,$lg} {
                column-gap: 8px;
            }

            @media #{$xxs} {
                column-gap: 6px;
            }
        }
    }
}

// Dashboard calculation styles
.page-wrapper {
    &.compact-wrapper {
        .app-page-header {
            @media #{$xs,$sm,$md,$lg} {
                margin-inline-start: 0;
                width: 100%;
            }
        }

        .app-page-body-wrapper {
            div {
                &.app-sidebar-wrapper {
                    &.close_icon {
                        &~.app-page-body {
                            margin-inline-start: 80px;
                            -webkit-transition: 0.3s;
                            transition: 0.3s;
                        }
                    }
                }
            }
        }
    }
}

.app-page-body {
    min-height: calc(100vh - 70px);
    margin-top: 70px;
    margin-inline-start: 290px;
    padding: 20px 20px 20px;
    position: relative;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    @media #{$xl,$xxl} {
        margin-inline-start: 260px;
    }

    @media #{$xs} {
        min-height: calc(100vh - 70px);
        margin-top: 70px;
    }
}

.app-page-body-wrapper {
    .app-page-body {
        @media #{$xs,$sm,$md,$lg} {
            margin-inline-start: 0 !important;
        }
    }
}

.bg-overlay {
    &.active {
        height: 100vh;
        width: 100vw;
        background-color: rgba(0, 0, 0, .2);
        position: fixed;
        z-index: 8;
        top: 0;
    }
}