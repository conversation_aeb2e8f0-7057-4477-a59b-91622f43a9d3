@use "../utils" as *;

/*----------------------------------------*/
/*  Modal styles
/*----------------------------------------*/
// default model
.default-model {
	.modal-dialog {
		max-width: 678px;

		@media #{$lg} {
			max-width: 690px;
		}

		@media #{$md} {
			max-width: 590px;
		}

		@media #{$sm} {
			max-width: 540px;
		}

		@media #{$xs} {
			max-width: inherit;
		}
	}

	&.modal {
		background: rgba($black, $alpha: 0.3);
	}

	.modal-content {
		background: #0d1c35;
		@include border-radius(16px);
		padding: 30px 30px;

		@media #{$md,$lg} {
			padding: 36px 38px;
		}

		@media #{$sm} {
			padding: 30px 30px;
		}

		@media #{$xs} {
			padding: 24px 20px;
		}

		.modal-header {
			border-bottom: 1px solid rgba($white, $alpha: 0.1);
			padding: 0;
			padding-bottom: 12px;
		}

		.modal-body {
			padding: 0;
		}
	}
}

.modal-btn-close {
	width: 50px;
	height: 50px;
	@include inline-flex();
	align-items: start;
	justify-content: end;
	font-size: 20px;
	color: var(--td-danger);
	position: absolute;
	right: -10px;
	top: -10px;

	i {
		width: 30px;
		height: 30px;
	}
}

// cryptocurrency modal
.cryptocurrency-purchase-modal {
	display: grid;
	grid-template-columns: 1fr 450px;
	column-gap: 40px;

	@media #{$xl,$lg} {
		grid-template-columns: 1fr 420px;
	}

	@media #{$xs,$sm,$md} {
		grid-template-columns: 1fr;
	}
}

.cryptocurrency-modal {
	.modal-dialog {
		max-width: 1085px;

		@media #{$lg} {
			max-width: 960px;
		}

		@media #{$md} {
			max-width: 690px;
		}

		@media #{$sm} {
			max-width: 490px;
		}

		@media #{$xs} {
			max-width: inherit;
		}
	}

	.modal-content {
		background-color: #0E1B2C;
		border-radius: 24px;
	}

	.currency-contents {
		padding: 40px 40px;

		@media #{$sm} {
			padding: 25px 25px;
		}

		@media #{$xs} {
			padding: 20px 20px;
		}

		.seller-info-wrapper {
			display: flex;
			align-items: center;
			row-gap: 20px;
			margin-top: 20px;
			flex-wrap: wrap;

			.seller-info {
				&:not(:last-child) {
					position: relative;
					padding-right: 40px;
					margin-right: 40px;

					@media #{$xl,$lg,$sm,$xs} {
						padding-right: 25px;
						margin-right: 25px;
					}

					@media #{$xxs} {
						padding-right: 15px;
						margin-right: 15px;
					}
				}

				&::before {
					position: absolute;
					content: "";
					height: 26px;
					width: 1px;
					background-color: rgba($white, $alpha: 0.1);
					top: 50%;
					transform: translateY(-50%);
					right: 0;
				}

				.status {
					font-size: 13px;
					font-weight: 500;
					display: block;
					margin-bottom: rem(4);
				}

				.status-title {
					font-size: 14px;
					font-weight: 700;
					color: var(--td-white);
				}
			}
		}

		.dvertisers-terms-contents {
			margin-top: 45px;

			.heading {
				border-bottom: 1px solid rgba($white, $alpha: 0.1);
				padding-bottom: 10px;
				margin-bottom: 18px;

				h5 {
					font-size: 16px;
				}
			}

			.info {
				ul {
					list-style-type: disc;
					padding-left: 16px;

					li {
						color: rgba($white, $alpha: 0.7);
						font-size: rem(14);
						font-weight: 500;

						&:not(:last-child) {
							margin-bottom: 16px;
						}
					}
				}
			}
		}
	}

	.currency-forms {
		border-left: 1px solid rgba($white, $alpha: 0.1);
		padding: 40px 40px;

		@media #{$xs,$sm,$md} {
			border-left: 0;
			border-top: 1px solid rgba($white, $alpha: 0.1);
		}

		@media #{$sm} {
			padding: 25px 25px;
		}

		@media #{$xs} {
			padding: 20px 20px;
		}

		.forms-grid {
			display: grid;
			gap: 18px;

			.price {
				background: rgba(255, 255, 255, 0.04);
				border-radius: 8px;
				padding: 5px 16px 5px 16px;
				display: inline-flex;
				gap: 10px;
				align-items: center;
				justify-content: center;
				position: relative;
				font-size: 16px;
				letter-spacing: 0.03em;
				font-weight: 700;
				color: var(--td-green);
				width: max-content;

				span {
					color: var(--td-white);
				}
			}

			.input-box {
				border-radius: 16px;
				border-style: solid;
				border-color: rgba(255, 255, 255, 0.1);
				border-width: 1px;
				padding: 12px 14px;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.contents {
					display: flex;
					flex-direction: column;
					gap: 8px;

					span {
						font-size: 20px;
						letter-spacing: 0.03em;
						font-weight: 700;
						color: var(--td-white);
					}

					.balance {
						font-size: 14px;
						font-weight: 500;
					}
				}

				.icon {
					border-left: 1px solid rgba($white, $alpha: 0.1);
					padding-left: 10px;

					img {
						width: 18px;
					}
				}
			}

			.payment-method {
				.td-form-group {
					.input-field {
						.select2-container--default .select2-selection {
							border: 1px solid rgba(255, 255, 255, 0.1);

						}
					}
				}

			}

			.processing-fee {
				color: #999999;
				text-align: center;
				font-size: 14px;
				letter-spacing: 0.03em;
				font-weight: 500;
			}
		}

		.buttons {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 16px;
			margin-top: rem(25);

			.payment-btn {
				font-size: 14px;
				line-height: 18px;
				font-weight: 700;
				padding: 0px 24px;
				height: 40px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px;

				&.buy {
					background-color: var(--td-green);
					color: var(--td-white);
				}

				&.sell {
					background-color: #EB4E5C;
					color: var(--td-white);
				}

				&.cancel {
					background-color: rgba($white, $alpha: 0.1);
					border: 1px solid rgba($white, $alpha: 0.16);
					color: var(--td-white);
				}
			}
		}
	}

	&.is-sell {
		.currency-forms {
			.forms-grid {
				.price {
					color: var(--td-danger);
				}
			}
		}
	}
}