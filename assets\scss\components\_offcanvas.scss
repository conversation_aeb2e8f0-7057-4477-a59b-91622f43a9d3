@use '../utils' as *;

/*----------------------------------------*/
/*  Offcanvas styles
/*----------------------------------------*/

.offcanvas-area {
    background: #171C35 none repeat scroll 0 0;
    position: fixed;
    right: 0;
    top: 0;
    width: 360px;
    height: 100%;
    -webkit-transform: translateX(calc(100% + 80px));
    -moz-transform: translateX(calc(100% + 80px));
    -ms-transform: translateX(calc(100% + 80px));
    -o-transform: translateX(calc(100% + 80px));
    transform: translateX(calc(100% + 80px));
    -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
    -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
    transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
    z-index: 999;
    overflow-y: scroll;
    overscroll-behavior-y: contain;
    scrollbar-width: none;
    @media #{$xxs} {
        width: 300px;
    }

    ::-webkit-scrollbar {
        display: none;
    }

    &.info-open {
        opacity: 1;
        transform: translateX(0);
    }
}

.offcanvas-logo {
    a {
        img {
            height: 26px;
        }
    }
}

.offcanvas-content {
    padding-bottom: 45px;
}

.offcanva-wrapper {
    position: relative;
    height: 100%;
    padding: 28px 28px;
}

.offcanvas-top {
    margin-bottom: 25px;
}

.offcanvas-title {
    color: var(--td-white);
    font-size: 20px;
    margin-bottom: 20px;

    @media #{$xxs} {
        font-size: 20px;
    }
}

.offcanvas-overlay {
    position: fixed;
    height: 100%;
    width: 100%;
    background: $black;
    z-index: 900;
    top: 0;
    opacity: 0;
    visibility: hidden;
    inset-inline-end: 0;
    transition: .3s;

    &.overlay-open {
        opacity: 0.6;
        visibility: visible;
    }
}

.sidebar-toggle {
    cursor: pointer;
}

.offcanvas-contact-icon {
    margin-inline-end: 15px;
}

.offcanvas-btn {
    @include flexbox();
    gap: 15px;
}

// Close icon 
.offcanvas-close-icon {
    @include inline-flex();
    align-items: center;
    justify-content: center;
    font-size: 16px;
    height: 40px;
    width: 40px;
    line-height: 40px;
    border: 2px solid rgba(255, 250, 250, 0.1);
    background-color: var(--td-primary);
    border-radius: 50%;

    svg * {
        color: var(--td-heading);
    }

    &:hover {
        background-color: var(--td-primary);
        color: var(--td-white);
        border-color: transparent;
    }
}