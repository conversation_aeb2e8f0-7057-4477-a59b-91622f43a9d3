@use '../../utils' as *;

/*----------------------------------------*/
/*  Ticker styles
/*----------------------------------------*/
.ticker-area {
    width: 100%;
    overflow: hidden;
    border-bottom: 1px solid hsla(0, 0%, 100%, .1);
    padding: 8px 0;
    white-space: nowrap;
    position: relative;
}

.ticker-wrap {
    display: flex;
    width: 100%;
    white-space: nowrap;
    position: relative;
}

.ticker-content {
    display: flex;
    animation: ticker-scroll 60s linear infinite;
}

.ticker-content {
    span {
        font-size: 12px;
        color: #cfd3dc;
        font-weight: 500;

        span {
            position: relative;
            padding-right: 10px;
            margin-right: 10px;

            &::before {
                position: absolute;
                content: "";
                height: 10px;
                width: 1px;
                background-color: rgba($white, $alpha: 0.3);
                right: 0;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }

    .green {
        color: #03A66D;
    }

    .red {
        color: var(--td-danger);
    }

}

@keyframes ticker-scroll {
    from {
        transform: translateX(0%);
    }

    to {
        transform: translateX(-50%);
    }
}