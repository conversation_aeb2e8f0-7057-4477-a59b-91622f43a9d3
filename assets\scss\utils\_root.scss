@use '../utils/variable' as *;

:root {
  // font family declaration
  --td-ff-body: 'Satoshi-Variable';
  --td-ff-fontawesome: "Font Awesome 6 Pro";

  // color declaration

  // Common Color
  --td-white: hsl(0, 0%, 100%);
  --td-always-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);

  // Body Color
  --td-body: #010C1A;

  // Heading Color
  --td-heading: #080808;

  // Theme Color
  --td-primary: #a6ef67;
  --td-deep-black: #1A1D1F;

  // Text Color
  --td-text-primary: #999999;

  // Border Color
  --td-border-primary: #eaeaea;

  // Others Color
  --td-yellow: #f2c94c;
  --td-warning: #FFA336;
  --td-success: #{$success};
  --td-danger: #eb4e5c;
  --td-danger-alt: #eb4e5c;
  --td-green: #03A66D;

  --td-eerie-black: #091628;
  --td-dark-gunmetal: #142032;
  --td-kuwanomi-purple: #0B1623;
  --td-chaos-black: #0F0F0F;
  --td-liberty-blue: #0A1729;
  

  //font weight declaration
  --td-fw-normal: normal;
  --td-fw-thin: 100;
  --td-fw-elight: 200;
  --td-fw-light: 300;
  --td-fw-regular: 400;
  --td-fw-medium: 500;
  --td-fw-sbold: 600;
  --td-fw-bold: 700;
  --td-fw-ebold: 800;
  --td-fw-black: 900;

  // font size declaration
  --td-fs-body: 16px;
  --td-fs-p: 16px;
  --td-fs-h1: 52px;
  --td-fs-h2: 42px;
  --td-fs-h3: 32px;
  --td-fs-h4: 24px;
  --td-fs-h5: 20px;
  --td-fs-h6: 16px;
  --td-body-color: #ffffff;
}