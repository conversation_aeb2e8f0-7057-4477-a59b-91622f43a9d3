<!doctype html>
<html class="no-js" lang="zxx">

<head>
   <meta charset="utf-8">
   <meta http-equiv="x-ua-compatible" content="ie=edge">
   <title>Markets Overview || PocketWage - Secure & Smart Trading Platform Crypto & Forex Exchange</title>
   <meta name="description" content="">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <!-- Place favicon.ico in the root directory -->
   <link rel="shortcut icon" type="image/x-icon" href="../assets/images/favicon.svg">
   <!-- CSS here -->
   <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
   <link rel="stylesheet" href="../assets/css/fontawesome-pro.css">
   <link rel="stylesheet" href="../assets/css/swiper.min.css">
   <link rel="stylesheet" href="../assets/css/flag-icon.css">
   <link rel="stylesheet" href="../assets/css/select2.css">
   <link rel="stylesheet" href="../assets/css/odometer-default.min.css">
   <link rel="stylesheet" href="../assets/css/styles.css">
   <link rel="stylesheet" href="../assets/css/satoshi.css">
</head>

<body>

   <!--[if lte IE 9]>
   <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
   <![endif]-->

   <!-- Pre loader start -->
   <div id="td-loadingDiv">
      <div class="td-loading-wrapper">
         <svg width="184" height="30" viewBox="0 0 184 30" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
               d="M0 8.73976C0 8.21184 0.263844 7.71884 0.703105 7.426L9.38732 1.63653C10.4366 0.936994 11.8421 1.68919 11.8421 2.95029V21.2602C11.8421 21.7882 11.5783 22.2812 11.139 22.574L2.45479 28.3635C1.40549 29.063 0 28.3108 0 27.0497V8.73976Z"
               fill="#3B57E7" />
            <path
               d="M8.68408 8.73976C8.68408 8.21184 8.94793 7.71884 9.38719 7.426L18.0714 1.63653C19.1207 0.936994 20.5262 1.68919 20.5262 2.95029V21.2602C20.5262 21.7882 20.2623 22.2812 19.8231 22.574L11.1389 28.3635C10.0896 29.063 8.68408 28.3108 8.68408 27.0497V8.73976Z"
               fill="#CCFF70" />
            <path
               d="M176.954 22.3422C172.858 22.3422 169.989 19.3611 169.989 15.0984C169.989 10.7799 172.803 7.79883 176.843 7.79883C180.966 7.79883 183.585 10.5571 183.585 14.8476V15.8785L173.221 15.9064C173.471 18.3303 174.753 19.5561 177.01 19.5561C178.876 19.5561 180.102 18.8318 180.492 17.5223H183.641C183.056 20.5313 180.548 22.3422 176.954 22.3422ZM176.87 10.5849C174.864 10.5849 173.639 11.6715 173.304 13.7332H180.214C180.214 11.8387 178.904 10.5849 176.87 10.5849Z"
               fill="#A6EF67" />
            <path
               d="M152.903 14.7915C152.903 10.7795 155.522 7.77051 159.45 7.77051C161.512 7.77051 163.128 8.6342 163.936 10.1108L164.131 8.21628H167.279V21.2831C167.279 25.8801 164.521 28.7498 160.063 28.7498C156.107 28.7498 153.404 26.4931 152.986 22.8154H156.385C156.608 24.5985 157.974 25.6572 160.063 25.6572C162.403 25.6572 163.908 24.1806 163.908 21.896V19.6114C163.044 20.893 161.345 21.701 159.367 21.701C155.466 21.701 152.903 18.7756 152.903 14.7915ZM156.33 14.7079C156.33 17.0203 157.806 18.7477 160.035 18.7477C162.376 18.7477 163.824 17.1039 163.824 14.7079C163.824 12.3676 162.403 10.7516 160.035 10.7516C157.778 10.7516 156.33 12.4511 156.33 14.7079Z"
               fill="#A6EF67" />
            <path
               d="M142.515 22.3422C139.589 22.3422 137.806 20.6427 137.806 18.0517C137.806 15.5163 139.645 13.9282 142.905 13.6775L147.028 13.371V13.0645C147.028 11.1979 145.914 10.4456 144.186 10.4456C142.18 10.4456 141.066 11.2814 141.066 12.7302H138.168C138.168 9.74909 140.62 7.79883 144.353 7.79883C148.059 7.79883 150.344 9.80481 150.344 13.6218V21.98H147.362L147.112 19.9462C146.527 21.3671 144.66 22.3422 142.515 22.3422ZM143.629 19.779C145.719 19.779 147.056 18.5253 147.056 16.4079V15.6835L144.186 15.9064C142.069 16.1014 141.261 16.7979 141.261 17.9123C141.261 19.1661 142.097 19.779 143.629 19.779Z"
               fill="#A6EF67" />
            <path
               d="M119.934 21.9801L115.477 8.2168H119.015L120.826 14.1233C121.132 15.2099 121.411 16.4079 121.662 17.6895C121.912 16.3522 122.163 15.4885 122.609 14.1233L124.504 8.2168H127.958L129.797 14.1233C129.964 14.6805 130.521 16.7422 130.689 17.6617C130.912 16.6308 131.357 14.987 131.608 14.1233L133.447 8.2168H137.041L132.277 21.9801H129.101L127.206 16.0179C126.649 14.2069 126.342 12.8974 126.231 12.2009C126.092 12.8417 125.841 13.8168 125.144 16.0736L123.25 21.9801H119.934Z"
               fill="#A6EF67" />
            <path
               d="M112.11 21.9797H108.711V11.0582H106.064V8.21636H108.711V3.92578H112.11V8.21636H114.785V11.0582H112.11V21.9797Z"
               fill="white" />
            <path
               d="M98.2143 22.3422C94.1187 22.3422 91.249 19.3611 91.249 15.0984C91.249 10.7799 94.063 7.79883 98.1028 7.79883C102.226 7.79883 104.845 10.5571 104.845 14.8476V15.8785L94.4809 15.9064C94.7316 18.3303 96.0132 19.5561 98.27 19.5561C100.137 19.5561 101.363 18.8318 101.753 17.5223H104.901C104.316 20.5313 101.808 22.3422 98.2143 22.3422ZM98.1307 10.5849C96.1247 10.5849 94.8988 11.6715 94.5645 13.7332H101.474C101.474 11.8387 100.165 10.5849 98.1307 10.5849Z"
               fill="white" />
            <path
               d="M81.1182 21.9795H77.7471V1.25098H81.1182V13.9834L86.4675 8.21621H90.7303L85.4924 13.677L90.6188 21.9795H86.7183L83.1242 16.1844L81.1182 18.274V21.9795Z"
               fill="white" />
            <path
               d="M61.5649 15.0705C61.5649 10.8078 64.3789 7.79883 68.4466 7.79883C72.2078 7.79883 74.771 9.8884 75.1332 13.2038H71.7342C71.3441 11.6436 70.2018 10.8357 68.5859 10.8357C66.4127 10.8357 64.964 12.4795 64.964 15.0705C64.964 17.6616 66.3013 19.2775 68.4744 19.2775C70.174 19.2775 71.372 18.4417 71.7342 16.9372H75.1611C74.7432 20.1412 72.0685 22.3422 68.4744 22.3422C64.2953 22.3422 61.5649 19.4447 61.5649 15.0705Z"
               fill="white" />
            <path
               d="M44.9683 15.07C44.9683 10.7794 48.0608 7.82617 52.3235 7.82617C56.5863 7.82617 59.6788 10.7794 59.6788 15.07C59.6788 19.3606 56.5863 22.3139 52.3235 22.3139C48.0608 22.3139 44.9683 19.3606 44.9683 15.07ZM48.3673 15.07C48.3673 17.5775 49.9832 19.277 52.3235 19.277C54.6639 19.277 56.2798 17.5775 56.2798 15.07C56.2798 12.5625 54.6639 10.863 52.3235 10.863C49.9832 10.863 48.3673 12.5625 48.3673 15.07Z"
               fill="white" />
            <path
               d="M28.5264 28.3602V8.21674H31.6747L31.8975 10.3063C32.7334 8.71824 34.5443 7.79883 36.6339 7.79883C40.5066 7.79883 43.0698 10.6128 43.0698 14.9312C43.0698 19.2218 40.7295 22.3422 36.6339 22.3422C34.5722 22.3422 32.7891 21.5343 31.9254 20.1412V28.3602H28.5264ZM31.9533 15.0984C31.9533 17.578 33.4856 19.2775 35.8259 19.2775C38.222 19.2775 39.6429 17.5502 39.6429 15.0984C39.6429 12.6466 38.222 10.8914 35.8259 10.8914C33.4856 10.8914 31.9533 12.6188 31.9533 15.0984Z"
               fill="white" />
         </svg>

         <div class="td-loading">
            <div class="td-loading-overlay"></div>
         </div>
      </div>
   </div>
   <!-- Pre loader start -->

   <!-- Back to top start -->
   <div class="back-to-top-wrap">
      <svg class="backtotop-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
         <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
      </svg>
   </div>
   <!-- Back to top end -->

   <!-- Cookie notes start -->
   <div class="caches-privacy cookiealert d-none">
      <div class="caches-contents">
         <p>We care about your data, and we will only use cookies to improve your experience. By using this website, you accept our <a target="_blank" class="td-underline-btn" href="#">Learn more</a></p>
      </div>
      <div class="caches-btns">
         <button type="button" class="td-btn outline-black-fill-btn btn-h-36 radius-8">
            <span class="btn-text">Decline</span>
         </button>
         <button type="button" class="td-btn gradient-btn acceptcookies btn-h-36 radius-8">
            <span class="btn-text">Accept</span>
         </button>
      </div>
   </div>
   <!-- Cookie notes end -->

   <!-- Welcome promo gift start -->
   <div id="promoPopup" class="welcome-promo-gift-popup show d-none">
      <div class="welcome-promo-gift-box" data-background="../assets/images/promo-popup/promo-gift-bg.png">
         <button class="promo-close" id="closePopupBtn">
            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
               <path d="M24.9994 45.8333C36.5056 45.8333 45.8327 36.5062 45.8327 25C45.8327 13.4937 36.5056 4.16663 24.9994 4.16663C13.4931 4.16663 4.16602 13.4937 4.16602 25C4.16602 36.5062 13.4931 45.8333 24.9994 45.8333Z" stroke="#EB4E5C" stroke-width="2" stroke-linejoin="round"/>
               <path d="M30.8919 19.1073L19.1064 30.8927M19.1064 19.1073L30.8919 30.8927" stroke="#EB4E5C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>               
         </button>
         <div class="promo-gift-icon">
            <img src="../assets/images/promo-popup/promo-gift-icon.png" alt="Gift Box">
         </div>
         <div class="promo-gift-contents">
            <h3 class="promo-amount">$30</h3>
            <p class="promo-subtitle">Welcome Gifts</p>
            <p class="promo-text">New user exclusive: Unlock up to 3,030 USDT in welcome rewards!</p>
            <button class="td-btn btn-outline-primary w-100">Sign Up Now</button>
         </div>
      </div>
   </div>
   <!-- Welcome promo gift end -->

   <!-- Offcanvas area start -->
   <div class="fix">
      <div class="offcanvas-area">
         <div class="offcanva-wrapper">
            <div class="offcanvas-content">
               <div class="offcanvas-top d-flex justify-content-between align-items-center">
                  <div class="offcanvas-logo">
                     <a href="index.html">
                     <img src="../assets/images/logo/logo.svg" alt="logo not found">
                     </a>
                  </div>
                  <div class="offcanvas-close">
                     <button class="offcanvas-close-icon">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M11 1L1 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                           <path d="M1 1L11 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                     </button>
                  </div>
               </div>
               <!-- mobile menu -->
               <div class="mobile-menu">
                  <ul>
                     <li>
                        <a href="javascript:void(0)">
                        Buy Crypto
                        <i class="ic--round-plus"></i>
                        </a>
                        <ul>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-01.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>Spot Trading</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-02.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>Margin Trading</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-03.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>Trading Bot</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-04.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>Convert</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-05.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>API Services</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-06.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>Pre-Market trading</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                           <!-- menu item box list -->
                           <li>
                              <a href="#">
                                 <div class="menu-item-box">
                                    <div class="icon">
                                       <img src="../assets/images/menu/menu-icon-07.png" alt="menu-icon">
                                    </div>
                                    <div class="contents">
                                       <h6>GemSPACE</h6>
                                       <p>Comprehensive digital asset management</p>
                                    </div>
                                 </div>
                              </a>
                           </li>
                        </ul>
                     </li>

                     <li>
                        <a href="javascript:void(0)">
                        Markets
                        <i class="ic--round-plus"></i>
                        </a>
                        <ul>
                           <li>
                              <a href="javascript:void(0)">
                              Submenu
                              <i class="ic--round-plus"></i>
                              </a>
                              <ul>
                                 <li><a href="home-main.html">Main Home</a></li>
                                 <li><a href="home-lawyer.html">Lawyer</a></li>
                                 <li><a href="home-freelancer.html">Freelancer</a></li>
                                 <li><a href="home-agency.html">Digital Agency</a></li>
                                 <li><a href="home-photographer.html">Photographer</a></li>
                                 <li><a href="home-startup.html">Startup</a></li>
                                 <li><a href="home-creative.html">Creative Agency</a></li>
                                 <li><a href="home-portfolio.html">Personal Portfolio</a></li>
                                 <li><a href="home-architecture.html">Architechture</a></li>
                                 <li><a href="home-vertical.html">Vertical Slider</a></li>
                                 <li><a href="home-politician.html">Politician</a></li>
                                 <li><a href="home-shop.html">Minimal Shop</a></li>
                                 <li><a href="home-swipper.html">Swipper Slider</a></li>
                              </ul>
                           </li>
                        </ul>
                     </li>

                     <li>
                        <a href="javascript:void(0)">
                        Trade
                        </a>
                     </li>

                     <li>
                        <a href="javascript:void(0)">
                        Earn
                        </a>
                     </li>

                     <li> 
                        <a href="javascript:void(0)">
                        Institutional
                        </a>
                     </li>
                  </ul>
               </div>
               <div class="offcanvas-bottom">
                  <div class="offcanvas-btns d-flex flex-wrap gap-3">
                     <a class="td-btn btn-primary-outline btn-sm" href="../auth/sign-up.html">
                     <span class="btn-text">Log In</span>
                     </a>
                     <a class="td-btn btn-outline-primary btn-sm" href="../auth/sign-up.html">
                     <span class="btn-text">Sign Up</span>
                     </a>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="offcanvas-overlay"></div>
   <!-- Offcanvas area end -->

   <!-- Header section start -->
   <header>
      <div class="header-area header-style-one has-landing" id="header-sticky">
         <div class="container-fluid">
            <div class="header-inner">
               <div class="header-left">
                  <div class="header-logo">
                     <a href="index.html">
                        <img src="../assets/images/logo/logo.svg" alt="logo not found">
                     </a>
                  </div>
                  <div class="header-menu d-none d-lg-inline-flex">
                     <div class="td-main-menu">
                        <nav>
                           <ul>
                              <li>
                                 <a href="#">Buy Crypto</a>
                                 <ul class="dp-menu">
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-01.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Spot Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-02.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Margin Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-03.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Trading Bot</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-04.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Convert</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-05.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>API Services</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-06.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Pre-Market trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-07.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>GemSPACE</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                 </ul>
                              </li>
                              <li class="menu-has-children">
                                 <a href="#">Markets</a>
                                 <ul class="dp-menu">
                                    <!-- menu item box list -->
                                    <li class="menu-has-children">
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-01.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Spot Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                       <ul class="menu-sidebar">
                                          <div class="menu-tabs-wrapper">
                                             <div class="menu-tabs td-tab">
                                                <button class="scroll-left d-none">&#10094;</button>
                                                <ul class="nav nav-tabs" id="marketTab" role="tablist">
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link active" id="header-menu-usdt-tab" data-bs-toggle="tab" data-bs-target="#header-menu-usdt-tab-pane" type="button" role="tab" aria-controls="header-menu-usdt-tab-pane" aria-selected="true">USDT</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-usdc-tab" data-bs-toggle="tab" data-bs-target="#header-menu-usdc-tab-pane" type="button" role="tab" aria-controls="header-menu-usdc-tab-pane" aria-selected="false">USDC</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-btc-tab" data-bs-toggle="tab" data-bs-target="#header-menu-btc-tab-pane" type="button" role="tab" aria-controls="header-menu-btc-tab-pane" aria-selected="false">BTC</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-fiat-tab" data-bs-toggle="tab" data-bs-target="#header-menu-fiat-tab-pane" type="button" role="tab" aria-controls="header-menu-fiat-tab-pane" aria-selected="false">FIAT</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-xrp-tab" data-bs-toggle="tab" data-bs-target="#header-menu-xrp-tab-pane" type="button" role="tab" aria-controls="header-menu-xrp-tab-pane" aria-selected="false">XRP</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-doge-tab" data-bs-toggle="tab" data-bs-target="#header-menu-doge-tab-pane" type="button" role="tab" aria-controls="header-menu-doge-tab-pane" aria-selected="false">DOGE</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-shiba-tab" data-bs-toggle="tab" data-bs-target="#header-menu-shiba-tab-pane" type="button" role="tab" aria-controls="header-menu-shiba-tab-pane" aria-selected="false">SHIBA</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-ada-tab" data-bs-toggle="tab" data-bs-target="#header-menu-ada-tab-pane" type="button" role="tab" aria-controls="header-menu-ada-tab-pane" aria-selected="false">ADA</button>
                                                   </li>
                                                   <li class="nav-item" role="presentation">
                                                     <button class="nav-link" id="header-menu-sol-tab" data-bs-toggle="tab" data-bs-target="#header-menu-sol-tab-pane" type="button" role="tab" aria-controls="header-menu-sol-tab-pane" aria-selected="false">SOL</button>
                                                   </li>
                                                </ul>
                                                <button class="scroll-right">&#10095;</button>
                                             </div>
                                          </div>
                                          <div class="tab-content" id="marketTabContent">
                                             <div class="tab-pane fade show active" id="header-menu-usdt-tab-pane" role="tabpanel" aria-labelledby="header-menu-usdt-tab" tabindex="0">
                                                <div class="box">
                                                   <div class="search-box">
                                                      <div class="search-icon">
                                                         <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M12.25 12.2501L9.71658 9.7167M9.71658 9.7167C10.1499 9.28335 10.4937 8.76889 10.7282 8.20269C10.9627 7.63649 11.0835 7.02964 11.0835 6.41679C11.0835 5.80394 10.9627 5.19709 10.7282 4.63088C10.4937 4.06468 10.1499 3.55022 9.71658 3.11687C9.28323 2.68352 8.76877 2.33977 8.20257 2.10524C7.63637 1.87071 7.02952 1.75 6.41666 1.75C5.80381 1.75 5.19696 1.87071 4.63076 2.10524C4.06456 2.33977 3.5501 2.68352 3.11675 3.11687C2.24156 3.99206 1.74988 5.17908 1.74988 6.41679C1.74988 7.6545 2.24156 8.84151 3.11675 9.7167C3.99194 10.5919 5.17896 11.0836 6.41666 11.0836C7.65437 11.0836 8.84139 10.5919 9.71658 9.7167Z" stroke="#CCCCCC" stroke-linecap="round" stroke-linejoin="round"></path>
                                                         </svg>
                                                      </div>
                                                      <input type="text" placeholder="Search">
                                                   </div>
                                                   <ul class="pair-list">
                                                      <li class="active">
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/btc.png" alt="BTC">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">BTC/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/eth.png" alt="ETH">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">ETH/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/sol.png" alt="ETH">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">SOL/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/xmr.png" alt="XMR">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">XMR/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/erp.png" alt="ERP">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">ERP/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/met.png" alt="MET">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">MET/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/gsc.png" alt="GSC">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">GSC/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li>
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/bnb.png" alt="BNB">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">BNB/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li> 
                                                      <li>
                                                         <a href="#">
                                                            <div class="currency-icon">
                                                               <img src="../assets/images/currency-icon/pot.png" alt="POT">
                                                            </div>
                                                            <div class="text">
                                                               <span class="eth-usdt-span">POT/</span>
                                                               <span class="eth-usdt-span2">USDT</span>
                                                            </div>
                                                         </a>
                                                      </li> 
                                                   </ul>
                                                </div>
                                             </div>
                                             <div class="tab-pane fade" id="header-menu-usdc-tab-pane" role="tabpanel" aria-labelledby="header-menu-usdc-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-btc-tab-pane" role="tabpanel" aria-labelledby="header-menu-btc-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-fiat-tab-pane" role="tabpanel" aria-labelledby="header-menu-fiat-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-xrp-tab-pane" role="tabpanel" aria-labelledby="header-menu-xrp-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-doge-tab-pane" role="tabpanel" aria-labelledby="header-menu-doge-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-shiba-tab-pane" role="tabpanel" aria-labelledby="header-menu-shiba-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-ada-tab-pane" role="tabpanel" aria-labelledby="header-menu-ada-tab" tabindex="0">...</div>
                                             <div class="tab-pane fade" id="header-menu-sol-tab-pane" role="tabpanel" aria-labelledby="header-menu-sol-tab" tabindex="0">...</div>
                                           </div>
                                      </ul>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-02.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Margin Trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-03.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Trading Bot</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-04.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Convert</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-05.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>API Services</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-06.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>Pre-Market trading</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                    <!-- menu item box list -->
                                    <li>
                                       <a href="#">
                                          <div class="menu-item-box">
                                             <div class="icon">
                                                <img src="../assets/images/menu/menu-icon-07.png" alt="menu-icon">
                                             </div>
                                             <div class="contents">
                                                <h6>GemSPACE</h6>
                                                <p>Comprehensive digital asset management</p>
                                             </div>
                                          </div>
                                       </a>
                                    </li>
                                 </ul>
                              </li>
                              <li>
                                 <a href="#">Trade</a>
                              </li>
                              <li>
                                 <a href="#">Derivatives</a>
                              </li>
                              <li>
                                 <a href="#">Earn</a>
                              </li>
                              <li>
                                 <a href="#">Institutional</a>
                              </li>
                           </ul>
                        </nav>
                     </div>
                  </div>
               </div>
               <div class="header-right">
                  <div class="header-quick-actions d-flex align-items-center">

                     <div class="header-btns-wrap d-none d-sm-inline-flex">
                        <a class="td-btn btn-primary-outline btn-h-40" href="../auth/sign-up.html">
                           <span class="btn-text">Log In</span>
                        </a>
                        <a class="td-btn btn-outline-primary btn-h-40" href="../auth/sign-up.html">
                           <span class="btn-text">Sign Up</span>
                        </a>
                     </div>
                     
                     <div class="currency-switcher d-none d-sm-block">
                        <div class="defaults-select">
                           <select class="defaultselect2">
                              <option value="USD" selected>USD</option>
                              <option value="EUR">EUR</option>
                              <option value="CNY">CNY</option>
                              <option value="JPY">JPY</option>
                              <option value="GBP">GBP</option>
                              <option value="INR">INR</option>
                              <option value="AUD">AUD</option>
                              <option value="CAD">CAD</option>
                           </select>
                        </div>
                     </div>

                     <div class="language-dropdown">
                        <div class="language-box language-nav">
                           <div class="translate_wrapper">
                              <div class="current_lang">
                                 <div class="quick-action-item">
                                    <button type="button" class="action-icon">
                                       <i class="solar--global-line-duotone"></i>    
                                    </button>
                                 </div>
                              </div>
                              <div class="more_lang">
                                 <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English</span></div>
                                 <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                                 <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                                 <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                              </div>
                           </div>
                        </div>
                     </div>

                     <div class="theme-switcher">
                        <!-- Theme toggle button for switching between light and dark mode -->
                        <button id="theme-toggle" class="action-icon theme-switcher" aria-label="Toggle Theme">
                           <!-- Light mode iconvisble in dark mode) -->
                           <span class="light-mode" aria-show="true">
                              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.5 11.8831C15.5149 12.3286 14.4214 12.5766 13.2701 12.5766C8.93647 12.5766 5.42337 9.06352 5.42337 4.7299C5.42337 3.57851 5.67135 2.48505 6.11683 1.5C3.39432 2.73122 1.5 5.47102 1.5 8.65327C1.5 12.9869 5.0131 16.5 9.34672 16.5C12.529 16.5 15.2688 14.6056 16.5 11.8831Z" stroke="#080808" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                              </svg>
                           </span>
                           <!-- Dark mode icon (visible in light mode) -->
                           <span class="dark-mode" aria-show="true">
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 3V4.8M12 19.2V21M4.8 12H3M6.88271 6.88271L5.60991 5.60991M17.1173 6.88271L18.3901 5.60991M6.88271 17.121L5.60991 18.3938M17.1173 17.121L18.3901 18.3938M21 12H19.2M16.5 12C16.5 14.4853 14.4853 16.5 12 16.5C9.51472 16.5 7.5 14.4853 7.5 12C7.5 9.51472 9.51472 7.5 12 7.5C14.4853 7.5 16.5 9.51472 16.5 12Z" stroke="white" stroke-opacity="0.7" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                              </svg>
                           </span>
                       </button>
                     </div>

                     <div class="header-hamburger d-lg-none">
                        <a class="sidebar-toggle" href="javascript:void(0)">
                           <span class="menu-icon"><span></span></span>
                        </a>
                     </div>

                  </div>
               </div>
            </div>
         </div>
      </div>
   </header>
   <!-- Header section end -->

   <div class="has-smooth" id="has_smooth"></div>
   <div id="smooth-wrapper">
      <div id="smooth-content">
         <div class="body-wrapper">

            <!-- Body main wrapper start -->
            <main>

               <!-- Breadcrumb section start -->
               <section class="td-breadcrumb-area">
                  <div class="container">
                     <div class="row">
                        <div class="col-xxl-12">
                           <div class="breadcrumb-contents">
                              <h1 class="breadcrumb-title">Market Overview</h1>
                           </div>
                        </div>
                     </div>
                  </div>
               </section>
               <!-- Breadcrumb section end -->

               <!-- Market Trends Panel start -->
               <section class="market-trends-panel p-relative zi-11 pt-30 pb-30">
                  <div class="container">
                     <div class="row gy-30">
                        <div class="col-xxl-4 col-xl-4 col-lg-6">
                           <div class="trends-panel-card fix" data-background="../assets/images/bg/currency-table-bg.png">
                              <div class="heading-top">
                                 <div class="heading-left">
                                    <img src="../assets/images/icons/broadcast-satellite.svg" alt="Broadcast Satellite">
                                    <h6>Total User</h6>
                                 </div>
                                 <div class="heading-right">
                                    <a href="#"><img src="../assets/images/icons/square-top-down.svg" alt="Square"></a>
                                 </div>
                              </div>
                              <div class="market-trends-panel table-responsive">
                                 <table class="td-table table-currency">
                                    <tbody>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">BTC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">SOL</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">XMR</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/binance.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <!-- Add more rows as necessary -->
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                        </div>
                        <div class="col-xxl-4 col-xl-4 col-lg-6">
                           <div class="trends-panel-card fix" data-background="../assets/images/bg/currency-table-bg.png">
                              <div class="heading-top">
                                 <div class="heading-left">
                                    <img src="../assets/images/icons/interface-edit-tool.svg" alt="Broadcast Satellite">
                                    <h6>Top Boosted</h6>
                                 </div>
                                 <div class="heading-right">
                                    <a href="#"><img src="../assets/images/icons/square-top-down.svg" alt="Square"></a>
                                 </div>
                              </div>
                              <div class="market-trends-panel table-responsive">
                                 <table class="td-table table-currency">
                                    <tbody>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">BTC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">SOL</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">XMR</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/binance.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-green">+100USD <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.21591 4.7625L10.9666 8.63028C11.2006 8.87163 11.0587 9.33301 10.7505 9.33301H3.24918C2.94094 9.33301 2.79908 8.87163 3.03311 8.63028L6.78377 4.7625C6.9081 4.63429 7.09157 4.63429 7.21591 4.7625Z" fill="#03A66D"/>
                                                </svg>
                                             </span>
                                          </td>
                                       </tr>
                                       <!-- Add more rows as necessary -->
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                        </div>
                        <div class="col-xxl-4 col-xl-4 col-lg-6">
                           <div class="trends-panel-card fix" data-background="../assets/images/bg/currency-table-bg.png">
                              <div class="heading-top">
                                 <div class="heading-left">
                                    <img src="../assets/images/icons/interface-link.svg" alt="Broadcast Satellite">
                                    <h6>Top Failures</h6>
                                 </div>
                                 <div class="heading-right">
                                    <a href="#"><img src="../assets/images/icons/square-top-down.svg" alt="Square"></a>
                                 </div>
                              </div>
                              <div class="market-trends-panel table-responsive">
                                 <table class="td-table table-currency">
                                    <tbody>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">BTC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">SOL</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">XMR</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <tr>
                                          <td>
                                             <div class="table-currency-info">
                                                <div class="thumb">
                                                   <img src="../assets/images/currency-icon/svg/binance.svg" alt="Currency Icon">
                                                </div>
                                                <div class="contents">
                                                   <h4 class="name">ETC</h4>
                                                </div>
                                             </div>
                                          </td>
                                          <td>
                                             <div class="text-center">
                                                <span class="text-white">+100USD</span>
                                             </div>
                                          </td>
                                          <td>
                                             <span class="change-up text-danger">+100USD 
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M7.21591 9.2375L10.9666 5.36972C11.2006 5.12837 11.0587 4.66699 10.7505 4.66699H3.24918C2.94094 4.66699 2.79908 5.12837 3.03311 5.36972L6.78377 9.2375C6.9081 9.36571 7.09157 9.36571 7.21591 9.2375Z" fill="#EB4E5C"/>
                                                </svg>                                                
                                             </span>
                                          </td>
                                       </tr>
                                       <!-- Add more rows as necessary -->
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="inner-pages-shapes">
                     <div class="glow-two">
                        <img src="../assets/images/inner-pages-shapes/glow-02.png" alt="Glow Two">
                     </div>
                     <div class="shape-one">
                        <img src="../assets/images/inner-pages-shapes/shape-01.png" alt="Shape One">
                     </div>
                  </div>
               </section>
               <!-- Market Trends Panel end -->

               <!-- Market overview area start -->
               <section class="market-overview-area p-relative section_space-bottom">
                  <div class="container">
                     <div class="row">
                        <div class="col-xxl-12">
                           <div class="market-overview-filter mb-30">
                              <div class="market-overview-tab td-tab">
                                 <ul class="nav nav-tabs" id="marketTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                      <button class="nav-link active" data-background="../assets/images/bg/button-bg-01.png" id="market-tab-favorites" data-bs-toggle="tab" data-bs-target="#market-tab-favorites-pane" type="button" role="tab" aria-controls="market-tab-favorites-pane" aria-selected="true">Favorites</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                      <button class="nav-link" data-background="../assets/images/bg/button-bg-01.png" id="market-tab-spot" data-bs-toggle="tab" data-bs-target="#market-tab-spot-pane" type="button" role="tab" aria-controls="market-tab-spot-pane" aria-selected="false">Spot</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                      <button class="nav-link" data-background="../assets/images/bg/button-bg-01.png" id="market-tab-futures" data-bs-toggle="tab" data-bs-target="#market-tab-futures-pane" type="button" role="tab" aria-controls="market-tab-futures-pane" aria-selected="false">Futures</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                      <button class="nav-link" data-background="../assets/images/bg/button-bg-01.png" id="market-tab-all" data-bs-toggle="tab" data-bs-target="#market-tab-all-pane" type="button" role="tab" aria-controls="market-tab-all-pane" aria-selected="false">Futures</button>
                                    </li>
                                  </ul>
                              </div>
                              <div class="filter-search-bar">
                                 <input type="text" placeholder="Search">
                                 <span class="icon">
                                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                       <path d="M12.2501 12.2501L9.7167 9.7167M9.7167 9.7167C10.1501 9.28335 10.4938 8.76889 10.7283 8.20269C10.9629 7.63649 11.0836 7.02964 11.0836 6.41679C11.0836 5.80394 10.9629 5.19709 10.7283 4.63088C10.4938 4.06468 10.1501 3.55022 9.7167 3.11687C9.28335 2.68352 8.76889 2.33977 8.20269 2.10524C7.63649 1.87071 7.02964 1.75 6.41679 1.75C5.80394 1.75 5.19709 1.87071 4.63088 2.10524C4.06468 2.33977 3.55022 2.68352 3.11687 3.11687C2.24168 3.99206 1.75 5.17908 1.75 6.41679C1.75 7.6545 2.24168 8.84151 3.11687 9.7167C3.99206 10.5919 5.17908 11.0836 6.41679 11.0836C7.6545 11.0836 8.84151 10.5919 9.7167 9.7167Z" stroke="#CCCCCC" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>                                       
                                 </span>
                               </div>
                           </div>
                        </div>
                        <div class="col-xxl-12">
                           <!-- marketTabContent here -->
                           <div class="tab-content" id="marketTabContent">
                              <div class="tab-pane fade show active" id="market-tab-favorites-pane" role="tabpanel" aria-labelledby="market-tab-favorites" tabindex="0">
                                 <div class="market-overview-table-wrapper">
                                    <div class="table-container has-eerie-black table-responsive"> 
                                       <div class="table-heading">
                                          <h4 class="title">Recent Orders</h4>
                                       </div>
                                       <table class="td-table recent-order-table">
                                          <thead>
                                             <tr>
                                                <th>Pair
                                                   <button type="button" class="" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-custom-class="custom-tooltip" aria-label="Complete identity verification to access all Pocketwage services" data-bs-original-title="Complete identity verification to access all Pocketwage services">
                                                   <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <g clip-path="url(#clip0_46xzxz2_16647)">
                                                      <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"></path>
                                                      <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"></path>
                                                      <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"></path>
                                                      </g>
                                                      <defs>
                                                      <clipPath id="clip0_462_xzx16647">
                                                      <rect width="10" height="10" fill="white"></rect>
                                                      </clipPath>
                                                      </defs>
                                                   </svg> 
                                                   </button>
                                                </th>
                                                <th>Price <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                   <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                   </svg>
                                                   </button>
                                                </th>
                                                <th>
                                                   <div class="d-flex align-items-center gap-2">
                                                      <div class="timeframe-container">  
                                                         <select class="timeframe-select">  
                                                             <option value="24h">24h</option>  
                                                             <option value="1w">1w</option>  
                                                             <option value="1m">1m</option>  
                                                             <option value="1y">1y</option>  
                                                         </select>  
                                                     </div>
                                                     Change
                                                     <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                        <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                        </svg>
                                                     </button>
                                                   </div>
                                                </th>
                                                <th>Markets</th>
                                                <th>Market Cap</th>
                                                <th>Action</th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>BTC</span> Bitcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ETC)</span> Ethereum Classic</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+7 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SOL)</span> Solana</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XMR)</span> Monero</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-4USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/galatasaray-fan-token(GAL).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(GSC) Global Social Chain</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/dawn-protocol(DAWN).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(POT)</span> Potcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/superFarm-(SUPER).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SUPER)</span> SuperFarm</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/persistence(XPRT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XPRT)</span> Persistence</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/akash-network(AKT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(AKT)</span> Akash Network</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/anchor-protocol(ANC).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ANC)</span> Anchor Protocol </h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/compound(COMP).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(COMP)</span> Compound</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/divi(DIVI).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(DIVI)</span> Divi</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/voyager-token.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(VTHO)</span> VeThor Token</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/metronome(MET).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(MET) Metronome</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <!-- Add more rows as necessary -->
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="td-pagination d-flex justify-content-end mt-30">
                                       <nav>
                                          <ul>
                                             <li>
                                                <a href="#" class="disabled"><i class="solar--alt-arrow-left-outline"></i></a>
                                             </li>
                                             <li>
                                                <a class="current" href="#">1</a>
                                             </li>
                                             <li>
                                                <a href="#">2</a>
                                             </li>
                                             <li>
                                                <a href="#">
                                                   <i class="solar--menu-dots-bold"></i>
                                                </a>
                                             </li>
                                             <li>
                                                <a href="#">9</a>
                                             </li>
                                             <li>
                                                <a href="#">10</a>
                                             </li>
                                             <li>
                                                <a href="#"><i class="solar--alt-arrow-right-outline"></i></a>
                                             </li>
                                          </ul>
                                       </nav>
                                    </div>  
                                 </div>  
                              </div>
                              <div class="tab-pane fade" id="market-tab-spot-pane" role="tabpanel" aria-labelledby="market-tab-spot" tabindex="0">
                                 <div class="market-overview-table-wrapper">
                                    <div class="table-container has-eerie-black table-responsive"> 
                                       <div class="table-heading">
                                          <h4 class="title">Recent Orders</h4>
                                       </div>
                                       <table class="td-table recent-order-table">
                                          <thead>
                                             <tr>
                                                <th>Pair
                                                   <button type="button" class="" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-custom-class="custom-tooltip" aria-label="Complete identity verification to access all Pocketwage services" data-bs-original-title="Complete identity verification to access all Pocketwage services">
                                                   <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <g clip-path="url(#clip0_46xzxz2_16647)">
                                                      <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"></path>
                                                      <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"></path>
                                                      <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"></path>
                                                      </g>
                                                      <defs>
                                                      <clipPath id="clip0_462_xzx16647">
                                                      <rect width="10" height="10" fill="white"></rect>
                                                      </clipPath>
                                                      </defs>
                                                   </svg> 
                                                   </button>
                                                </th>
                                                <th>Price <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                   <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                   </svg>
                                                   </button>
                                                </th>
                                                <th>
                                                   <div class="d-flex align-items-center gap-2">
                                                      <div class="timeframe-container">  
                                                         <select class="timeframe-select">  
                                                             <option value="24h">24h</option>  
                                                             <option value="1w">1w</option>  
                                                             <option value="1m">1m</option>  
                                                             <option value="1y">1y</option>  
                                                         </select>  
                                                     </div>
                                                     Change
                                                     <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                        <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                        </svg>
                                                     </button>
                                                   </div>
                                                </th>
                                                <th>Markets</th>
                                                <th>Market Cap</th>
                                                <th>Action</th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>BTC</span> Bitcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ETC)</span> Ethereum Classic</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+7 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SOL)</span> Solana</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XMR)</span> Monero</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-4USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/galatasaray-fan-token(GAL).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(GSC) Global Social Chain</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/dawn-protocol(DAWN).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(POT)</span> Potcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/superFarm-(SUPER).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SUPER)</span> SuperFarm</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/persistence(XPRT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XPRT)</span> Persistence</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/akash-network(AKT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(AKT)</span> Akash Network</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/anchor-protocol(ANC).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ANC)</span> Anchor Protocol </h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/compound(COMP).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(COMP)</span> Compound</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/divi(DIVI).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(DIVI)</span> Divi</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/voyager-token.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(VTHO)</span> VeThor Token</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/metronome(MET).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(MET) Metronome</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <!-- Add more rows as necessary -->
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="td-pagination d-flex justify-content-end mt-30">
                                       <nav>
                                          <ul>
                                             <li>
                                                <a href="#" class="disabled"><i class="solar--alt-arrow-left-outline"></i></a>
                                             </li>
                                             <li>
                                                <a class="current" href="#">1</a>
                                             </li>
                                             <li>
                                                <a href="#">2</a>
                                             </li>
                                             <li>
                                                <a href="#">
                                                   <i class="solar--menu-dots-bold"></i>
                                                </a>
                                             </li>
                                             <li>
                                                <a href="#">9</a>
                                             </li>
                                             <li>
                                                <a href="#">10</a>
                                             </li>
                                             <li>
                                                <a href="#"><i class="solar--alt-arrow-right-outline"></i></a>
                                             </li>
                                          </ul>
                                       </nav>
                                    </div>  
                                 </div>  
                              </div>
                              <div class="tab-pane fade" id="market-tab-futures-pane" role="tabpanel" aria-labelledby="market-tab-futures" tabindex="0">
                                 <div class="market-overview-table-wrapper">
                                    <div class="table-container has-eerie-black table-responsive"> 
                                       <div class="table-heading">
                                          <h4 class="title">Recent Orders</h4>
                                       </div>
                                       <table class="td-table recent-order-table">
                                          <thead>
                                             <tr>
                                                <th>Pair
                                                   <button type="button" class="" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-custom-class="custom-tooltip" aria-label="Complete identity verification to access all Pocketwage services" data-bs-original-title="Complete identity verification to access all Pocketwage services">
                                                   <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <g clip-path="url(#clip0_46xzxz2_16647)">
                                                      <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"></path>
                                                      <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"></path>
                                                      <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"></path>
                                                      </g>
                                                      <defs>
                                                      <clipPath id="clip0_462_xzx16647">
                                                      <rect width="10" height="10" fill="white"></rect>
                                                      </clipPath>
                                                      </defs>
                                                   </svg> 
                                                   </button>
                                                </th>
                                                <th>Price <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                   <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                   </svg>
                                                   </button>
                                                </th>
                                                <th>
                                                   <div class="d-flex align-items-center gap-2">
                                                      <div class="timeframe-container">  
                                                         <select class="timeframe-select">  
                                                             <option value="24h">24h</option>  
                                                             <option value="1w">1w</option>  
                                                             <option value="1m">1m</option>  
                                                             <option value="1y">1y</option>  
                                                         </select>  
                                                     </div>
                                                     Change
                                                     <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                        <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                        </svg>
                                                     </button>
                                                   </div>
                                                </th>
                                                <th>Markets</th>
                                                <th>Market Cap</th>
                                                <th>Action</th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>BTC</span> Bitcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ETC)</span> Ethereum Classic</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+7 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SOL)</span> Solana</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XMR)</span> Monero</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-4USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/galatasaray-fan-token(GAL).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(GSC) Global Social Chain</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/dawn-protocol(DAWN).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(POT)</span> Potcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/superFarm-(SUPER).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SUPER)</span> SuperFarm</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/persistence(XPRT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XPRT)</span> Persistence</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/akash-network(AKT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(AKT)</span> Akash Network</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/anchor-protocol(ANC).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ANC)</span> Anchor Protocol </h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/compound(COMP).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(COMP)</span> Compound</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/divi(DIVI).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(DIVI)</span> Divi</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/voyager-token.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(VTHO)</span> VeThor Token</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/metronome(MET).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(MET) Metronome</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <!-- Add more rows as necessary -->
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="td-pagination d-flex justify-content-end mt-30">
                                       <nav>
                                          <ul>
                                             <li>
                                                <a href="#" class="disabled"><i class="solar--alt-arrow-left-outline"></i></a>
                                             </li>
                                             <li>
                                                <a class="current" href="#">1</a>
                                             </li>
                                             <li>
                                                <a href="#">2</a>
                                             </li>
                                             <li>
                                                <a href="#">
                                                   <i class="solar--menu-dots-bold"></i>
                                                </a>
                                             </li>
                                             <li>
                                                <a href="#">9</a>
                                             </li>
                                             <li>
                                                <a href="#">10</a>
                                             </li>
                                             <li>
                                                <a href="#"><i class="solar--alt-arrow-right-outline"></i></a>
                                             </li>
                                          </ul>
                                       </nav>
                                    </div>  
                                 </div>  
                              </div>
                              <div class="tab-pane fade" id="market-tab-all-pane" role="tabpanel" aria-labelledby="market-tab-all" tabindex="0">
                                 <div class="market-overview-table-wrapper">
                                    <div class="table-container has-eerie-black table-responsive"> 
                                       <div class="table-heading">
                                          <h4 class="title">Recent Orders</h4>
                                       </div>
                                       <table class="td-table recent-order-table">
                                          <thead>
                                             <tr>
                                                <th>Pair
                                                   <button type="button" class="" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-custom-class="custom-tooltip" aria-label="Complete identity verification to access all Pocketwage services" data-bs-original-title="Complete identity verification to access all Pocketwage services">
                                                   <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <g clip-path="url(#clip0_46xzxz2_16647)">
                                                      <path d="M5 0C4.0111 0 3.0444 0.293245 2.22215 0.842652C1.39991 1.39206 0.759043 2.17295 0.380605 3.08658C0.00216643 4.00021 -0.0968503 5.00555 0.0960759 5.97545C0.289002 6.94536 0.765206 7.83627 1.46447 8.53554C2.16373 9.2348 3.05465 9.711 4.02455 9.90393C4.99446 10.0969 5.99979 9.99784 6.91342 9.6194C7.82705 9.24096 8.60794 8.6001 9.15735 7.77785C9.70676 6.95561 10 5.98891 10 5C9.99857 3.67436 9.47133 2.40342 8.53395 1.46605C7.59658 0.528678 6.32565 0.00143378 5 0ZM5 8.75C4.25832 8.75 3.5333 8.53007 2.91661 8.11801C2.29993 7.70596 1.81928 7.12029 1.53545 6.43506C1.25163 5.74984 1.17736 4.99584 1.32206 4.26841C1.46675 3.54098 1.8239 2.8728 2.34835 2.34835C2.8728 1.8239 3.54099 1.46675 4.26841 1.32206C4.99584 1.17736 5.74984 1.25162 6.43507 1.53545C7.12029 1.81928 7.70596 2.29993 8.11801 2.91661C8.53007 3.5333 8.75 4.25832 8.75 5C8.74879 5.99419 8.35331 6.94731 7.65031 7.65031C6.94732 8.35331 5.99419 8.74879 5 8.75Z" fill="#999999"></path>
                                                      <path d="M4.81041 3.97725H4.68541C4.52165 3.97323 4.36229 4.03052 4.23858 4.13789C4.11487 4.24526 4.03572 4.39497 4.01666 4.55767C4.00576 4.72043 4.05892 4.881 4.1648 5.00509C4.27068 5.12919 4.42088 5.20697 4.58332 5.22183V7.15933C4.58332 7.32509 4.64917 7.48406 4.76638 7.60127C4.88359 7.71848 5.04256 7.78433 5.20832 7.78433C5.37408 7.78433 5.53306 7.71848 5.65027 7.60127C5.76748 7.48406 5.83332 7.32509 5.83332 7.15933V5.00017C5.83332 4.72887 5.72555 4.46869 5.53372 4.27685C5.34188 4.08502 5.0817 3.97725 4.81041 3.97725Z" fill="#999999"></path>
                                                      <path d="M4.92917 3.52766C5.07059 3.52766 5.20882 3.48572 5.32641 3.40716C5.44399 3.32859 5.53563 3.21692 5.58975 3.08628C5.64386 2.95563 5.65802 2.81186 5.63043 2.67317C5.60285 2.53447 5.53475 2.40707 5.43475 2.30708C5.33476 2.20708 5.20736 2.13898 5.06866 2.1114C4.92997 2.08381 4.7862 2.09797 4.65555 2.15208C4.5249 2.2062 4.41324 2.29784 4.33467 2.41542C4.25611 2.53301 4.21417 2.67124 4.21417 2.81266C4.21412 2.90657 4.23257 2.99957 4.26849 3.08634C4.3044 3.17311 4.35706 3.25196 4.42347 3.31836C4.48987 3.38477 4.56872 3.43743 4.65549 3.47334C4.74226 3.50925 4.83526 3.52771 4.92917 3.52766Z" fill="#999999"></path>
                                                      </g>
                                                      <defs>
                                                      <clipPath id="clip0_462_xzx16647">
                                                      <rect width="10" height="10" fill="white"></rect>
                                                      </clipPath>
                                                      </defs>
                                                   </svg> 
                                                   </button>
                                                </th>
                                                <th>Price <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                   <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                   <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                   </svg>
                                                   </button>
                                                </th>
                                                <th>
                                                   <div class="d-flex align-items-center gap-2">
                                                      <div class="timeframe-container">  
                                                         <select class="timeframe-select">  
                                                             <option value="24h">24h</option>  
                                                             <option value="1w">1w</option>  
                                                             <option value="1m">1m</option>  
                                                             <option value="1y">1y</option>  
                                                         </select>  
                                                     </div>
                                                     Change
                                                     <button type="button"><svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M4.48501 0.582426L7.69986 3.89767C7.90046 4.10453 7.77886 4.5 7.51465 4.5H1.08496C0.820749 4.5 0.699152 4.10453 0.899753 3.89767L4.1146 0.582426C4.22117 0.472525 4.37844 0.472525 4.48501 0.582426Z" fill="#999999"/>
                                                        <path d="M4.1146 11.4176L0.899753 8.10233C0.699151 7.89547 0.820748 7.5 1.08496 7.5L7.51465 7.5C7.77886 7.5 7.90046 7.89547 7.69986 8.10233L4.48501 11.4176C4.37843 11.5275 4.22117 11.5275 4.1146 11.4176Z" fill="#999999"/>
                                                        </svg>
                                                     </button>
                                                   </div>
                                                </th>
                                                <th>Markets</th>
                                                <th>Market Cap</th>
                                                <th>Action</th>
                                             </tr>
                                          </thead>
                                          <tbody>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/bitcoin.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>BTC</span> Bitcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/ethereum.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ETC)</span> Ethereum Classic</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+7 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/sol.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SOL)</span> Solana</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+200USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/monero(XMR).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XMR)</span> Monero</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-4USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/galatasaray-fan-token(GAL).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(GSC) Global Social Chain</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/dawn-protocol(DAWN).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(POT)</span> Potcoin</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/superFarm-(SUPER).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(SUPER)</span> SuperFarm</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/persistence(XPRT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(XPRT)</span> Persistence</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/akash-network(AKT).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(AKT)</span> Akash Network</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/anchor-protocol(ANC).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(ANC)</span> Anchor Protocol </h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/compound(COMP).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(COMP)</span> Compound</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/divi(DIVI).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(DIVI)</span> Divi</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/voyager-token.svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name"><span>(VTHO)</span> VeThor Token</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+400USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-green">
                                                      <span>+3.5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="up" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <tr>
                                                <td>
                                                   <div class="table-currency-info">
                                                      <div class="thumb">
                                                         <img src="../assets/images/currency-icon/svg/metronome(MET).svg" alt="Currency Icon">
                                                      </div>
                                                      <div class="contents">
                                                         <h4 class="name">(MET) Metronome</h4>
                                                      </div>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span>-5 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <div class="text-danger">
                                                      <span> -1 USD</span>
                                                   </div>
                                                </td>
                                                <td>
                                                   <canvas class="mini-chart" data-change="down" width="50" height="25"></canvas>
                                                </td>
                                                <td>
                                                   <span class="text-white">+$423.33M</span>
                                                </td>
                                                <td>
                                                   <div class="table-actions">
                                                      <button class="icon is-outline" data-bs-toggle="tooltip" title="Link">
                                                         <iconify-icon icon="solar:square-top-down-outline"></iconify-icon>   
                                                      </button>
                                                      <button class="text is-primary" data-bs-toggle="tooltip" title="Trade">Trade</button>
                                                  </div> 
                                                </td>
                                             </tr>
                                             <!-- Add more rows as necessary -->
                                          </tbody>
                                       </table>
                                    </div>
                                    <div class="td-pagination d-flex justify-content-end mt-30">
                                       <nav>
                                          <ul>
                                             <li>
                                                <a href="#" class="disabled"><i class="solar--alt-arrow-left-outline"></i></a>
                                             </li>
                                             <li>
                                                <a class="current" href="#">1</a>
                                             </li>
                                             <li>
                                                <a href="#">2</a>
                                             </li>
                                             <li>
                                                <a href="#">
                                                   <i class="solar--menu-dots-bold"></i>
                                                </a>
                                             </li>
                                             <li>
                                                <a href="#">9</a>
                                             </li>
                                             <li>
                                                <a href="#">10</a>
                                             </li>
                                             <li>
                                                <a href="#"><i class="solar--alt-arrow-right-outline"></i></a>
                                             </li>
                                          </ul>
                                       </nav>
                                    </div>  
                                 </div>  
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="inner-pages-shapes">
                     <div class="shape-one">
                        <img src="../assets/images/inner-pages-shapes/shape-01.png" alt="Shape One">
                     </div>
                  </div>
               </section>
               <!-- Market overview area end -->
               
            </main>
            <!-- Body main wrapper end -->

            <!-- Footer area start -->
            <footer>
               <div class="footer-line"></div>
               <div class="footer-primary">
                  <div class="td-footer-section p-relative zi-11 fix">
                     <div class="footer-bg">
                        <img src="../assets/images//bg/footer-bg.png" alt="">
                     </div>
                     <div class="container">
                        <div class="footer-main">
                           <div class="footer-top">
                              <div class="footer-logo">
                                 <a href="#">
                                    <img src="../assets/images/logo/logo.svg" alt="Footer Logo">
                                 </a>
                              </div>
                           </div>
                           <div class="footer-widget-grid">
                              <!-- footer widget -->
                              <div class="footer-widget">
                                 <div class="footer-wg-head">
                                    <h5 class="title">Information</h5>
                                 </div>
                                 <div class="footer-links">
                                    <ul>
                                       <li><a href="#">About Us</a></li>
                                       <li><a href="#">Join Us</a></li>
                                       <li><a href="#">Media Kit</a></li>
                                       <li><a href="#">Blog</a></li>
                                       <li><a href="#">News & Announcements</a></li>
                                       <li><a href="#">Privacy Media Kit</a></li>
                                       <li><a href="#">Pocketwage Labs</a></li>
                                       <li><a href="#">Pocketwage Ventures</a></li>
                                       <li><a href="#">PoR (Proof of Reserves)</a></li>
                                       <li><a href="#">Security</a></li>
                                       <li><a href="#">Terms of Use</a></li>
                                       <li><a href="#">Privacy Policy</a></li>
                                       <li><a href="#">Risk Disclosure Statement</a></li>
                                       <li><a href="#">AML & CFT</a></li>
                                       <li><a href="#">Law Enforcement Requests</a></li>
                                       <li><a href="#">Whistleblower Contact</a></li>
                                    </ul>
                                 </div>
                              </div>
                              <!-- footer widget -->
                              <div class="footer-widget">
                                 <div class="footer-wg-head">
                                    <h5 class="title">Products</h5>
                                 </div>
                                 <div class="footer-links">
                                    <ul>
                                       <li><a href="#">Fast Trade</a></li>
                                       <li><a href="#">PwCard</a></li>
                                       <li><a href="#">Spot TradingKit</a></li>
                                       <li><a href="#">Futures Trading</a></li>
                                       <li><a href="#">Margin Trading</a></li>
                                       <li><a href="#">Crypto Lending</a></li>
                                       <li><a href="#">ETFs</a></li>
                                       <li><a href="#">Pocketwage Earn</a></li>
                                       <li><a href="#">Fast Trade</a></li>
                                       <li><a href="#">Referral</a></li>
                                       <li><a href="#">GemSPACE</a></li>
                                       <li><a href="#">Coincap Learn</a></li>
                                       <li><a href="#">Converter</a></li>
                                    </ul>
                                 </div>
                              </div>
                              <!-- footer widget -->
                              <div class="footer-widget">
                                 <div class="footer-wg-head">
                                    <h5 class="title">Service</h5>
                                 </div>
                                 <div class="footer-links">
                                    <ul>
                                       <li><a href="#">Beginner's Guide</a></li>
                                       <li><a href="#">Help Center</a></li>
                                       <li><a href="#">Submit a Ticket</a></li>
                                       <li><a href="#">Technical Support</a></li>
                                       <li><a href="#">Bug Bounty</a></li>
                                       <li><a href="#">Ticket Verification</a></li>
                                       <li><a href="#">Pocketwage Labs</a></li>
                                       <li><a href="#">Pocketwage Ventures</a></li>
                                       <li><a href="#">Official Verification Center</a></li>
                                       <li><a href="#">Fees & VIP</a></li>
                                       <li><a href="#">Safeguard Program</a></li>
                                       <li><a href="#">Special Treatment</a></li>
                                       <li><a href="#">Delistings</a></li>
                                       <li><a href="#">Sitemap</a></li>
                                    </ul>
                                 </div>
                              </div>
                              <!-- footer widget -->
                              <div class="footer-widget">
                                 <div class="footer-wg-head">
                                    <h5 class="title">Crypto Prices</h5>
                                 </div>
                                 <div class="footer-links">
                                    <ul>
                                       <li><a href="#">Bitcoin (BTC) Price</a></li>
                                       <li><a href="#">Ethereum (ETH) Price</a></li>
                                       <li><a href="#">Ripple (XRP) Price</a></li>
                                       <li><a href="#">Pocketwage Token (KCS) Price</a></li>
                                       <li><a href="#">More Prices</a></li>
                                    </ul>
                                 </div>
                              </div>
                              <!-- footer widget -->
                              <div class="footer-widget">
                                 <div class="footer-wg-head">
                                    <h5 class="title">Community</h5>
                                 </div>
                                 <div class="footer-social">
                                    <ul>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M10.69 0.110352H12.6579L8.35868 5.02406L13.4163 11.7105H9.45623L6.35453 7.65521L2.80548 11.7105H0.836426L5.43484 6.45474L0.583008 0.110352H4.64365L7.44732 3.81705L10.69 0.110352ZM9.99935 10.5326H11.0898L4.05115 1.22635H2.88103L9.99935 10.5326Z" fill="#BBBBBB"/>
                                             </svg>                                       
                                          </a>
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M3.0835 10.2292V5.5625C3.0835 5.27194 3.0835 5.12666 3.05353 5.00702C2.96387 4.64909 2.6844 4.36962 2.32648 4.27997C2.20683 4.25 2.06155 4.25 1.771 4.25C1.48044 4.25 1.33516 4.25 1.21552 4.27997C0.857591 4.36962 0.57812 4.64909 0.488465 5.00702C0.458496 5.12666 0.458496 5.27194 0.458496 5.5625V10.2292C0.458496 10.5197 0.458496 10.665 0.488465 10.7846C0.57812 11.1426 0.857591 11.422 1.21552 11.5117C1.33516 11.5417 1.48044 11.5417 1.771 11.5417C2.06155 11.5417 2.20683 11.5417 2.32648 11.5117C2.6844 11.422 2.96387 11.1426 3.05353 10.7846C3.0835 10.665 3.0835 10.5197 3.0835 10.2292Z" fill="#BBBBBB"/>
                                                <path d="M3.0835 1.771C3.0835 2.49587 2.49587 3.0835 1.771 3.0835C1.04612 3.0835 0.458496 2.49587 0.458496 1.771C0.458496 1.04612 1.04612 0.458496 1.771 0.458496C2.49587 0.458496 3.0835 1.04612 3.0835 1.771Z" fill="#BBBBBB"/>
                                                <path d="M5.89853 4.25H5.41667C4.86669 4.25 4.59171 4.25 4.42085 4.42085C4.25 4.59171 4.25 4.86669 4.25 5.41667V10.375C4.25 10.925 4.25 11.2 4.42085 11.3708C4.59171 11.5417 4.86669 11.5417 5.41667 11.5417H5.70835C6.25831 11.5417 6.5333 11.5417 6.70415 11.3708C6.875 11.2 6.87501 10.925 6.87501 10.375L6.87503 8.3334C6.87503 7.3669 7.18307 6.5834 8.09292 6.5834C8.54785 6.5834 8.91664 6.97515 8.91664 7.4584V10.0834C8.91664 10.6334 8.91664 10.9084 9.0875 11.0792C9.25835 11.2501 9.53334 11.2501 10.0833 11.2501H10.3743C10.9241 11.2501 11.199 11.2501 11.3699 11.0793C11.5407 10.9085 11.5408 10.6335 11.5409 10.0837L11.5417 6.87514C11.5417 5.42539 10.1629 4.25014 8.79811 4.25014C8.02121 4.25014 7.32805 4.63097 6.87503 5.22647C6.87502 4.85896 6.87501 4.67521 6.79517 4.53875C6.74461 4.45234 6.67267 4.38039 6.58625 4.32984C6.44979 4.25 6.26604 4.25 5.89853 4.25Z" fill="#BBBBBB"/>
                                             </svg>                                                                              
                                          </a>
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.24275 5.95519C2.61128 5.95519 2.47949 6.07549 2.47949 6.65195V7.69708C2.47949 8.27354 2.61128 8.39384 3.24275 8.39384H4.76927V12.5744C4.76927 13.1509 4.90105 13.2712 5.53252 13.2712H7.05904C7.69051 13.2712 7.8223 13.1509 7.8223 12.5744V8.39384H9.53634C10.0153 8.39384 10.1387 8.30887 10.2702 7.8885L10.5973 6.84336C10.8227 6.12326 10.6838 5.95519 9.86345 5.95519H7.8223V4.21329C7.8223 3.82848 8.16402 3.51653 8.58555 3.51653H10.7579C11.3894 3.51653 11.5212 3.39622 11.5212 2.81977V1.42625C11.5212 0.849799 11.3894 0.729492 10.7579 0.729492H8.58555C6.47788 0.729492 4.76927 2.28924 4.76927 4.21329V5.95519H3.24275Z" fill="#BBBBBB"/>
                                             </svg>                                                                                
                                          </a> 
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 14L0.984083 10.4049C0.376833 9.35258 0.05775 8.15967 0.0583333 6.93642C0.0600833 3.11208 3.17217 0 6.99592 0C8.8515 0.000583333 10.5933 0.723333 11.9035 2.03467C13.2131 3.346 13.9341 5.089 13.9335 6.94283C13.9318 10.7677 10.8197 13.8798 6.99592 13.8798C5.83508 13.8792 4.69117 13.5882 3.67792 13.0352L0 14ZM3.84825 11.7793C4.82592 12.3597 5.75925 12.7073 6.99358 12.7079C10.1716 12.7079 12.7604 10.1214 12.7622 6.94167C12.7633 3.7555 10.1868 1.1725 6.99825 1.17133C3.81792 1.17133 1.23083 3.75783 1.22967 6.937C1.22908 8.23492 1.60942 9.20675 2.24817 10.2235L1.66542 12.3515L3.84825 11.7793ZM10.4907 8.59192C10.4475 8.51958 10.332 8.47642 10.1582 8.3895C9.98492 8.30258 9.13267 7.88317 8.97342 7.82542C8.81475 7.76767 8.69925 7.7385 8.58317 7.91233C8.46767 8.08558 8.13517 8.47642 8.03425 8.59192C7.93333 8.70742 7.83183 8.722 7.65858 8.63508C7.48533 8.54817 6.9265 8.36558 6.26442 7.77467C5.74933 7.315 5.40108 6.74742 5.30017 6.57358C5.19925 6.40033 5.28967 6.30642 5.376 6.22008C5.45417 6.1425 5.54925 6.01767 5.63617 5.91617C5.72425 5.81583 5.75283 5.7435 5.81117 5.62742C5.86892 5.51192 5.84033 5.41042 5.79658 5.3235C5.75283 5.23717 5.40633 4.38375 5.26225 4.03667C5.12108 3.69892 4.97817 3.74442 4.872 3.73917L4.5395 3.73333C4.424 3.73333 4.23617 3.7765 4.0775 3.95033C3.91883 4.12417 3.47083 4.543 3.47083 5.39642C3.47083 6.24983 4.09208 7.07408 4.17842 7.18958C4.26533 7.30508 5.4005 9.05625 7.13942 9.807C7.553 9.9855 7.87617 10.0922 8.12758 10.1722C8.54292 10.304 8.92092 10.2853 9.21958 10.241C9.55267 10.1914 10.2451 9.82158 10.3898 9.41675C10.5344 9.01133 10.5344 8.66425 10.4907 8.59192Z" fill="#BBBBBB"/>
                                             </svg>                                                                              
                                          </a>
                                       </li>
                                    </ul>
                                    <ul>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g clip-path="url(#clip0_594_60489)">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7.00579 0C3.13177 0 0 3.15486 0 7.05785C0 10.1777 2.00663 12.8186 4.79036 13.7533C5.1384 13.8236 5.26589 13.6015 5.26589 13.4146C5.26589 13.251 5.25442 12.6901 5.25442 12.1058C3.30557 12.5265 2.89974 11.2644 2.89974 11.2644C2.58655 10.4465 2.1225 10.2362 2.1225 10.2362C1.48465 9.80386 2.16896 9.80386 2.16896 9.80386C2.87651 9.85061 3.24778 10.5283 3.24778 10.5283C3.87402 11.6033 4.88315 11.2996 5.28912 11.1126C5.34705 10.6568 5.53276 10.3413 5.72994 10.1661C4.1756 10.0025 2.54023 9.39488 2.54023 6.68385C2.54023 5.91263 2.81844 5.28166 3.25926 4.79094C3.1897 4.6157 2.94606 3.89109 3.32895 2.92126C3.32895 2.92126 3.92048 2.73426 5.25427 3.64573C5.82531 3.49123 6.41422 3.41264 7.00579 3.41198C7.59733 3.41198 8.20034 3.49386 8.75717 3.64573C10.0911 2.73426 10.6826 2.92126 10.6826 2.92126C11.0655 3.89109 10.8217 4.6157 10.7522 4.79094C11.2046 5.28166 11.4714 5.91263 11.4714 6.68385C11.4714 9.39488 9.83599 9.99072 8.27003 10.1661C8.52529 10.3881 8.74555 10.8087 8.74555 11.4748C8.74555 12.4212 8.73408 13.1809 8.73408 13.4145C8.73408 13.6015 8.86171 13.8236 9.2096 13.7535C11.9933 12.8185 14 10.1777 14 7.05785C14.0114 3.15486 10.8682 0 7.00579 0Z" fill="#BBBBBB"/>
                                                </g>
                                                <defs>
                                                <clipPath id="clip0_594_60489">
                                                <rect width="14" height="14" fill="white"/>
                                                </clipPath>
                                                </defs>
                                             </svg>                                                                               
                                          </a>
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <g clip-path="url(#clip0_594_60491)">
                                                <path d="M7.39439 12.7659C4.40976 12.7659 1.01848 9.22504 1.28969 6.28541C1.5609 3.47949 5.35949 0.873999 8.17433 1.27484C10.6502 1.60867 12.8549 4.68203 12.7188 7.1206C12.5837 9.69227 10.0057 12.7659 7.39439 12.7659Z" fill="black"/>
                                                <path d="M9.19342 13.9007C9.00125 13.8377 8.71343 13.7748 8.55363 13.7116C8.36176 13.6173 8.1378 13.6173 7.43446 13.6486C6.76289 13.6799 6.41092 13.6486 5.93153 13.5854C3.37298 13.1445 1.3262 11.3161 0.590773 8.7945C0.462755 8.32168 0.430976 8.0696 0.430976 6.93483C0.430976 5.70519 0.398896 5.61093 0.2391 5.10648C-0.688202 2.61619 1.19848 0 3.88475 0C4.4283 0 5.09986 0.157805 5.48361 0.346638C5.73965 0.472823 5.89945 0.504147 6.89061 0.504147C7.78613 0.504147 8.1378 0.535767 8.58541 0.661952C10.6004 1.16639 12.2311 2.45868 13.0945 4.2554C13.6381 5.35856 13.8302 6.49363 13.7019 7.78561C13.6381 8.47948 13.6381 8.54243 13.7978 8.95201C14.3096 10.4337 13.8299 12.1356 12.5827 13.1445C12.1987 13.4598 11.4315 13.8064 10.9518 13.9326C10.4718 14.0272 9.70459 14.0272 9.2246 13.901L9.19342 13.9007ZM8.32968 11.4736C9.3853 11.2532 10.3123 10.6226 10.6643 9.86633C11.1119 8.95201 10.9839 7.88018 10.3765 7.21823C9.86469 6.65084 9.09718 6.33582 7.27436 5.89462C6.05894 5.61093 5.77113 5.48474 5.51539 5.20105C5.29144 4.94868 5.25936 4.75985 5.45154 4.47615C5.64341 4.16084 6.1231 3.97171 6.76259 3.94038C7.69019 3.87744 8.10572 4.09789 8.64927 4.94897C9.0651 5.5796 9.48063 5.73711 10.0248 5.48474C10.7281 5.1381 10.6643 4.00333 9.89647 3.34138C9.16104 2.74267 8.36176 2.4903 7.01832 2.4903C5.61133 2.4903 4.65165 2.77399 4.01216 3.40432C3.50039 3.90876 3.40476 4.33013 3.40476 4.9803C3.40476 5.55288 3.5007 6.05183 4.04424 6.52495C4.61987 7.06072 5.09956 7.25014 6.82615 7.65973C8.04156 7.94342 8.36146 8.10093 8.64897 8.41624C8.77729 8.57405 8.84115 8.69994 8.84115 8.98363C8.84115 9.29894 8.80907 9.36189 8.55333 9.61426C8.16958 9.99222 7.59395 10.1497 6.85853 10.1187C5.99508 10.0555 5.61133 9.80338 5.16372 8.92068C4.84383 8.32197 4.58809 8.10122 4.17226 8.10122C3.24466 8.10122 2.92507 8.85774 3.40476 9.83471C3.82059 10.6861 4.65195 11.2535 5.70727 11.4739C6.34676 11.6001 7.75405 11.6001 8.36176 11.4739L8.32968 11.4736Z" fill="#BBBBBB"/>
                                                </g>
                                                <defs>
                                                <clipPath id="clip0_594_60491">
                                                <rect width="14" height="14" fill="white"/>
                                                </clipPath>
                                                </defs>
                                             </svg>                                                                                                                    
                                          </a>
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.90683 1.90868C4.87457 1.71141 5.9162 1.60433 7.00039 1.60433C8.08458 1.60433 9.12621 1.71141 10.0939 1.90868L10.1682 1.9238C11.2734 2.14875 12.0098 2.29864 12.6444 3.11453C13.2721 3.92167 13.2718 4.85769 13.2713 6.30738V6.30739V7.69293V7.69295C13.2718 9.14263 13.2721 10.0787 12.6444 10.8858C12.0098 11.7017 11.2734 11.8516 10.1682 12.0765L10.0939 12.0917C9.12621 12.2889 8.08458 12.396 7.00039 12.396C5.9162 12.396 4.87457 12.2889 3.90683 12.0917L3.83258 12.0765C2.72738 11.8516 1.99098 11.7017 1.3564 10.8858C0.728629 10.0787 0.72898 9.14263 0.729524 7.69293L0.729524 6.30739C0.72898 4.85769 0.728629 3.92167 1.3564 3.11453C1.99098 2.29864 2.72738 2.14875 3.83258 1.9238L3.90683 1.90868ZM6.58886 4.81397C6.89134 4.94092 7.25876 5.14611 7.708 5.397C7.76714 5.43003 7.82656 5.46283 7.88605 5.49567C8.17969 5.65778 8.47505 5.82084 8.74741 6.01668C8.99217 6.19268 9.22442 6.41079 9.30129 6.72886C9.34445 6.90742 9.34445 7.09291 9.30129 7.27147C9.22442 7.58954 8.99217 7.80764 8.74741 7.98365C8.47504 8.1795 8.17966 8.34256 7.88599 8.50469C7.82652 8.53751 7.76713 8.5703 7.708 8.60332L7.70798 8.60333C7.25875 8.85422 6.89134 9.05941 6.58886 9.18635C6.09712 9.39273 5.57566 9.40943 5.14708 9.05827C4.87883 8.83849 4.78401 8.52379 4.74241 8.20666C4.64187 7.44021 4.64183 6.56043 4.74241 5.79366C4.78401 5.47653 4.87883 5.16184 5.14708 4.94205C5.57566 4.5909 6.09712 4.6076 6.58886 4.81397Z" fill="#BBBBBB"/>
                                             </svg>                                                                                                                                                                
                                          </a> 
                                       </li>
                                       <li>
                                          <a href="#">
                                             <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.90683 1.90868C4.87457 1.71141 5.9162 1.60433 7.00039 1.60433C8.08458 1.60433 9.12621 1.71141 10.0939 1.90868L10.1682 1.9238C11.2734 2.14875 12.0098 2.29864 12.6444 3.11453C13.2721 3.92167 13.2718 4.85769 13.2713 6.30738V6.30739V7.69293V7.69295C13.2718 9.14263 13.2721 10.0787 12.6444 10.8858C12.0098 11.7017 11.2734 11.8516 10.1682 12.0765L10.0939 12.0917C9.12621 12.2889 8.08458 12.396 7.00039 12.396C5.9162 12.396 4.87457 12.2889 3.90683 12.0917L3.83258 12.0765C2.72738 11.8516 1.99098 11.7017 1.3564 10.8858C0.728629 10.0787 0.72898 9.14263 0.729524 7.69293L0.729524 6.30739C0.72898 4.85769 0.728629 3.92167 1.3564 3.11453C1.99098 2.29864 2.72738 2.14875 3.83258 1.9238L3.90683 1.90868ZM6.58886 4.81397C6.89134 4.94092 7.25876 5.14611 7.708 5.397C7.76714 5.43003 7.82656 5.46283 7.88605 5.49567C8.17969 5.65778 8.47505 5.82084 8.74741 6.01668C8.99217 6.19268 9.22442 6.41079 9.30129 6.72886C9.34445 6.90742 9.34445 7.09291 9.30129 7.27147C9.22442 7.58954 8.99217 7.80764 8.74741 7.98365C8.47504 8.1795 8.17966 8.34256 7.88599 8.50469C7.82652 8.53751 7.76713 8.5703 7.708 8.60332L7.70798 8.60333C7.25875 8.85422 6.89134 9.05941 6.58886 9.18635C6.09712 9.39273 5.57566 9.40943 5.14708 9.05827C4.87883 8.83849 4.78401 8.52379 4.74241 8.20666C4.64187 7.44021 4.64183 6.56043 4.74241 5.79366C4.78401 5.47653 4.87883 5.16184 5.14708 4.94205C5.57566 4.5909 6.09712 4.6076 6.58886 4.81397Z" fill="#BBBBBB"/>
                                             </svg>                                                                                                                        
                                          </a>
                                       </li>
                                    </ul>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="footer-copyright">
                           <div class="copyright-link">
                              <ul>
                                 <li>2024-09-26 17:19:23</li>
                                 <li>(UTC+8)</li>
                              </ul>
                           </div>
                           <div class="copyright-text">
                              <p class="description">© 2025 pocketwage. All rights reserved  </p>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </footer>
            <!-- Footer area end -->

         </div>
      </div>
   </div>

   <!-- JS here -->
   <script src="../assets/js/jquery-3.7.1.min.js"></script>
   <script src="../assets/js/bootstrap.bundle.min.js"></script>
   <script src="../assets/js/iconify-icon.min.js"></script>
   <script src="../assets/js/magnific-popup.min.js"></script>
   <script src="../assets/js/select2.js"></script>
   <script src="../assets/js/swiper.min.js"></script>
   <script src="../assets/js/jquery.appear.min.js"></script>
   <script src="../assets/js/odometer.min.js"></script>
   <script src="../assets/js/chart.js"></script>

   <!-- Gsap script -->
   <script src="../assets/js/gsap/gsap.min.js"></script>
   <script src="../assets/js/gsap/ScrollSmoother.min.js"></script>
   <script src="../assets/js/gsap/ScrollToPlugin.min.js"></script>
   <script src="../assets/js/gsap/ScrollTrigger.min.js"></script>
   <script src="../assets/js/gsap/SplitText.min.js"></script>

   <script src="../assets/js/main.js"></script>
   <script src="../assets/js/error-handling.js"></script>
      <script>
         document.addEventListener("DOMContentLoaded", function () {
            const charts = document.querySelectorAll(".mini-chart");

            charts.forEach((canvas) => {
               const ctx = canvas.getContext("2d");
               const isUp = canvas.dataset.change === "up";

               new Chart(ctx, {
                  type: "line",
                  data: {
                     labels: ["1", "2", "3", "4", "5", "6"],
                     datasets: [{
                        data: isUp ? [1, 2, 2.8, 3.5, 4.2, 4.5] : [4.5, 4.0, 3.8, 3.2, 2.9, 2.5],
                        borderColor: isUp ? "#00c58e" : "#ff4d4d",
                        borderWidth: 2,
                        fill: true, // enable fill for both
                        backgroundColor: isUp
                           ? "rgba(0, 197, 142, 0.2)"   // green fill for 'up'
                           : "rgba(255, 77, 77, 0.3)",  // red fill for 'down'
                        pointRadius: 0,
                        tension: 0 // sharp lines
                     }]
                  },
                  options: {
                     responsive: false,
                     maintainAspectRatio: false,
                     scales: {
                        x: { display: false },
                        y: { display: false }
                     },
                     plugins: {
                        legend: { display: false }
                     },
                     elements: {
                        line: { borderJoinStyle: 'miter' }
                     }
                  }
               });
            });
         });
      </script>
</body>

</html>