@use '../../utils' as *;

/*----------------------------------------*/
/*  Main menu css
/*----------------------------------------*/

// Bar icon
.bar-icon {
	width: 24px;
	height: 24px;
	background: rgb(255 255 255);
	border-radius: 50%;
	@include inline-flex();
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(7, 37, 68, 0.1);

	@media #{$xs,$sm,$md,$lg} {
		width: 32px;
		height: 32px;
		@include border-radius(8px);
		background-color: var(--td-primary)
	}
}

// Header menu
.td-main-menu {
	nav {

		li {
			position: relative;
			list-style: none;
		}

		>ul {
			@include inline-flex();
			gap: 16px;

			@media #{$xl} {
				gap: 8px
			}

			@media #{$lg} {
				gap: 3px;
			}

			&>li {
				>a {
					position: relative;
					font-size: 14px;
					line-height: 1;
					padding: 22px 5px;
					display: inline-block;
					font-weight: 700;
					color: var(--td-white);
				}

				&:hover {
					>a {
						color: var(--td-primary);
					}

					>ul {
						opacity: 1;
						pointer-events: all;

					}
				}

				&:has(ul) {
					&>a {
						&::after {
							content: "\f107";
							margin-inline-start: 3px;
							position: relative;
							top: 0px;
							display: inline-block;
							width: 18px;
							height: 18px;
							--svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
							background-color: currentColor;
							-webkit-mask-image: var(--svg);
							mask-image: var(--svg);
							-webkit-mask-repeat: no-repeat;
							mask-repeat: no-repeat;
							-webkit-mask-size: 100% 100%;
							mask-size: 100% 100%;
							color: rgba($white, $alpha: 0.5);
						}
					}
				}

				&:has(li.menu-has-children) {
					.dp-menu {
						border-radius: 24px;
						&.active {
							border-radius: 24px 0 0px 24px;
						}
					}
				}
			}
		}

		ul.dp-menu {
			background-color: #171c35;
			width: 320px;
			position: absolute;
			inset-inline-start: 0px;
			opacity: 0;
			pointer-events: none;
			transition: all .5s;
			border-radius: 24px;
			display: flex;
			flex-direction: column;
			z-index: 11;

			>li {
				padding: 0px 10px;

				&:first-child {
					padding-top: 10px;
				}

				&:last-child {
					padding-bottom: 10px;
				}
			}
		}

		ul.menu-sidebar {
			position: absolute;
			right: -100%;
			background: #141931;
			padding: 25px 10px 10px;
			width: 320px;
			inset-inline-start: calc(100% + 0px);
			top: 0;
			border-radius: 0px 24px 24px 0px;
			opacity: 0;
			pointer-events: none;
			transition: all .5s;
			.box {
				.pair-list {
					height: 412px;
					overflow-y: scroll;
					scrollbar-width: none;
					&::-webkit-scrollbar {  
						display: none;  
					}
				}
			}

			.search-box {
				position: relative;
				margin-top: 15.5px;
				margin-bottom: 10px;

				.search-icon {
					position: absolute;
					top: 50%;
					left: 15px;
					transform: translateY(-50%);
					display: flex;
					align-items: center;
				}

				input {
					background: #091628;
					border-radius: 100px;
					border: 1px solid rgba(0, 0, 0, 0);
					padding: 5px 16px 5px 35px;
					height: 36px;
					color: #999;
					line-height: 16px;
					font-size: 14px;
				}
			}

			.pair-list {
				display: flex;
				row-gap: 6px;
				flex-direction: column;

				li {
					a {
						border-radius: 8px;
						padding: 9px 10px;
						display: flex;
						column-gap: 12px;
						font-size: 14px;
						font-weight: 700;

						.currency-icon {
							img {
								width: 20px;
								height: 20px;
								background-size: cover;
								border-radius: 50%;
							}
						}

						.text {
							.eth-usdt-span {
								color: var(--td-white);
							}

							.eth-usdt-span-2 {
								color: rgba(255, 255, 255, 0.6);
							}
						}

						&:hover {
							background: rgba(255, 255, 255, 0.04);
						}
					}
				}
			}
		}
	}
}


.td-main-menu {
	nav {
		>ul {
			>li {
				ul {
					li {
						&:has(ul) {
							&>a {
								::after {
									position: absolute;
									content: "\f107";
									right: 24px;
									top: 50%;
									transform: translateY(-50%);
									display: inline-block;
									width: 18px;
									height: 18px;
									--svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
									background-color: currentColor;
									-webkit-mask-image: var(--svg);
									mask-image: var(--svg);
									-webkit-mask-repeat: no-repeat;
									mask-repeat: no-repeat;
									-webkit-mask-size: 100% 100%;
									mask-size: 100% 100%;
									color: rgba($white, $alpha: 0.5);
								}
							}
						}

						&:hover {
							>ul {
								opacity: 1;
								pointer-events: all;

							}
						}
					}
				}
			}
		}
	}
}

.menu-tabs-wrapper {
	position: relative;

	.menu-tabs {
		display: flex;
		overflow-x: auto;
		white-space: nowrap;
		scrollbar-width: none;
		scroll-behavior: smooth;
		width: 100%;
		padding: 5px 0;
		align-items: center;

		&::-webkit-scrollbar {
			display: none;
		}

		&:hover {
			.scroll-left {
				opacity: 1;
			}

			.scroll-right {
				opacity: 1;
			}
		}

		.nav-tabs {
			display: inline-flex;
			flex: none;
			column-gap: 18px;

			.nav-link {
				font-size: 12px;
				line-height: 16px;
				font-weight: 700;
				background-color: rgba(0, 0, 0, 0);
				color: #999;
				display: flex;
				align-items: center;
				border-bottom: 1px solid hsla(0, 0%, 100%, .1);
				padding-bottom: 1px;
				text-transform: uppercase;

				&.active {
					border-color: var(--td-primary);
					color: var(--td-white);
				}
			}
		}

		/* Scroll Buttons */
		.scroll-left {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			background: rgba(255, 255, 255, 0.1);
			border: none;
			color: white;
			padding: 5px;
			border-radius: 5px;
			cursor: pointer;
			font-size: 14px;
			opacity: 0;
			transition: opacity 0.3s ease-in-out;
			left: -5px;
		}

		.scroll-right {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			background: rgba(255, 255, 255, 0.1);
			border: none;
			color: white;
			padding: 5px;
			border-radius: 5px;
			cursor: pointer;
			font-size: 14px;
			opacity: 0;
			transition: opacity 0.3s ease-in-out;
			right: -5px;
		}
	}
}

// menu item box
.menu-item-box {
	display: flex;
	align-items: center;
	column-gap: 16px;
	border-radius: 16px;
	padding: 12px 12px;

	&:hover {
		background: rgba(255, 255, 255, 0.04);
	}

	.icon {
		background: #2e3553;
		border-radius: 8px;
		display: flex;
		gap: 10px;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 40px;
		height: 40px;
	}

	.contents {
		h6 {
			color: #fff;
			text-align: left;
			font-size: 14px;
			font-weight: 700;

		}

		p {
			font-size: 12px;
			line-height: 13px;
			font-weight: 700;
			color: rgba($white, $alpha: 0.5);
			margin-top: 2px;
		}
	}
}

// menu icon
.menu-icon {
	width: 26px;
	height: 18px;
	position: relative;
	display: block;

	&::before {
		position: absolute;
		content: "";
		width: 100%;
		height: 1px;
		top: 0;
		inset-inline-start: 0;
		background: var(--td-white);
		transition: all 0.3s;
	}

	&::after {
		position: absolute;
		content: "";
		width: 100%;
		height: 1px;
		bottom: 0;
		inset-inline-start: 0;
		background: var(--td-white);
		transition: all 0.3s;
	}

	span {
		position: absolute;
		content: "";
		width: 18px;
		height: 1px;
		top: 50%;
		inset-inline-end: 0;
		transition: all 0.3s;
		background-color: var(--td-white);
	}
}