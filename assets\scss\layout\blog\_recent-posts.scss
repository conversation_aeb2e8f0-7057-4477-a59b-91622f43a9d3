@use '../../utils' as *;

/*----------------------------------------*/
/*  Recent post styles
/*----------------------------------------*/
.rc-post {
    padding: 12px;
    gap: 16px;
    margin-bottom: 16px;
    border: 1px solid rgba($white, $alpha: 0.1);
    @include border-radius(16px);

    @media #{$xxs} {
        gap: 12px;
    }

    &:hover {
        & .rc-post-thumb {
            img {
                @include transform(scale(1.1));
            }
        }
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.rc-post-title {
    font-size: 16px;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: box;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    white-space: normal;
    color: rgba($white, $alpha: 0.6);
    font-weight: 500;

    & a {
        &:hover {
            color: var(--td-primary);
        }
    }
}

.rc-post-thumb {
    overflow: hidden;
    flex: 0 0 auto;
    @include border-radius(16px);

    & img {
        width: 90px;
        height: 90px;
        object-fit: cover;

        @media #{$xxs} {
            width: 80px;
            height: 80px;
        }
    }
}

.rc-meta {
    margin-top: 3px;

    & span {
        font-size: 14px;

        & svg,
        & i {
            margin-inline-end: 6px;
        }

        & svg {
            @include transform(translateY(-2px));
        }

        &:hover {
            & a {
                color: var(--td-primary);
            }
        }
    }
}